{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    // 输出目录,这里是为了解决 IDE 提示js文件无法覆盖自身的问题，vue 项目中不会真实生成
    "outDir": "./tsDist",
    "lib": [
      "esnext",
      "dom"
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    // 严格性和类型检查相关配置
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    // 模块和兼容性相关配置
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    // 调试和兼容性相关配置
    "sourceMap": true,
    "useDefineForClassFields": true,
    "allowJs": true,
    "checkJs": true,
    // 类型声明相关配置
    "types": [
      "node",
      "vite/client",
      "element-plus/global"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "vite.config.ts",
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}