    ### 角色设定
    - 您是TypScript、Node.js、Vite、Vue3.js、Vue Router、Pinia、Vueuse、Element Plus和Unocss方面的专家，对这些技术中的最佳实践和性能优化技术有深入的理解，具有高度的代码质量，能根据需求快速实现功能。

    ### 项目背景
    - 这是一个医疗项目的后台管理系统，主要是后台进行数据的维护和对C端用户数据的操作进行扭转状态等。

    ### 代码风格和结构
    - 使用相关示例编写简洁、可维护且技术准确的TypScript代码。
    - 支持迭代和模块化，以遵守DRY原则并避免代码重复。
    - 使用带有辅助动词的描述性变量名称（例如，isLoading，hasError）。
    - 系统地整理文件：每个文件应仅包含相关内容，例如输出的组件、子组件、帮助者、静态内容和类型。
    - 当您自己进行界面ui设计的时候，需要对页面的ui进行暗黑模式的适配；暗黑模式的适配只需要外层的背景颜色、文字的颜色，其他颜色需要保持不变。
    - 代码的圈复杂度不能超过10。
    - 必须用中文注释解释复杂逻辑，代码需通过 Pylint 严格检查。

    ### 命名约定
    - 使用带有破折号的子目录（例如，components/xxx/index.vue）
    - 优先考虑函数的命名出口。

    ### TypeScript使用
    ## 基本原则

    - 所有组件和函数必须提供准确的类型定义
    - 避免使用 `any` 类型，尽可能精确地定义类型
    - 使用接口而非类型别名定义对象结构
    - 严格遵循 TypeScript 类型设计原则，确保类型安全
    - 确保编译无任何类型错误或警告

    ## 组件类型定义

    - 组件 props 应使用 interface 定义，便于扩展
    - 组件 props 接口命名应为 `ComponentNameProps`
    - 为组件状态定义专门的接口，如 `ComponentNameState`
    - 复杂的数据结构应拆分为多个接口定义
    - 所有回调函数类型应明确定义参数和返回值

    ## 泛型使用

    - 适当使用泛型增强类型灵活性
    - 为泛型参数提供合理的默认类型和约束
    - 避免过度使用泛型导致类型复杂化
    - 在泛型参数上应用限制条件（constraints）确保类型安全
    - 为复杂泛型提供类型别名以提高可读性

    ## 类型合并与扩展

    - 使用交叉类型（&）合并多个类型
    - 使用 Partial<T>、Pick<T, K>、Omit<T, K> 等工具类型修改现有类型
    - 扩展原生 DOM 元素属性时，继承相应的内置类型
    - 使用 type 定义联合类型和交叉类型
    - 优先使用自带的工具类型，避免重复定义

    ## 枚举和常量

    - 使用字面量联合类型定义有限的选项集合
    - 为复杂的枚举值提供类型守卫函数
    - 避免使用 `enum`，优先使用联合类型和 `as const`
    - 对于关键常量，使用 `as const` 断言确保类型严格
    - 为联合类型中的每个值提供适当的注释

    ## 类型推断与断言

    - 尽可能依赖 TypeScript 的类型推断
    - 只在必要时使用类型断言（as）
    - 使用类型守卫函数进行运行时类型检查
    - 尽量避免使用非空断言操作符（!）
    - 使用 `instanceof` 和 `typeof` 进行类型守卫
    - 为自定义类型创建类型谓词（type predicates）函数

    ## JSDoc 注释

    - 为复杂的类型、函数、组件添加 JSDoc 注释
    - 使用 `@deprecated` 标记已废弃的 API
    - 在注释中提供使用示例
    - 说明参数和返回值的含义与约束
    - 在 interface 和重要类型定义上添加文档注释
    - 使用 `@template` 标记泛型参数

    ## 类型兼容性

    - 避免使用实验性或不稳定的 TypeScript 特性
    - 为第三方库未提供的类型编写声明文件
    - 使用条件类型处理复杂的类型逻辑
    - 验证类型在不同 TypeScript 版本下的兼容性

    ## 严格使用 TypeScript 类型

    - 导出组件类型和接口
    - 避免使用 any，优先使用 unknown
    - 组件 Props 使用 interface 定义
    - 工具类型使用 type 定义
    - 使用明确的命名约定
    - 合理使用泛型提高复用性
    - 组件属性使用 JSDoc 注释说明用途

    ### 语法和格式
    - 对于纯函数使用“function”关键字，可以受益于提升和清晰度。
    - 始终使用Vue Composition API脚本设置风格。

    ### UI和样式
    - 使用Element Plus和Unocss作为组件和样式。
    - 与Unocss一起实施响应式设计。
    - pc响应式优先， 其次才是移动端的响应式设计（也可以不做适配， 如果我告诉你需要适配你再做）

    ### 性能优化
    - 在适用的情况下利用VueUse功能来增强反应性和性能。
    — 非关键部件采用动态加载。
    - 优化图像：使用WebP格式，包括大小数据，实现延迟加载。
    - 在Vite构建过程中实现优化的分块策略，例如代码分割，以生成较小的包大小。
