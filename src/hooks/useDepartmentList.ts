import Passport_Api from "@/api/passport";
import { type DepartmentInputDTO } from "@/api/passport/types";

type DepartmentOption = Pick<BaseDepartment, "Id" | "Name">;

export default (options?: { hasAll?: boolean; allOption?: DepartmentOption }) => {
  /** 是否包含全部科室 默认包含 */
  const hasAll = options?.hasAll ?? true;
  /** 全部科室选项 可传入自定义选项*/
  const allOption = options?.allOption ?? { Id: "*", Name: "全部科室" };
  /** 科室列表 */
  const baseDepartmentList = ref<DepartmentOption[]>([]);

  /** 加载科室列表 */
  const loadDepartmentList = async (
    orgId: string,
    params?: Omit<Partial<DepartmentInputDTO>, "OrgId" | "Pageable" | "SingleOne">
  ) => {
    let data: DepartmentInputDTO = {
      OrgId: orgId,
      DtoTypeName: "DepartmentOutputDto1",
      Pageable: false,
      IsEnabled: true,
      IsLocked: false,
      SingleOne: false,
    };
    if (params) {
      data = { ...data, ...params };
    }

    const res = await Passport_Api.getDeptReadList(data);
    if (res.Type === 200) {
      baseDepartmentList.value.push(...res.Data);
    }
  };

  /** 重置科室列表 */
  const resetDepartmentList = () => {
    if (hasAll) {
      baseDepartmentList.value = [allOption];
    } else {
      baseDepartmentList.value = [];
    }
  };

  resetDepartmentList();

  return {
    baseDepartmentList,
    loadDepartmentList,
    resetDepartmentList,
  };
};
