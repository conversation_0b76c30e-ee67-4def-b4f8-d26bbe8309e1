import { type TableInstance } from "element-plus";
import { ref, nextTick, watch } from "vue";
import dayjs from "dayjs";
import { type TableColumnCtx } from "element-plus";

/** 获取表格的基本配置 */
export const useTableConfig = <T>(padding: number = 10) => {
  const kTableRef = "tableRef";

  const tableLoading = ref<boolean>(false);
  const pageData = ref<T[]>([]);
  const total = ref<number>(0);
  const tableRef = useTemplateRef<TableInstance>(kTableRef);
  const selectedTableIds = ref<string[]>([]);
  const exportLoading = ref<boolean>(false);

  /**表格高度 */
  const tableFluidHeight = ref<number>(0);

  /** 选择表格 */
  const handleSelectionChange = <T extends Record<string, any>, K extends keyof T>(
    selection: T[],
    key: K & string
  ) => {
    selectedTableIds.value = selection.map((item) => item[key]);
  };

  /** 将表格滚动到最上方 */
  const scrollToTop = () => {
    nextTick(() => {
      if (tableRef.value && tableRef.value.$el) {
        const tableEl = tableRef.value.$el.querySelector(".el-scrollbar__wrap");
        if (tableEl) {
          tableEl.scrollTo({
            top: 0,
            behavior: "smooth",
            duration: 10,
          });
        }
      }
    });
  };

  /**
   * 表格中时间格式化，格式化后默认为YYYY-MM-DD HH:mm:ss
   *
   * @param row 行数据
   * @param column 列配置
   *
   * @returns 格式化后的时间
   */
  function tableDateFormat(
    row: any,
    column: TableColumnCtx<any>,
    cellValue: any,
    index: number,
    format: string = "YYYY-MM-DD HH:mm:ss"
  ): VNode | string {
    const date = row[column.property];
    if (!date) {
      return "";
    }
    return dayjs(date).format(format);
  }

  /**
   * 表格中时间格式化，格式化后为YYYY-MM-DD
   *
   * @param row 行数据
   * @param column 列配置
   *
   * @returns 格式化后的时间
   */
  function tableDateFormatDay(row: any, column: TableColumnCtx<any>): VNode | string {
    const date = row[column.property];
    if (!date) {
      return "";
    }
    return dayjs(date).format("YYYY-MM-DD");
  }

  const tableResize = (rect: DOMRectReadOnly) => {
    tableFluidHeight.value = rect.height - padding * 2;
  };

  // 监听 pageData 变化，自动滚动到顶部
  watch(pageData, () => {
    scrollToTop();
  });

  return {
    kTableRef,
    tableLoading,
    exportLoading,
    pageData,
    total,
    tableRef,
    selectedTableIds,
    tableFluidHeight,
    tableResize,
    handleSelectionChange,
    scrollToTop,
    tableDateFormat,
    tableDateFormatDay,
  };
};
