import dayjs from "dayjs";
export const useDateRangePicker = () => {
  const datePickerShortcuts = [
    {
      text: "本月",
      value() {
        const end = dayjs().format("YYYY-MM-DD 00:00:00");
        const start = dayjs().format("YYYY-MM-01 00:00:00");
        return [start, end];
      },
    },
    {
      text: "本年",
      value() {
        const end = dayjs().format("YYYY-MM-DD 00:00:00");
        const start = dayjs().startOf("year").format("YYYY-MM-DD 00:00:00");
        return [start, end];
      },
    },
    {
      text: "近一个月",
      value() {
        const end = dayjs().format("YYYY-MM-DD 00:00:00");
        const start = dayjs().subtract(1, "months").format("YYYY-MM-DD 00:00:00");
        return [start, end];
      },
    },
    {
      text: "近三个月",
      value() {
        const end = dayjs().format("YYYY-MM-DD 00:00:00");
        const start = dayjs().subtract(3, "months").format("YYYY-MM-DD 00:00:00");
        return [start, end];
      },
    },
    {
      text: "近半年",
      value() {
        const end = dayjs().format("YYYY-MM-DD 00:00:00");
        const start = dayjs().subtract(6, "months").format("YYYY-MM-DD 00:00:00");
        return [start, end];
      },
    },
    {
      text: "近一年",
      value() {
        const end = dayjs().format("YYYY-MM-DD 00:00:00");
        const start = dayjs().subtract(12, "months").format("YYYY-MM-DD 00:00:00");
        return [start, end];
      },
    },
  ];

  return { datePickerShortcuts };
};
