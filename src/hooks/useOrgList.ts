import Passport_Api from "@/api/passport";
import { type OrganizationListInputDTO } from "@/api/passport/types";

type OrgOption = RequireKeys<BaseOrganization, "Id" | "Name">;

export default (options?: { hasAll?: boolean; allOption?: OrgOption }) => {
  /** 是否包含全部机构 */
  const hasAll = options?.hasAll ?? true;
  /** 全部机构选项 */
  const allOption = options?.allOption ?? { Id: "*", Name: "全部机构" };

  /** 机构列表 */
  const baseOrganizationList = ref<OrgOption[]>([]);

  /** 加载机构列表 */
  const loadOrgList = async (params?: OrganizationListInputDTO) => {
    let data = {
      IsEnabled: true,
      Keyword: "",
      DtoTypeName: "QueryOrgDtoForDropDownList", // QueryOrgDetailOutputDto1
      PageIndex: 1,
      PageSize: 20,
      Pageable: true,
      Scopeable: false,
      IsTreatment: null,
    };
    if (params) {
      data = { ...data, ...params };
    }

    const res = await Passport_Api.getOrganizationList(data);
    if (res.Type === 200) {
      baseOrganizationList.value.push(
        ...res.Data.Rows.map((item) => ({
          ...item,
          Id: item.Id ?? "",
          Name: item.Name ?? "",
        }))
      );
    }
  };

  /** 重置机构列表 */
  const resetOrgList = () => {
    if (hasAll) {
      baseOrganizationList.value = [allOption];
    } else {
      baseOrganizationList.value = [];
    }
  };

  resetOrgList();

  return {
    baseOrganizationList,
    loadOrgList,
    resetOrgList,
  };
};
