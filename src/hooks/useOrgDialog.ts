export default () => {
  /** 选择机构弹窗 */
  const showOrgDialog = ref(false);

  /** 选择的机构id */
  const selectedOrgIds = ref<string[]>([]);

  /** 是否正在请求 */
  const orgDialogLoading = ref(false);

  /** 机构选项发生变化 */
  function handleOrgSelectionChange(selection: string[]) {
    selectedOrgIds.value = selection;
  }

  return {
    showOrgDialog,
    selectedOrgIds,
    orgDialogLoading,
    handleOrgSelectionChange,
  };
};
