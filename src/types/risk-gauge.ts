declare global {
  interface RiskWarningGauge {
    /** 更新量表id */
    ObjectId?: string;
    Id?: string;
    Name?: string;
    OrgId?: string;
    OrgName?: string;
    UserId?: string;
    UpdaterId?: string;
    UpdatedTime?: string;
    CreatorId?: string;
    CreatedTime?: string;
    DeleterId?: string;
    DeletedTime?: string;
    /** 量表开始填写时间 */
    StartDate?: string;
    /** 量表填写完成提交时间 */
    EndDate?: string;
    /**量表基础数据id*/
    BaseGaugeId?: string;
    ReferenceId?: string;
    ReportId?: string;
    /** 结果 */
    Result?: RiskWarningReportDetail[];
    Sign?: number;
    StandardRule?: number;
    /**问题*/
    Questions?: RiskWarningGaugeQuestion[];
  }

  interface RiskWarningGaugeQuestion {
    /** 问题key */
    Key?: number;
    /** 问题计算规则，对应rules中的key */
    Rule?: string;
    /**
     * 针对本题的结果
     *
     * 内容：[RiskWarningResult] 结构JSON字符串
     */
    Result?: string;
    /** 问题描述 */
    Text?: string;
    /** 是否必填 */
    Required?: boolean;
    /** 问题是否分组, null表示不分组，其他值为分组名 */
    Group?: string[];
    /** 问题类型（单选0/多选1/文本2/数值3） */
    Type?: number;

    /**
     * 记录的上次提交的答案
     *
     * 单选题，Type == 0 时，[Options.Value]
     * 多选题，Type == 1 时，[Options.Value, ...]转字符串
     * 文本、数值题，Type == 2 || 3 时，输入的值
     *
     */
    Answer?: string;
    /**
     * 默认选项值，JSON字符串
     * 结构同 [Answer]
     */
    Default?: string;
    /**
     * 选项
     *
     * 仅在 Type == 0 || 1 时有值
     * Type == 2 || 3 时为null
     */
    Options?: RiskWarningQuestionOption[];

    /**
     * 多选题，Type == 1 时
     * 根据选项跳转指定题目，如果选项都不符合，则直接下一题
     */
    Jumps?: RiskWarningQuestionJump[];

    /**
     * 文本数值题，Type == 2 || 3 时
     * 根据选项跳转指定题目，如果选项都不符合，则直接下一题
     */
    Conditions?: RiskWarningQuestionCondition[];

    /**
     * 扩展属性，可自定义；例如人体图的map数据，选项分2列
     */
    Ext?: RiskWarningQuestionExt;

    /**
     * 当list不为空时需要按list内的数值以及unit构造options
     * 针对选项题
     *
     * 例如 list=[6,8,1] unit="cmH2O"
     * 则需要以 lits[0]为起点,list[1]为终点,list[2]为间隔构造
     * 结果：
     * [
     *   {
     *     "value": 6,
     *     "text": "6cmH2O",
     *   },
     *   {
     *     "value": 7,
     *     "text": "7cmH2O",
     *   },
     *   {
     *     "value": 8,
     *     "text": "8cmH2O",
     *   }
     * ],
     */
    List?: number[];

    /**
     * 选项单位
     */
    Unit?: string;
  }

  interface RiskWarningQuestionOption {
    /** 选项值 */
    Value?: number;
    /** 选项描述 */
    Text?: string;

    /**
     * 单选题情况下 Type == 0，跳转下一题Key
     * null 表示 questions 中的下一个
     */
    Jump?: number;

    /**
     * 是否完成，是否是最后一题
     */
    IsDone?: boolean;
  }

  // 多选跳转规则
  interface RiskWarningQuestionJump {
    /** 选中选项值，多选 */
    Values?: number[];

    /**
     * 跳转指定题Key
     * null表示下一题
     */
    Jump?: number;
  }

  // 数值题跳转规则
  interface RiskWarningQuestionCondition {
    /**
     * 匹配值范围
     *
     * 左开右闭，null，表示无穷小/大
     * eg: 如[1,2]表示1<x<=2，[1]、[1,null]表示 >1, [null,2]表示<=2
     */
    Range?: (number | undefined)[];

    /**
     * 跳转指定题Key
     * null表示下一题
     */
    Jump?: number;
  }

  interface RiskWarningQuestionExt {
    // /**
    //  * 图表，例如人体图（HumanBody）
    //  */
    // Diagram?: string;

    // /**
    //  * 在计算的时候排除的选项
    //  */
    // Excludes?: number[];

    /**
     * 报告中显示的名字
     */
    Name?: string;

    /**
     * 报告中的排序
     */
    Sort?: number;
  }

  interface RiskWarningReport {
    Sign?: number;
    /**
     * sign == 1/4/5/6，则表示量表结论（直接取第一个）
     * sign == 2/3/7，则表示每道题的结论
     */
    Report?: RiskWarningReportDetail[];
  }

  interface RiskWarningReportDetail {
    Name?: string;
    /**
     * 标题
     */
    ShowName?: string;
    /**
     * 量表建议
     * sign == 1/4/5/6，显示
     */
    ShowTips?: string;
    /**
     * 量表结论
     * sign == 1/4/5/6，显示
     */
    Conclusion?: string;
    /**
     * 是否存在问题
     * 针对认知障碍
     */
    ExistProblem?: boolean;
    /**
     * 显示顺序
     */
    Sort?: number;
    /**
     * sign == 1/4/5/6，则表示量表结论（直接取第一个）
     * sign == 2/3/7，则表示每道题的每个选项结论
     */
    Report?: RiskWarningReportDetailReport[];
  }

  interface RiskWarningReportDetailReport {
    /**
     * 评估结果
     * sign == 1/4/5/6，分数
     * sign == 2/3/7，结论
     */
    Result?: string;
    /**
     * 评估建议
     * sign == 2/3，展示建议
     */
    Suggest?: string;
  }
}

export {};
