declare global {
  interface ConsultationOrder {
    OrderNo?: string;
    CreatedTime?: string;
    PayTime?: string;
    VistDate?: string;
    WeChatTradeNo?: string;
    PatientInfo?: string;
    ConsultWay?: string;
    OrganizationName?: string;
    DoctorName?: string;
    Amount?: string;
    AssistantName?: string;
    State?: string;
    IsTest?: string;
  }

  interface VisitOutputDTO {
    // 结算记录
    CostCalcs?: VisitCostCalc[];
    // 费用清单记录
    CostDetails?: VisitCostDetail[];
    // 预缴记录
    CostPays?: VisitCostPay[];
    VisitDatumImgs?: string[];
    // 诊断记录(可能包含一个主诊断和多个其他诊断、症状描述)
    VisitDiagnoses?: VisitDiagnosis[];
    VisitReportDetails?: VisitReportDetail[];
    // 各种图文报告
    VisitReports?: VisitReport[];
    VisitTreatmentTeams?: VisitTreatmentTeamMember[];
    // 访问信息
    Vist?: VisitInfo;
  }

  interface VisitCostCalc {
    Key?: string;
    Value?: string;
    VisitId?: string;
    Id?: string;
  }

  interface VisitCostDetail {
    Item?: string;
    Price?: number;
    Number?: number;
    Amount?: number;
    VisitId?: string;
    CreatedTime?: string;
    Id?: string;
  }

  interface VisitCostPay {
    VisitId?: string;
    PayWay?: string;
    Amount?: number;
    State?: string;
    CreatedTime?: string;
    Id?: string;
  }

  interface VisitInfo {
    Id?: string;
    OthersId?: string;
    PatientId?: string;
    ConsultId?: string;
    Name?: string;
    Age?: string;
    Sex?: string;
    BirthDay?: string;
    VisitNo?: string;
    Source?: string;
    DepartmentName?: string;
    DepartmentId?: string;
    BedNo?: string;
    DoctorName?: string;
    DoctorId?: string;
    NurseId?: string;
    NurseName?: string;
    InDate?: string;
    OutDate?: string;
    ChiefComplaint?: string;
    Complain?: string;
    PresentHistory?: string;
    PresentIllness?: string;
    PastHistory?: string;
    HistoryIllness?: string;
    Allergies?: string;
    IsAllergy?: boolean;
    AllergyDrug?: string;
    PatientNo?: string;
    CardNumber?: string;
    OrganizationId?: string;
    OrganizationName?: string;
    CreatorId?: string;
    CreatedTime?: string;
    IsSelfBuild?: boolean;
    AuxiliaryDiagnosis?: string;
    TreatmentTime?: string;
    Department?: string;
    Disposal?: string;
    Address?: string;
    Marital?: string;
    Give?: string;
    Nation?: string;
    Professional?: string;
    WorkUnit?: string;
    ReportUrls?: string[];
    VisitDiagnoses?: VisitDiagnosis[];
  }

  interface VisitDiagnosis extends BaseDiagnosis {
    OthersId?: string;
    Source?: number;
    VisitId?: string;
    ConsultId?: string;
    MedicalId?: string;
    CreatedTime?: string;
    OperatorTime?: string;
    DeleteTime?: string;
    Id?: string;
  }

  interface VisitReportDetail {
    Id?: string;
    OriginalReportUrl?: string;

    // 病历需要用到，不然报告找不到图片
    VisitReportId?: string;

    // 报告图片地址
    ReportUrl?: string;
    Source?: number;
    CreatorId?: string;
    // 0:同步，1：医生，2：患者
    Source?: number;
  }

  interface VisitReport {
    CreatedTime?: string;
    DeleteTime?: string;
    Id?: string;
    ItemNames?: string;
    OperatorTime?: string;
    OthersId?: string;
    ReportDate?: string;
    ReportName?: string;
    ReportType?: number;
    ReportTypeName?: string;
    Source?: number;
    VisitId?: string;
  }

  interface VisitTreatmentTeamMember {
    Id?: string;
    VisitId?: string;
    OthersId?: string;
    DoctorId?: string;
    DoctorTypeName?: string;
    Name?: string;
    Code?: string;
    CreatedTime?: string;
    OperatorTime?: string;
    DeleteTime?: string;
    testTime?: string;
  }

  interface FollowUpPlan {
    /** 随访明细 */
    Details?: FollowUpDetail[];
    /** 模板中随访计划 */
    TempDetail?: FollowUpDetail[];

    PlanId?: number;
    ShowPlanId?: string;
    Id?: number;
    ShowId?: string;
    FollowUpPlanId?: string;
    ShowFollowUpPlanId?: string;

    Name?: string;
    PatId?: string;
    PatName?: string;
    Sex?: string;
    Age?: number;
    PatNo?: string;
    ExecName?: string;
    FollowUpExtendId?: string;
    WaitDay?: number;

    /**
     * 计划类型（1:电话随访 2:在线随访 3:来院随访）
     */
    Type?: number;
    PlanType?: number;
    /**
     * 随访状态（1:未发送 2:已发送 3:已完成 4:未完成）
     */
    State?: number;
    Sort?: number;
    OrgId?: string;
    OrgName?: string;

    CreatorId?: string;
    CreatorName?: string;
    CreatedTime?: string;
    OperaTime?: string;
    OperaUserId?: string;
    OperaUserName?: string;
    StartTime?: string;
    CompleteTime?: string;

    RelatedId?: string;
    /** 所有明细的名字(多个用逗号拼接) */
    RelatedName?: string;
    BusinessId?: string;
    /** 量表名称(多个用逗号拼接) */
    GaugeNames?: string;
    GaugePoint?: string;
    /** 宣教名称(多个用逗号拼接) */
    RecoveryMissionNames?: string;
    RecoveryImgUrl?: string;
    /** 问卷名称(多个用逗号拼接) */
    QuestionnaireNames?: string;
    IsTemp?: boolean;
    Remark?: string;
  }

  interface FollowUpDetail {
    Id?: string;
    ShowId?: string;
    FollowUpPlanId?: string;
    ShowFollowUpPlanId?: string;

    /** 关联ID，选择中量表/问卷/宣教的基础ID */
    RelatedId?: string;

    /** 关联名称 */
    RelatedName?: string;
    BusinessId?: string;

    /** 操作数据的时间（null：未读/未填写/未评估  非null：已读/已填写/已评估） */
    OperaTime?: string;

    /** 宣教/问卷/量表 操作人id */
    OperaUserId?: string;

    /** 类型（1：量表  2：宣教  3：问卷） */
    Type?: number;

    /**
     * 量表分数
     * [type] == 1，且 [operaTime] != null，才会有值
     */
    GaugePoint?: string;

    /**
     * 宣教图片
     * [type] == 2，才会有值
     */
    RecoveryImgUrl?: string;

    /** 操作状态（0：未操作  1：已操作  2：已过期） */
    State?: number;

    /** 创建时间 */
    CreatedTime?: string;
  }

  // 就诊财务结算报表-列表项
  interface VisitSettlementReport {
    tradeType?: string;
    orderNo?: string;
    orderCreatedTime?: string;
    optTime?: string;
    TradeNo?: string;
    Amount?: string;
    organizationName?: string;
    doctorName?: string;
    patientInfo?: string;
    assistantName?: string;
    isTest?: string;
  }

  // 治疗财务结算报表-列表项
  interface TreatmentSettlementReport {
    tradeType?: string;
    orderNo?: string;
    orderCreatedTime?: string;
    optTime?: string;
    tradeAlias?: string;
    Remark?: string;
    cityName?: string;
    organizationName?: string;
    assistantName?: string;
    patientInfo?: string;
    treatmentName?: string;
    treatmentUnitPrice?: string;
    chargeUnit?: string;
    treatmentItemsCount?: string;
    Amount?: string;
    state?: string;
    hasSettle?: string;
    doctorName?: string;
    doctorPhoneNumber?: string;
    doctorSettleRatio?: string;
    doctorSettleAmount?: string;
    doctorBankCardNo?: string;
    doctorBankName?: string;
    doctorSubBranch?: string;
    therapistName?: string;
    therapistNumber?: string;
    therapistSettleRatio?: string;
    therapistSettleAmount?: string;
    therapistBankCardNo?: string;
    therapistBankName?: string;
    therapistSubBranch?: string;
    nurseName?: string;
    nursePhoneNumber?: string;
    nurseSettleRatio?: string;
    nurseSettleAmount?: string;
    nurseBankCardNo?: string;
    nurseBankName?: string;
    nurseSubBranch?: string;
    MarketingName?: string;
    MarketingSettleRatio?: string;
    MarketingSettleAmount?: string;
    AssistantName?: string;
    AssistantSettleRatio?: string;
    AssistantSettleAmount?: string;
    AssistantManagerName?: string;
    AssistantManagerSettleRatio?: string;
    AssistantManagerSettleAmount?: string;
    OnlineGuidanceName?: string;
    OnlineGuidanceSettleRatio?: string;
    OnlineGuidanceSettleAmount?: string;
    DealerName?: string;
    DealerSettleRatio?: string;
    DealerSettleAmount?: string;
    ReferrerName?: string;
    ReferrerSettleRatio?: string;
    ReferrerSettleAmount?: string;
    OtherName?: string;
    OtherSettleRatio?: string;
    OtherSettleAmount?: string;
    nurseChiefName?: string;
    nurseChiefPhoneNumber?: string;
    nurseChiefSettleRatio?: string;
    nurseChiefSettleAmount?: string;
    nurseChiefBankCardNo?: string;
    nurseChiefBankName?: string;
    nurseChiefSubBranch?: string;
    deptDirectorName?: string;
    deptDirectorPhoneNumber?: string;
    deptDirectorSettleRatio?: string;
    deptDirectorSettleAmount?: string;
    deptDirectorBankCardNo?: string;
    deptDirectorBankName?: string;
    deptDirectorSubBranch?: string;
    inspectorName?: string;
    inspectorPhoneNumber?: string;
    inspectorSettleRatio?: string;
    inspectorSettleAmount?: string;
    inspectorBankCardNo?: string;
    inspectorBankName?: string;
    inspectorSubBranch?: string;
    hospitalDirectorName?: string;
    hospitalDirectorPhoneNumber?: string;
    hospitalDirectorSettleRatio?: string;
    hospitalDirectorSettleAmount?: string;
    hospitalDirectorBankCardNo?: string;
    hospitalDirectorBankName?: string;
    hospitalDirectorSubBranch?: string;
    isTest?: string;
    /** 医生推荐 */
    recommendedDoctorName?: string;
    /** 医生推荐手机号 */
    recommendedDoctorPhoneNumber?: string;
    /** 普通用户推荐 */
    recommendedNormalUserName?: string;
    /** 普通用户推荐手机号 */
    recommendedNormalUserPhoneNumber?: string;
    /** 方案销售 */
    prescriptionSellerName?: string;
    /** 方案销售手机号 */
    prescriptionSellerPhoneNumber?: string;
  }

  // 治疗财务结算报表-统计项
  interface TreatmentSettlementReportStatistics {
    freePrescriptionCount?: string;
    payPrescriptionCount?: string;
    totalPayAmount?: string;
    totalRefundAmount?: string;
    creatorAmount?: string;
    guideAmount?: string;
  }

  // 就诊记录
  interface ConsultRecord {
    Id?: string;
    UserId?: string;
    UserName?: string;
    Sex?: string;
    Age?: string;
    PhoneNumber?: string;
    HeadImg?: string;
    DocUserId?: string;
    DocUserName?: string;
    Organization?: string;
    Complain?: string;
    DiagnoseName?: string;
    OrganizationName?: string;
    DepartmentId?: string;
    DepartmentName?: string;
    CostState?: number;
    State?: number;
    ConsultDate?: string;
    CreateDate?: string;
    CompletedTime?: string;
    Describing?: string;
    IsHospital?: boolean;
    VistDate?: string;
    VisitDate?: string;
    HospitName?: string;
    HospitDepartmentName?: string;
    OfflineDate?: string;
    OfflineDiagnosis?: string;
    RoomId?: string;
    Dialogue?: string;
    WaitHours?: number;
    IsEva?: boolean;
    DocAssistantId?: string;
    DocAssistantName?: string;
    PreState?: number;
    PreReviewState?: number;
    ConsultWay?: number;
    IsTest?: boolean;
    Source?: number;
    CreatorId?: string;
    TotalAmount?: number;
  }

  interface ConsultRecordInfo {
    Assists?: unknown[];
    Consult?: ConsultRecord;
    Diagnoses?: VisitDiagnosis[];
    Disposal?: string;
    Doctor?: BaseDoctor;
    Medical?: VisitInfo;
    Order?: TreatOrder;
    RxDetails?: BaseRxMoItem[];
    RxUrl?: string;
    Urls?: {
      Url?: string;
    }[];
  }

  interface TreatOrder {
    Status: number;
    OrderNo: string;
    Price: number;
    PayTime: string; // ISO 8601
    PayType: number;
    RefundPrice?: number;
    DeliveryTime?: string; // ISO 8601
    CancelTime?: string; // ISO 8601
    CollectTime: string; // ISO 8601
    OrderExpresses: OrderExpress[];
    IsPayCompleted: boolean;
    CreatedTime: string; // ISO 8601
    OrderDetails: OrderDetailItem[];
    OrderAddresss: OrderAddress[]; // 注意这里有三个s
    Payment: {
      Id: string;
      PaymentName: string;
      Provider: string;
      PayAlias: string;
    };
  }
}

export {};
