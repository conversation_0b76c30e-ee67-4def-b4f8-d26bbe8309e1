declare global {
  interface BaseGauge {
    Id?: string;
    Code?: string;
    Name?: string;
    Remark?: string;
    IsEnble?: boolean; // 注意：这里的属性名可能是一个拼写错误，应该是 IsEnable
    IsDefaultPush?: boolean;
    CreatedTime?: string;
    CreatorId?: string;
    OrganizationId?: string;
    OrganizationName?: string;
    Reference?: string;
    Type?: number;
    Types?: string;
  }

  interface DctPatGauge {
    PatName?: string; // 患者姓名，允许为 null
    Sex?: string; // 性别，允许为 null
    Age?: number; // 年龄，允许为 null
    DeptName?: string | null; // 科室名称，允许为 null
    OrgName?: string | null; // 组织名称，允许为 null
    PatGaugeDiseaseRelations?: GaugeDiseaseRelation[]; // 疾病关系数组，根据实际情况定义
    PatGaugeProblems?: GaugeProblem[]; // 问题数组
    PatGaugeResults?: GaugeResult[]; // 结果数组
    Id?: string; // ID
    Remark?: string; // 备注
    Code?: string; // 代码
    RelatedId?: string; // 相关 ID
    Source?: number; // 来源
    IsDoctor?: boolean; // 是否为医生
    PatientId?: string; // 患者 ID
    SumPoint?: number; // 总分
    BaseEvaluateGaugeId?: string; // 基础评估量表 ID
    SuccessResultContent?: string; // 成功结果内容
    CreatedTime?: string; // 创建时间
    Name?: string; // 名称
    CreatorId?: string; // 创建者 ID
    DctSendSign?: string; // 医生发送标识
    Type?: number; // 类型
    GaugeSchemeId?: string; // 量表方案 ID，允许为 null
    IsEvaluate?: boolean; // 是否评估
    ExecName?: string; // 执行人
  }

  interface GaugeDetail extends BaseGauge {
    GaugeDiseaseRelations?: GaugeDiseaseRelation[];
    GaugeProblems?: GaugeProblem[];
    GaugeResults?: GaugeResult[];
  }

  interface GaugeDiseaseRelation {
    Id?: string;
    EvaluateGaugeId?: string;
    DiseaseId?: string;
    CreatedTime?: string;
    DeletedTime?: string;
  }

  interface GaugeProblem {
    Id?: string;
    EvaluateGaugeId?: string;
    PatEvaluateGaugeId?: string;
    IsRequired?: boolean;
    /**
     * 题型（1：单选 2：多选 3：问答 4：填分值）
     */
    ProblemType?: number;
    Sort?: number;
    Title?: string;
    GaugeProblemDetails?: GaugeProblemDetail[];
    PatGaugeProblemDetails?: GaugeProblemDetail[];
  }

  interface GaugeProblemDetail {
    Id?: string;
    ProlemId?: string;
    ProblemOption?: string;
    Answer?: string;
    Points?: number;
    Sort?: number;
  }

  interface GaugeResult {
    Id?: string;
    EvaluateGaugeId?: string;
    StartPoint?: number;
    EndPoint?: number;
    Content?: string;
  }
}

export {};
