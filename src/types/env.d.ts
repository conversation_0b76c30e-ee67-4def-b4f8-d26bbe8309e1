// https://cn.vitejs.dev/guide/env-and-mode

// TypeScript 类型提示都为 string： https://github.com/vitejs/vite/issues/6930
interface ImportMetaEnv {
  /** API 地址 */
  VITE_APP_API_URL: string;
  /** 上传文件地址 */
  VITE_APP_UPLOAD_URL: string;
  /** singnar 地址 */
  VITE_APP_WS_URL: string;
  /** 旧版源 */
  VITE_APP_OLD_ORIGIN: string;
  /** 客户端ID */
  VITE_APP_CLIENT_ID: string;
  /** 客户端秘钥 */
  VITE_APP_CLIENT_SECRET: string;
  VITE_DEV_HOST: string;
  VITE_DEV_PORT: number;
  /** 本地调试旧版源 */
  VITE_DEV_OLD_ORIGIN: string;
  VITE_PROXY_API: string;
  VITE_PROXY_WS: string;
  VITE_PROXY_OSS: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

/**
 * 平台的名称、版本、运行所需的`node`版本、依赖、构建时间的类型提示
 */
declare const __APP_INFO__: {
  pkg: {
    name: string;
    version: string;
    engines: {
      node: string;
    };
    dependencies: Record<string, string>;
    devDependencies: Record<string, string>;
  };
  buildTimestamp: number;
};
