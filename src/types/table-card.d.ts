declare global {
  interface TableCardDetail {
    Id?: string;

    /**
     * 是否申领（用户是否申请邮寄）
     * 支付完成，就是申领了
     */
    Application?: boolean;
    AssistantId?: string;
    AssistantName?: string;

    /**
     * 存放图片urls的json数组，可解析为TableCardImage[]
     * 格式：[{"Type":2,"Url":"https://kfxoss-biz-dev.oss-cn-hangzhou.aliyuncs.com/7224428389878533120.png"}]
     */
    CardImgs?: string;

    /**
     * 创建来源（医生端=0 医助=1）
     */
    CardSource?: number;
    Creator?: string;
    CreatedTime?: string;
    CreatorName?: string;
    DeletedTime?: string;

    /** 是否下载（用户是否保存至相册） */
    Download?: boolean;
    DeptId?: string;
    DeptName?: string;
    OrderAddresss?: OrderAddress[];
    OrderExpresses?: OrderExpress[];
    OrderNo?: string;
    OrderState?: number;
    OrgId?: string;
    OrgName?: string;
    UserId?: string;
    UserName?: string;
  }

  interface TableCardImage {
    Type?: number;
    Url?: string;
  }

  // 订单快递
  interface OrderExpress {
    OrderNo?: string;
    ExpressNum?: string;
    ExpressComCode?: string;
    ExpressComName?: string;
    Remark?: string;
    /**
     * 物流订单类型（0=发货  1=退货）
     */
    Type?: number;
  }

  // 快递信息
  interface ExpressInfo {
    IsCheck?: boolean;
    Message?: string;
    CompanyCode?: string;

    /** 快递公司 */
    Company?: string;

    /** 快递单号 */
    ExpressNum?: string;

    /** 快递轨迹详情 */
    Track?: ExpressTrack[];
    RouteInfo?: ExpressRouteInfo;
  }

  interface ExpressTrack {
    Time?: string;
    FTime?: string;

    /** 详细内容 */
    Context?: string;

    /** 当前轨迹所在地编码 */
    AreaCode?: string;

    /** 当前轨迹所在地 */
    AreaName?: string;

    /** 当前轨迹状态 */
    Status?: string;

    /** 定位城市 */
    Location?: string;

    /** 城市经纬度 */
    AreaCenter?: string;

    AreaPinYin?: string;
    StatusCode?: string;
  }

  interface ExpressRouteInfo {
    /** 出发地 */
    From?: ExpressRouteInfoDetail;
    /** 当前地 */
    Cur?: ExpressRouteInfoDetail;
    /** 到达地 */
    To?: ExpressRouteInfoDetail;
  }

  interface ExpressRouteInfoDetail {
    Number?: string;
    Name?: string;
  }
}

export {};
