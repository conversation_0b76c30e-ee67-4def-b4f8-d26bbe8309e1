declare global {
  // 接口响应数据
  interface ServerResult<T = unknown> {
    Type: number;
    Content?: string;
    Data: T;
  }
  interface ListDataTotalCount<T = unknown> {
    Data: T[];
    TotalCount: number;
    PageIndex?: number;
    PageSize?: number;
  }
  interface ListRowsTotal<T = unknown> {
    Rows: T[];
    Total: number;
  }
  interface ListRowsTotalCount<T> {
    Rows: T[];
    TotalCount: number;
  }
  interface ListRowTotal<T = unknown> {
    Row: T[];
    Total: number;
  }
  interface ListDataTotal<T = unknown> {
    Data: T[];
    Total: number;
    PageIndex?: number;
    PageSize?: number;
  }
  interface ListDataTotalCount<T = unknown> {
    Data: T[];
    TotalCount: number;
    PageIndex?: number;
    PageSize?: number;
  }

  interface PageParams {
    PageIndex: number;
    PageSize: number;
  }
}
export {};
