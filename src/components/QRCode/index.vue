<template>
  <qrcode-vue
    :id="canvasId"
    :value="value"
    :level="level"
    :render-as="renderAs"
    :background="background"
    :foreground="foreground"
    :gradient="gradient"
    :gradient-type="gradientType"
    :gradient-start-color="gradientStartColor"
    :gradient-end-color="gradientEndColor"
    :image-settings="imageSettings"
    :size="size"
    :margin="2"
  />
</template>

<script setup lang="ts">
import FileAPI from "@/api/file";
import { base64ToFile, generateUUID } from "@/utils";
import QrcodeVue from "qrcode.vue";
import type { Level, RenderAs, GradientType, ImageSettings } from "qrcode.vue";

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

interface Props {
  value: string;
  size?: number;
  level?: Level;
  renderAs?: RenderAs;
  background?: string;
  foreground?: string;
  gradient?: boolean;
  gradientType?: GradientType;
  gradientStartColor?: string;
  gradientEndColor?: string;
  imageSettings?: ImageSettings;
  needUpload?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  value: "",
  level: "L",
  renderAs: "canvas",
  background: "#ffffff",
  foreground: "#000000",
  gradient: false,
  gradientType: "linear",
  gradientStartColor: "#000000",
  gradientEndColor: "#38bdf8",
  size: 200,
  needUpload: true,
});
const canvasId = ref<string>("");

const handleGetQrCodeBase64 = async () => {
  if (!props.needUpload) return;
  await nextTick();
  try {
    const canvas = document.getElementById(canvasId.value) as HTMLCanvasElement;
    if (!canvas) {
      throw new Error("Canvas element not found");
    }

    const ctx = canvas.getContext("2d");
    if (!ctx) {
      throw new Error("Failed to get canvas context");
    }

    const cropImgInfo = ctx.getImageData(0, 0, props.size, props.size);
    const newCanvas = document.createElement("canvas");
    newCanvas.width = props.size;
    newCanvas.height = props.size;

    const newCtx = newCanvas.getContext("2d");
    if (!newCtx) {
      throw new Error("Failed to get new canvas context");
    }

    newCtx.putImageData(cropImgInfo, 0, 0);
    const formData = base64ToFile(newCanvas.toDataURL(), "qrcode.png");
    FileAPI.upload(formData)
      .then((res) => {
        console.log("res", res);
        const serverUrl = res.Data.HostSetting.External + res.Data.PathSetting.Path;
        emit("update:modelValue", serverUrl);
      })
      .catch((err) => {
        console.error("Error generating QR code base64:", err);
      });
  } catch (error) {
    console.error("Error generating QR code base64:", error);
  }
};

/** 同时监听value和needUpload */
watch(
  () => [props.value, props.needUpload],
  ([newValue, newNeedUpload]) => {
    if (newNeedUpload && newValue) {
      nextTick(() => {
        handleGetQrCodeBase64();
      });
    }
  },
  { immediate: true }
);

onMounted(() => {
  nextTick(() => {
    canvasId.value = generateUUID();
  });
});
</script>

<style scoped lang="scss"></style>
