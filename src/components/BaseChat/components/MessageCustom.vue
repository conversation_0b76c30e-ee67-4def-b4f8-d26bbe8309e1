<script lang="ts" setup>
import dayjs from "dayjs";

interface Props {
  msg: RoomMsg;
  sessionUsers: Record<string, PageBaseUserProfile>;
}

const props = defineProps<Props>();

const userInfo = ref<PageBaseUserProfile | null>(null);

watchEffect(() => {
  // 获取当前这条消息是谁发送的
  const fromUserId = props.msg.FromUserGuid;
  userInfo.value = props.sessionUsers[fromUserId] || null;
});

const isDoctor = computed(
  () =>
    userInfo.value?.UserRole === "doctor" ||
    userInfo.value?.UserRole === "nurse" ||
    userInfo.value?.UserRole === "therapist" ||
    userInfo.value?.UserRole === "assistant"
);

// 需要显示头像的类型
const hasAvatar = [5, 6, 7, 10, 11, 13];

const handleProcessMsg = (msg: string): RoomMsgObject | null => {
  if (!msg) return null;
  const msgObj: RoomMsgObject = JSON.parse(msg);
  return msgObj;
};

const msgObject = computed(() => handleProcessMsg(props.msg.Msg));
</script>

<template>
  <div v-if="userInfo" class="message-custom" :class="{ 'is-doctor': isDoctor }">
    <!-- 左侧医生布局 -->
    <template v-if="isDoctor && hasAvatar.includes(msgObject?.CustomType || 0)">
      <el-avatar :size="40" :src="userInfo.HeadImg" :icon="!userInfo.HeadImg ? 'User' : ''" />
      <div class="message-content left">
        <div class="user-name">{{ userInfo.Name }}</div>
        <div v-if="msgObject" class="custom-content">
          <!-- 类型5:评估表单 -->
          <div v-if="msgObject.CustomType === 5" class="assess-card">
            <div class="assess-title">{{ msgObject.Body.assessName }}</div>
            <div class="assess-tip">请打开全量表，并从具体写后提交</div>
          </div>

          <!-- 类型6:运动指导 -->
          <div v-else-if="msgObject.CustomType === 6" class="exercise-card">
            <div class="exercise-content">
              <img :src="msgObject.Body.image" alt="运动图示" class="exercise-image" />
              <div class="exercise-name">{{ msgObject.Body.name }}</div>
            </div>
          </div>

          <!-- 类型7:评估结果 -->
          <div v-else-if="msgObject.CustomType === 7" class="assess-card">
            <div class="assess-title">{{ msgObject.Body.assessName }}</div>
            <div class="assess-tip">查看结果</div>
          </div>

          <!-- 类型11:治疗方案 -->
          <div v-else-if="msgObject.CustomType === 11" class="treatment-card">
            <div class="treatment-header">
              <div class="treatment-title">点击查看</div>
            </div>
            <div class="treatment-content">
              <div class="treatment-info">
                <span>治疗方案</span>
                <span>{{ dayjs(msgObject.Body.sendDateTime).format("YYYY-MM-DD") }}</span>
              </div>
            </div>
          </div>

          <!-- 默认情况 -->
          <div v-else class="unsupported-type">不兼容的类型</div>
        </div>
      </div>
    </template>

    <!-- 右侧患者布局 -->
    <template v-else-if="!isDoctor && hasAvatar.includes(msgObject?.CustomType || 0)">
      <div class="message-content right">
        <div class="user-name">{{ userInfo.Name }}</div>
        <div v-if="msgObject" class="custom-content">
          <!-- 复用上面相同的消息类型组件 -->
          <div v-if="msgObject.CustomType === 5" class="assess-card">
            <div class="assess-title">{{ msgObject.Body.assessName }}</div>
            <div class="assess-tip">请打开全量表，并从具体写后提交</div>
          </div>

          <div v-else-if="msgObject.CustomType === 6" class="exercise-card">
            <div class="exercise-content">
              <img :src="msgObject.Body.image" alt="运动图示" class="exercise-image" />
              <div class="exercise-name">{{ msgObject.Body.name }}</div>
            </div>
          </div>

          <div v-else-if="msgObject.CustomType === 7" class="assess-card">
            <div class="assess-title">{{ msgObject.Body.assessName }}</div>
            <div class="assess-tip">查看结果</div>
          </div>

          <!-- 类型10:病情描述 -->
          <div v-else-if="msgObject.CustomType === 10" class="treatment-card">
            <div class="treatment-header">
              <div class="treatment-title">病情描述</div>
            </div>
            <div class="treatment-content">
              <span>{{ msgObject.Body.content }}</span>
            </div>
          </div>

          <div v-else-if="msgObject.CustomType === 11" class="treatment-card">
            <div class="treatment-header">
              <div class="treatment-title">点击查看</div>
            </div>
            <div class="treatment-content">
              <div class="treatment-info">
                <span>治疗方案</span>
                <span>{{ dayjs(msgObject.Body.sendDateTime).format("YYYY-MM-DD") }}</span>
              </div>
            </div>
          </div>

          <div v-else class="unsupported-type">不兼容的类型</div>
        </div>
      </div>
      <el-avatar :size="40" :src="userInfo.HeadImg" :icon="!userInfo.HeadImg ? 'User' : ''" />
    </template>

    <!-- 普通消息 -->
    <template v-else>
      <div v-if="msgObject" class="w-full flex items-center justify-center">
        <!-- 类型4:评论 -->
        <div v-if="msgObject.CustomType === 4" class="normal-message">
          <div class="message-text">
            {{ msgObject.Body.userName }}在
            <span>{{ msgObject.Body.actionName }}</span>
            中发表了新评论
          </div>
        </div>

        <!-- 类型8:普通消息 -->
        <div v-else-if="msgObject.CustomType === 8" class="normal-message">
          <div class="message-text">{{ msgObject.Body.message }}</div>
        </div>

        <!-- 类型9:群聊消息 -->
        <div v-else-if="msgObject.CustomType == 9">
          <span class="message-text">
            {{ msgObject.Body.title }}
            <br />
            <span v-for="(item, index) in msgObject.Body.userNames" :key="index">@{{ item }}</span>
            {{ msgObject.Body.message }}
          </span>
        </div>

        <!-- 类型12:普通消息 -->
        <div v-else-if="msgObject.CustomType === 12" class="normal-message">
          <div class="message-text">医生更新了电子病历</div>
        </div>

        <div v-else-if="msgObject.CustomType === 13" class="continue-treatment">
          <div class="message-text">提交了延续治疗的申请</div>
        </div>

        <!-- 默认情况 -->
        <div v-else class="unsupported-type">不兼容的类型</div>
      </div>
    </template>
  </div>
</template>

<style scoped>
.message-custom {
  display: flex;
  margin: 16px 0;
  gap: 12px;
}

.message-custom.is-doctor {
  justify-content: flex-start;
}

.message-custom:not(.is-doctor) {
  justify-content: flex-end;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-content.left {
  align-items: flex-start;
}

.message-content.right {
  align-items: flex-end;
}

.user-name {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.assess-card {
  width: 300px;
  background: #11a952;
  border-radius: 4px;
}

.assess-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  text-align: center;
  color: #fff;
  padding: 8px 12px;
}

.assess-tip {
  color: #666;
  font-size: 12px;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  text-align: center;
}

.exercise-card {
  width: 300px;
  background: #eff0f1;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  padding: 12px;
}

.exercise-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.exercise-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.exercise-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.normal-message {
  width: 300px;
}

.message-text {
  color: #666;
  font-size: 12px;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  text-align: center;
}

.treatment-card {
  width: 300px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.treatment-header {
  padding: 12px;
  background: #11a952;
  border-bottom: 1px solid #f0f0f0;
}

.treatment-title {
  font-size: 16px;
  color: #fff;
  text-align: center;
  font-weight: 500;
}

.treatment-content {
  padding: 12px;
}

.treatment-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.treatment-info span:first-child {
  color: #666;
}

.treatment-info span:last-child {
  color: #333;
}

.continue-treatment {
  width: 300px;
}

.unsupported-type {
  text-align: center;
}
</style>
