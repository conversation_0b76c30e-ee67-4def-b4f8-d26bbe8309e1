<template>
  <div
    v-if="userInfo"
    :style="{
      display: 'flex',
      justifyContent: isDoctor ? 'flex-start' : 'flex-end',
      margin: '16px 0',
      gap: '12px',
    }"
  >
    <el-avatar v-if="isDoctor" :size="40" :src="userInfo.HeadImg">
      <el-icon v-if="!userInfo.HeadImg"><User /></el-icon>
    </el-avatar>

    <div
      :style="{
        display: 'flex',
        flexDirection: 'column',
        maxWidth: '70%',
        alignItems: isDoctor ? 'flex-start' : 'flex-end',
      }"
    >
      <div
        :style="{
          fontSize: '14px',
          color: '#666',
          marginBottom: '4px',
          textAlign: isDoctor ? 'left' : 'right',
        }"
      >
        {{ userInfo.Name }}
      </div>

      <el-image
        v-if="imageUrl"
        :src="imageUrl"
        :style="{
          maxWidth: '150px',
          borderRadius: '8px',
        }"
        :preview-src-list="[imageUrl]"
        :initial-index="0"
        hide-on-click-modal
        fit="cover"
      >
        <template #preview>预览图片</template>
      </el-image>
    </div>

    <el-avatar v-if="!isDoctor" :size="40" :src="userInfo.HeadImg">
      <el-icon v-if="!userInfo.HeadImg"><User /></el-icon>
    </el-avatar>
  </div>
</template>

<script setup lang="ts">
import { User } from "@element-plus/icons-vue";

interface Props {
  msg: RoomMsg;
  sessionUsers: Record<string, PageBaseUserProfile>;
}

const props = defineProps<Props>();
const userInfo = ref<PageBaseUserProfile | null>(null);
const imageUrl = ref<string | null>(null);

const isDoctor = computed(
  () =>
    userInfo.value?.UserRole === "doctor" ||
    userInfo.value?.UserRole === "nurse" ||
    userInfo.value?.UserRole === "therapist" ||
    userInfo.value?.UserRole === "assistant"
);

// 处理消息内容，解析JSON获取图片URL
const handleProcessMsg = (msg: string): { url: string } | null => {
  if (!msg) return null;
  try {
    const msgObj = JSON.parse(msg);
    return msgObj;
  } catch (error) {
    console.error("解析消息失败:", error);
    return null;
  }
};

// 监听props变化并更新状态
watchEffect(() => {
  const fromUserId = props.msg.FromUserGuid;
  userInfo.value = props.sessionUsers[fromUserId] || null;

  const processedMsg = handleProcessMsg(props.msg.Msg);
  imageUrl.value = processedMsg?.url || null;
});
</script>
