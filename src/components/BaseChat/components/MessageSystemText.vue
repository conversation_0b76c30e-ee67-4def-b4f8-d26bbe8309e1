<template>
  <div class="message-content">
    <div v-if="props.msg.MsgType === 'invite'" class="message-text">发起了视频通话</div>
    <div v-if="props.msg.MsgType === 'tip'" class="message-text">
      {{ props.msg.Msg }}
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  msg: RoomMsg;
}>();
</script>

<style lang="scss" scoped>
.message-content {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
}

.message-text {
  color: #666;
  font-size: 12px;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  text-align: center;
}
</style>
