<template>
  <div class="message-list" :style="containerStyle">
    <template v-for="item in localMsgList" :key="item.Id">
      <div>
        <div v-if="item.SendDate" class="text-center">
          {{ handleProcessMsgSendTime(item.SendDate) }}
        </div>
        <MessageCustom v-if="item.MsgType === 'custom'" :msg="item" :session-users="sessionUsers" />
        <MessageText
          v-else-if="item.MsgType === 'text'"
          :msg="item"
          :session-users="sessionUsers"
        />
        <MessageSystemText
          v-else-if="item.MsgType === 'tip' || item.MsgType === 'invite'"
          :msg="item"
        />
        <MessageImage
          v-else-if="item.MsgType === 'image'"
          :msg="item"
          :session-users="sessionUsers"
        />
        <MessageVideo
          v-else-if="item.MsgType === 'video'"
          :msg="item"
          :session-users="sessionUsers"
        />
        <div v-else class="text-center">不兼容的类型</div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

const props = defineProps<{
  sessionUsers: Record<string, PageBaseUserProfile>;
  msgList: RoomMsg[];
}>();

const containerStyle = {
  height: "calc(100vh - 260px)",
  overflow: "auto",
  padding: "0 20px",
  backgroundColor: "#fff",
};

const localMsgList = ref<RoomMsg[]>([]);

const handleProcessMsgList = () => {
  localMsgList.value = [...props.msgList].sort((a, b) => a.SendDate - b.SendDate);
};

const handleProcessMsgSendTime = (time: number): string => {
  const sendDates = localMsgList.value.map((x) => x.SendDate);
  const index = sendDates.indexOf(time);
  if (index === 0) {
    return dayjs(time).format("HH:mm:ss");
  }
  const before = sendDates[index - 1] * 1;
  const current = time * 1;
  const timestamp = current - before;
  const minute = timestamp / 60000;
  if (minute > 5) {
    return dayjs(time).format("HH:mm:ss");
  }
  return "";
};

watch(
  () => props.msgList,
  () => {
    handleProcessMsgList();
  },
  { immediate: true }
);
</script>

<style scoped>
.text-center {
  text-align: center;
}
</style>
