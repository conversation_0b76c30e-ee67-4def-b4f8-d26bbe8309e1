<script setup lang="ts">
import { User } from "@element-plus/icons-vue";

interface Props {
  msg: RoomMsg;
  sessionUsers: Record<string, PageBaseUserProfile>;
}

const props = defineProps<Props>();
const userInfo = ref<PageBaseUserProfile | null>(null);

watchEffect(() => {
  const fromUserId = props.msg.FromUserGuid;
  userInfo.value = props.sessionUsers[fromUserId] || null;
});

const isDoctor = computed(
  () =>
    userInfo.value?.UserRole === "doctor" ||
    userInfo.value?.UserRole === "nurse" ||
    userInfo.value?.UserRole === "therapist" ||
    userInfo.value?.UserRole === "assistant"
);

const handleProcessMsg = (msg: string): { url: string } | null => {
  if (!msg) return null;
  return JSON.parse(msg);
};

const videoUrl = computed(() => handleProcessMsg(props.msg.Msg)?.url);
</script>

<template>
  <div v-if="userInfo" class="message-video" :class="{ 'is-doctor': isDoctor }">
    <el-avatar v-if="isDoctor" :size="40" :src="userInfo.HeadImg">
      <template v-if="!userInfo.HeadImg">
        <el-icon><User /></el-icon>
      </template>
    </el-avatar>

    <div class="message-content">
      <div class="user-name">
        {{ userInfo.Name }}
      </div>
      <video v-if="videoUrl" :src="videoUrl" controls class="video-player" />
    </div>

    <el-avatar v-if="!isDoctor" :size="40" :src="userInfo.HeadImg">
      <template v-if="!userInfo.HeadImg">
        <el-icon><User /></el-icon>
      </template>
    </el-avatar>
  </div>
</template>

<style scoped>
.message-video {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
  gap: 12px;
}

.message-video.is-doctor {
  justify-content: flex-start;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  align-items: flex-end;
}

.is-doctor .message-content {
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
  text-align: right;
}

.is-doctor .user-name {
  text-align: left;
}

.video-player {
  max-width: 300px;
  border-radius: var(--el-border-radius-base);
}
</style>
