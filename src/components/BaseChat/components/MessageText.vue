<script setup lang="ts">
import { ref, watchEffect } from "vue";
import { ElAvatar } from "element-plus";
import { User } from "@element-plus/icons-vue";

// props定义
const props = defineProps<{
  msg: RoomMsg;
  sessionUsers: Record<string, PageBaseUserProfile>;
}>();

// 响应式数据
const userInfo = ref<PageBaseUserProfile | null>(null);

// 监听数据变化
watchEffect(() => {
  const fromUserId = props.msg.FromUserGuid;
  const user = props.sessionUsers[fromUserId];
  userInfo.value = user || null;
});

// 计算属性
const isDoctor = computed(
  () =>
    userInfo.value?.UserRole === "doctor" ||
    userInfo.value?.UserRole === "nurse" ||
    userInfo.value?.UserRole === "therapist" ||
    userInfo.value?.UserRole === "assistant"
);
</script>

<template>
  <div v-if="userInfo" class="message-container" :class="{ 'is-doctor': isDoctor }">
    <!-- 医生消息布局 -->
    <template v-if="isDoctor">
      <el-avatar :size="40" :src="userInfo.HeadImg">
        <el-icon v-if="!userInfo.HeadImg">
          <User />
        </el-icon>
      </el-avatar>
      <div class="message-content">
        <div class="user-name">{{ userInfo.Name }}</div>
        <div class="message-text doctor-message">
          {{ msg.Msg }}
        </div>
      </div>
    </template>

    <!-- 患者消息布局 -->
    <template v-else>
      <div class="message-content">
        <div class="user-name patient-name">{{ userInfo.Name }}</div>
        <div class="message-text patient-message">
          {{ msg.Msg }}
        </div>
      </div>
      <el-avatar :size="40" :src="userInfo.HeadImg">
        <el-icon v-if="!userInfo.HeadImg">
          <User />
        </el-icon>
      </el-avatar>
    </template>
  </div>
</template>

<style scoped>
.message-container {
  display: flex;
  margin: 16px 0;
  gap: 12px;
}

.message-container.is-doctor {
  justify-content: flex-start;
}

.message-container:not(.is-doctor) {
  justify-content: flex-end;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.user-name {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.patient-name {
  text-align: right;
}

.message-text {
  padding: 12px 16px;
  border-radius: 8px;
  word-break: break-all;
}

.doctor-message {
  background: var(--el-fill-color-light);
  color: var(--el-text-color-primary);
}

.patient-message {
  background: var(--el-color-primary);
  color: #fff;
}
</style>
