<template>
  <MessageList v-if="msgList.length" :session-users="sessionUsers" :msg-list="msgList" />
  <div v-else class="empty-chat">暂无聊天记录</div>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";

const sessionUsers = ref<{ [key: string]: PageBaseUserProfile }>({});
const msgList = ref<RoomMsg[]>([]);
const handleGetChatListByVisitId = async () => {
  if (!props.programId) return;
  try {
    const res = await Training_Api.getRoomMsgByProgramId({ programId: props.programId });
    if (res.Type === 200) {
      const user: PageBaseUserProfile[] = res.Data.Users;
      const tempSessionUsers: { [key: string]: PageBaseUserProfile } = {};
      if (user.length) {
        user.forEach((item) => {
          const userRole = res.Data.RoomInfo?.Members.find((v) => v.UserGuid === item.Id)
            ?.ExpContent?.WorkRole;
          item.UserRole = userRole;
          tempSessionUsers[item.Id] = item;
        });
      }
      sessionUsers.value = tempSessionUsers;
      msgList.value = res.Data.Msg || [];
    }
  } catch (error) {
    console.log(error);
  }
};

interface Props {
  programId: string | undefined;
}
const props = defineProps<Props>();
watch(
  () => props.programId,
  (newVal) => {
    if (newVal) {
      handleGetChatListByVisitId();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped></style>
