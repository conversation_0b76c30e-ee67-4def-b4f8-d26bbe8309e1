<!-- src/components/verify-slider/index.vue -->
<template>
  <div
    ref="trackRef"
    class="verify-slider-track"
    :class="{
      'is-dragging': isDragging && !isVerified,
      'is-track-verified': isVerified,
    }"
    @click="handleTrackClick"
  >
    <div
      ref="sliderRef"
      class="verify-slider-handle"
      :style="{ left: sliderPosition + 'px' }"
      :class="{ 'is-verified': isVerified }"
      @mousedown="handleDragStart"
      @touchstart.prevent="handleDragStart"
    >
      <el-icon v-if="!isVerified" class="slider-icon" :size="20"><ArrowRightBold /></el-icon>
      <el-icon v-if="isVerified" class="slider-icon slider-icon-success" :size="20">
        <Select />
      </el-icon>
    </div>
    <div v-if="!isVerified" class="track-placeholder">
      {{ placeholderText }}
    </div>
    <span v-if="isVerified" class="track-success-text">{{ successText }}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import { ArrowRightBold, Select } from "@element-plus/icons-vue";

const props = defineProps({
  successText: {
    type: String,
    default: "验证成功",
  },
  placeholderText: {
    type: String,
    default: "请向右拖动滑块",
  },
});

const emit = defineEmits<{
  (e: "verified", value: boolean): void;
}>();

const trackRef = ref<HTMLElement | null>(null);
const sliderRef = ref<HTMLElement | null>(null);

const isDragging = ref(false);
const startX = ref(0); // 记录鼠标按下时的X坐标（相对于页面）
const initialSliderX = ref(0); // 记录鼠标按下时滑块的初始left值
const sliderPosition = ref(0); // 滑块当前的left值
const isVerified = ref(false);

const trackWidth = ref(0);
const sliderWidth = ref(0);

const maxSliderPosition = computed(() => {
  return trackWidth.value - sliderWidth.value;
});

function getTrackAndSliderWidths() {
  if (trackRef.value && sliderRef.value) {
    trackWidth.value = trackRef.value.offsetWidth;
    sliderWidth.value = sliderRef.value.offsetWidth;
  }
}

onMounted(() => {
  nextTick(() => {
    // Ensure DOM elements are rendered
    getTrackAndSliderWidths();
  });
  window.addEventListener("resize", getTrackAndSliderWidths);
});

onUnmounted(() => {
  document.removeEventListener("mousemove", handleDragging);
  document.removeEventListener("mouseup", handleDragEnd);
  document.removeEventListener("touchmove", handleDragging);
  document.removeEventListener("touchend", handleDragEnd);
  window.removeEventListener("resize", getTrackAndSliderWidths);
});

function handleDragStart(event: MouseEvent | TouchEvent) {
  if (isVerified.value) return;

  getTrackAndSliderWidths(); // Recalculate widths in case of resize before drag
  isDragging.value = true;
  if (sliderRef.value) {
    sliderRef.value.style.transition = "none"; // Remove transition during drag
  }

  if (event instanceof MouseEvent) {
    startX.value = event.clientX;
  } else {
    startX.value = event.touches[0].clientX;
  }
  initialSliderX.value = sliderPosition.value; // Store current position

  document.addEventListener("mousemove", handleDragging);
  document.addEventListener("mouseup", handleDragEnd);
  document.addEventListener("touchmove", handleDragging, { passive: false });
  document.addEventListener("touchend", handleDragEnd);
}

function handleDragging(event: MouseEvent | TouchEvent) {
  if (!isDragging.value) return;

  // 确保在触摸事件中调用 preventDefault
  if (event instanceof TouchEvent) {
    event.preventDefault();
  }

  let currentX = 0;
  if (event instanceof MouseEvent) {
    currentX = event.clientX;
  } else {
    currentX = event.touches[0].clientX;
  }

  const diffX = currentX - startX.value;
  let newPosition = initialSliderX.value + diffX;

  if (newPosition < 0) {
    newPosition = 0;
  }
  if (newPosition > maxSliderPosition.value) {
    newPosition = maxSliderPosition.value;
  }
  sliderPosition.value = newPosition;
}

function handleDragEnd() {
  if (!isDragging.value) return;
  isDragging.value = false;

  if (sliderPosition.value >= maxSliderPosition.value - 5) {
    // Add a small tolerance
    sliderPosition.value = maxSliderPosition.value; // Snap to end
    isVerified.value = true;
    emit("verified", true);
    if (sliderRef.value) {
      sliderRef.value.style.transition = "left 0.3s ease";
    }
  } else {
    // Reset slider to start with transition
    if (sliderRef.value) {
      sliderRef.value.style.transition = "left 0.3s ease";
    }
    sliderPosition.value = 0;
    emit("verified", false);
  }

  document.removeEventListener("mousemove", handleDragging);
  document.removeEventListener("mouseup", handleDragEnd);
  document.removeEventListener("touchmove", handleDragging);
  document.removeEventListener("touchend", handleDragEnd);
}

// Optional: Clicking on the track to move the slider (can be removed if not desired)
function handleTrackClick(event: MouseEvent) {
  if (isVerified.value || isDragging.value) return;
  // This logic would move the slider to the click point, typically not desired for a verify slider.
  // For a verify slider, usually only dragging the handle is allowed.
  // If user clicks track and it's not on handle, we might reset or do nothing.
  // For now, let's assume track clicks don't do anything unless it's to start a drag on the handle itself.
}

// Expose for template (though most are refs and automatically available)
// defineExpose({ isVerified });
</script>

<style lang="scss" scoped>
.verify-slider-track {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: #e9ecef; // 默认背景色
  border-radius: 4px;
  user-select: none;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;

  /* &.is-dragging {
    background-color: #d1e7dd; // 移除：拖动过程中不再改变背景色
  } */

  &.is-track-verified {
    background-color: #a3d9a5; // 验证成功后的绿色背景 (保持)
  }
}

.track-placeholder {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #6c757d;
  font-size: 14px;
  pointer-events: none;
  white-space: nowrap;
}

.track-success-text {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #155724; // 深绿色文本，确保在绿色背景上可读
  font-size: 14px;
  font-weight: bold;
  pointer-events: none;
  white-space: nowrap;
}

.verify-slider-handle {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 100%;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  color: #495057;
  transition: left 0.3s ease;

  .slider-icon {
    color: #6c757d;
  }

  .slider-icon-success {
    color: #28a745;
  }

  &.is-verified {
    cursor: default;
  }

  &:active {
    cursor: grabbing;
  }
}

html.dark {
  .verify-slider-track {
    background-color: var(--el-fill-color-light); // 默认暗黑模式背景色
    /* &.is-dragging {
      background-color: #2c4836; // 移除：暗黑模式下拖动过程中不再改变背景色
    } */
    &.is-track-verified {
      background-color: #38761d; // 暗黑模式下验证成功后的深绿色背景 (保持)
    }
  }

  .track-placeholder {
    color: var(--el-text-color-placeholder);
  }

  .track-success-text {
    color: #d1e7dd;
  }

  .verify-slider-handle {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color-lighter);
    color: var(--el-text-color-primary);

    .slider-icon {
      color: var(--el-text-color-secondary);
    }
    .slider-icon-success {
      color: var(--el-color-success);
    }
  }
}
</style>
