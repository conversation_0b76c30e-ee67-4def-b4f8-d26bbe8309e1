<template>
  <section ref="screenBox" class="screen-box">
    <div ref="screenWrapper" class="screen-wrapper">
      <slot />
    </div>
  </section>
</template>

<script setup lang="ts">
interface AutoScaleOptions {
  x?: boolean;
  y?: boolean;
}

interface ScreenProps {
  width?: string | number;
  height?: string | number;
  autoScale?: boolean | AutoScaleOptions;
  delay?: number;
}

// Props 定义
const props = withDefaults(defineProps<ScreenProps>(), {
  width: 1920,
  height: 1080,
  autoScale: true,
  delay: 500,
});

// refs
const screenBox = useTemplateRef<HTMLDivElement>("screenBox");
const screenWrapper = useTemplateRef<HTMLDivElement>("screenWrapper");

// 目标尺寸
const targetSize = {
  width: Number(props.width),
  height: Number(props.height),
};

// 更新容器尺寸
const updateSize = () => {
  if (!screenWrapper.value) return;

  const { width, height } = targetSize;

  screenWrapper.value.style.width = `${width}px`;
  screenWrapper.value.style.height = `${height}px`;
};

// 更新缩放比例
const updateScale = () => {
  if (!screenWrapper.value || !screenBox.value) return;

  const box = screenBox.value;
  const wrapper = screenWrapper.value;

  // 计算缩放比例
  const scale = Math.min(box.clientWidth / targetSize.width, box.clientHeight / targetSize.height);

  const dimensions = {
    wrapper: {
      width: wrapper.clientWidth,
      height: wrapper.clientHeight,
    },
    box: {
      width: box.clientWidth,
      height: box.clientHeight,
    },
  };

  wrapper.style.transform = `scale(${scale},${scale})`;

  // 计算边距
  let margins = {
    x: Math.max((dimensions.box.width - dimensions.wrapper.width * scale) / 2, 0),
    y: Math.max((dimensions.box.height - dimensions.wrapper.height * scale) / 2, 0),
  };

  // 根据 autoScale 配置调整边距
  if (typeof props.autoScale === "object") {
    !props.autoScale.x && (margins.x = 0);
    !props.autoScale.y && (margins.y = 0);
  }

  wrapper.style.margin = `${margins.y}px ${margins.x}px`;
};

// 监听容器大小变化
const onResize = useDebounceFn(async () => {
  updateScale();
}, props.delay);

useResizeObserver(screenBox, onResize);

// 生命周期钩子
onMounted(async () => {
  updateSize();
  updateScale();
});
</script>

<style scoped>
.screen-box {
  overflow: hidden;
  background-size: 100% 100%;
  background-color: #000;
  width: 100%;
  height: 100%;
}

.screen-box .screen-wrapper {
  box-sizing: content-box;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  z-index: 100;
  transform-origin: left top;
}
</style>
