<template>
  <el-card class="flex flex-col h-full">
    <div>
      <slot name="search" />
    </div>
    <div>
      <slot name="searchTable" />
    </div>
    <div ref="centerRef" v-resize="onResize" class="flex-1 overflow-auto">
      <div class="p-10px">
        <slot name="table" />
      </div>
    </div>
    <div>
      <slot name="pagination" />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useTemplateRef } from "vue";
defineProps({ centerClass: { type: String, default: "" } });
const emit = defineEmits(["sizeChanged"]);

const centerRef = useTemplateRef<HTMLElement>("centerRef");
const onResize = (e: ResizeObserverEntry) => {
  // console.log("resize", e, centerRef.value, centerRef.value?.getBoundingClientRect().height);
  emit("sizeChanged", e.contentRect);
};
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}
</style>
