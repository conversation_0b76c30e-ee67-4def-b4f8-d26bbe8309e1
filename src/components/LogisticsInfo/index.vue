<template>
  <div v-loading="logisticsLoading" class="p-20px overflow-y-auto max-h-600px min-h-600px">
    <div
      v-for="(item, index) in logisticsData"
      :key="index"
      class="flex flex-col justify-start items-stretch pb-20px"
      :class="{ 'border-b border-solid border-#dcdfe6 mb-25px': index < logisticsData.length - 1 }"
    >
      <div v-if="item.express && item.express.ExpressNum" class="flex items-center mb-10px">
        <span class="text-14px font-600 color-#333333">
          {{ item.express.Company || "快递单号" }}：
        </span>
        <span class="text-14px font-600 color-#29b7a3 flex-1">
          {{ item.express.ExpressNum }}
        </span>
        <div
          class="text-14px font-600 color-#343434 p-10px cursor-pointer hover:opacity-80 active:opacity-50 transition-opacity"
          @click="onCopyExpressNum(item.express.ExpressNum)"
        >
          复制
        </div>
      </div>
      <div v-if="item.remark" class="text-14px color-#666 mb-20px">备注：{{ item.remark }}</div>
      <el-timeline v-if="item.express?.Track?.length" class="mt-10px!" style="max-width: 800px">
        <el-timeline-item
          v-for="track in item.express.Track"
          :key="track.Time"
          :hide-timestamp="true"
        >
          <el-card>
            <div class="flex items-center justify-start">
              <h4>{{ track.Status }}</h4>
              <h4 class="ml-10px">{{ track.Time }}</h4>
            </div>
            <p class="mt-0!">{{ track.Context }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <el-card v-else class="mt-10px!">
        <p class="color-#333 m-0!">暂无物流信息</p>
      </el-card>
    </div>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
  </div>
</template>

<script setup lang="ts">
import Order_Api from "@/api/order";

const kEnableDebug = false;
const props = defineProps<{
  phone?: string;
  expresses: OrderExpress[];
}>();

const emit = defineEmits<{
  cancel: [];
}>();

const logisticsLoading = ref(false);
const logisticsData = ref<
  {
    express?: ExpressInfo; // 快递信息
    remark?: string; // 备注
  }[]
>([]);

onMounted(() => {
  requestLogisticsInfo();
});

// 复制快递单号
function onCopyExpressNum(expressNum: string) {
  console.debug("复制快递单号", expressNum);
  if (!expressNum) return;

  // 检查是否支持 Clipboard API
  if (!navigator.clipboard) {
    // 降级方案：使用 document.execCommand
    const textArea = document.createElement("textarea");
    textArea.value = expressNum;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      const successful = document.execCommand("copy");
      if (successful) {
        ElMessage.success("复制成功");
      }
    } catch (err) {
      kEnableDebug && console.error("复制失败:", err);
    }
    document.body.removeChild(textArea);
    return;
  }

  // 使用 Clipboard API
  navigator.clipboard
    .writeText(expressNum)
    .then(() => {
      ElMessage.success("复制成功");
    })
    .catch((err) => {
      kEnableDebug && console.error("复制失败:", err);
    });
}

// 获取物流信息
async function requestLogisticsInfo() {
  kEnableDebug && console.debug("requestLogisticsInfo", props.phone, props.expresses);
  const expressNums = props.expresses.map((e) => e.ExpressNum).filter((e) => e);

  // 如果快递单号为空，无法获取快递信息，直接显示快递单号和备注
  if (!expressNums?.length) {
    logisticsData.value = props.expresses.map((e) => ({
      remark: e.Remark,
    }));
    return;
  }

  logisticsLoading.value = true;
  const data = expressNums.map((e) => ({ ExpressNum: e, Phone: props.phone }));
  const r = await Order_Api.getExpressInfo(data);
  logisticsLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.warning(r.Content);
    return;
  }

  logisticsData.value = r.Data.map((e) => ({
    express: e,
    remark: props.expresses.find((f) => f.ExpressNum === e.ExpressNum)?.Remark,
  }));
}
</script>

<style lang="scss" scoped></style>
