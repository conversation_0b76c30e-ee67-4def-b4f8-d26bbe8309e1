<template>
  <div ref="dragContainerRef">
    <slot />
  </div>
</template>

<script setup lang="ts">
import Sortable from "sortablejs";

const kEnableDebug = true;
defineOptions({
  name: "DragContainer",
});

const props = withDefaults(
  defineProps<{
    /** 拖拽占位符样式 */
    ghostClass?: string;
    /** 拖拽中的项样式 */
    dragClass?: string;
    /** 拖拽元素句柄，可为元素类名 */
    handle?: string;
  }>(),
  {
    ghostClass: "ghost",
    dragClass: "drag",
  }
);
const emit = defineEmits<{
  /**
   * 拖拽开始时的回调
   */
  start: [evt: Sortable.SortableEvent];
  /**
   * 拖拽结束后的回调
   */
  end: [evt: Sortable.SortableEvent];
  /**
   * 数据通过 (add, remove, update) 方法更新后触发
   */
  sort: [evt: Sortable.SortableEvent];
}>();

type T = string | number | boolean | object;

// 拖拽列表
const list = defineModel<T[]>();

// 定义拖拽容器
const dragContainer = useTemplateRef<HTMLElement>("dragContainerRef");
// Sortable 实例
let sortable: Sortable | null = null;

// 在 initSortable 中添加动画类配置
const initSortable = () => {
  if (!dragContainer.value) return;

  sortable = Sortable.create(dragContainer.value, {
    animation: 150,
    ghostClass: props.ghostClass, // 拖拽占位符样式
    dragClass: props.dragClass, // 拖拽中的项样式
    handle: props.handle, // 拖拽句柄元素
    onStart: (evt) => emit("start", evt),
    onEnd: (evt) => {
      if (list.value === undefined) return;

      // 拖拽结束后的回调
      const { oldIndex, newIndex } = evt;
      const movedItem = list.value!.splice(oldIndex!, 1)[0];
      list.value!.splice(newIndex!, 0, movedItem);
      emit("end", evt);
    },
    onSort: (evt) => emit("sort", evt),
  });
};

onMounted(() => {
  initSortable();
});

onBeforeUnmount(() => {
  sortable?.destroy();
});
</script>

<style lang="scss" scoped>
// 拖拽占位符样式
// 拖拽占位符样式
.ghost {
  opacity: 0.5;
  border-color: #4080ff;
}

// 拖拽中的项样式
.drag {
  opacity: 0.8;
  transform: rotate(2deg); // 轻微旋转效果
}
</style>
