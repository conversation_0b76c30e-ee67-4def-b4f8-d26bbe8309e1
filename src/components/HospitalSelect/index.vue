<template>
  <el-select-v2
    v-model="modelValue"
    placeholder="请选择医院"
    :options="options"
    :loading="loading"
    filterable
    clearable
    :multiple="props.multiple"
    :multiple-limit="props.multipleLimit"
    collapse-tags
    collapse-tags-tooltip
    :props="{
      label: 'Name',
      value: props.keyId,
    }"
    :empty-values="[null, undefined, '']"
    :value-on-clear="() => null"
    :disabled="props.disabled"
    @focus="handleFetchOptions"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";

interface Props {
  // 请求数据需要的参数
  isEnabled?: boolean;
  pageable?: boolean;
  scopeable?: boolean;
  dtoTypeName?: string;
  isTreatment?: boolean | null;

  // 内部组件需要参数
  keyId?: string;
  disabled?: boolean;
  multiple?: boolean;
  multipleLimit?: number;
}

type OptionType = string | string[] | null | undefined;
const emit = defineEmits<{
  (e: "change", value: OptionType, option: BaseOrganization): void;
}>();

const modelValue = defineModel<OptionType>({ required: true });
const props = withDefaults(defineProps<Props>(), {
  isEnabled: true,
  pageable: true,
  scopeable: false,
  multiple: false,
  dtoTypeName: "QueryOrgDtoForDropDownList",
  keyId: "Id",
  multipleLimit: 9999999,
  isTreatment: null,
  disabled: false,
});

// 是否正在从远程获取数据
const loading = ref(false);
// 选项
const options = ref<BaseOrganization[]>([]);

const handleFetchOptions = async () => {
  if (options.value.length > 0) return;

  loading.value = true;
  const res = await Passport_Api.getOrganizationList({
    IsEnabled: props.isEnabled,
    Keyword: "",
    DtoTypeName: props.dtoTypeName,
    PageIndex: 1,
    PageSize: 9999,
    Pageable: props.pageable,
    Scopeable: props.scopeable,
    IsTreatment: props.isTreatment,
  });
  loading.value = false;
  if (res.Type === 200) {
    options.value = res.Data.Rows;
  }
};

const handleChange = (value: OptionType) => {
  const orgInfo = options.value.find((item) => item.Id === value);
  emit("change", modelValue.value, orgInfo!);
};

defineExpose({
  handleFetchOptions,
});
</script>

<style scoped lang="scss"></style>
