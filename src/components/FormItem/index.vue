<template>
  <div class="flex justify-start items-center flex-wrap mb-10px mr-10px">
    <div v-if="props.label" class="text-right mr-10px">
      <span>
        {{ props.label }}
        <span v-if="props.required" class="mr-4px text-red">*</span>
      </span>
    </div>
    <div class="flex-1">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  // 请求数据需要的参数
  label?: string;
  required?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  label: "",
  required: false,
});
</script>

<style lang="scss" scoped></style>
