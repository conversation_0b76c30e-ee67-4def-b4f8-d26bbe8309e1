<template>
  <el-cascader
    v-model="selectedValue"
    :options="options"
    :props="cascaderProps"
    filterable
    style="width: 300px"
    :empty-values="['', null, undefined]"
    value-on-clear=""
    clearable
    @change="handleCascaderChange"
  />
</template>

<script setup lang="ts">
import { CascaderOption, CascaderValue } from "element-plus";

declare global {
  interface Window {
    AMap: any;
  }
}

interface DistrictInfo {
  name: string;
  code: string;
}

const emit = defineEmits<{
  (e: "update:modelValue", value: CascaderValue): void;
  (e: "change", value: CascaderValue): void;
  (e: "getSelectCityInfo", value: DistrictInfo[]): void;
}>();

interface Props {
  modelValue: string;
  isOnlyCity?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  isOnlyCity: false,
});
const selectedValue = ref<CascaderValue>(props.modelValue ? JSON.parse(props.modelValue) : []);
const cascaderProps = {
  value: "adcode",
  label: "name",
  children: "districtList",
};
const options = ref<CascaderOption[]>([]);
const getDistrictInfoByAdcodes = (
  adcodes: string[],
  districts: CascaderOption[],
  isReturnName: boolean = true
): string[] => {
  const result: string[] = [];
  const findName = (adcode: string, districts: CascaderOption[]): string | null => {
    for (const district of districts) {
      if (district.adcode === adcode) {
        return district.name as string;
      }
      if (district.districtList) {
        const found = findName(adcode, district.districtList as CascaderOption[]);
        if (found) return found;
      }
    }
    return null;
  };
  adcodes.forEach((adcode) => {
    if (isReturnName) {
      const name = findName(adcode, districts);
      if (name) result.push(name);
    } else {
      result.push(adcode);
    }
  });
  return result;
};
const getDistrictDetailInfo = (adcodes: string[], districts: CascaderOption[]): DistrictInfo[] => {
  const result: DistrictInfo[] = [];

  const findDistrict = (adcode: string, districts: CascaderOption[]): DistrictInfo | null => {
    for (const district of districts) {
      if (district.adcode === adcode) {
        return {
          name: district.name as string,
          code: district.adcode as string,
        };
      }
      if (district.districtList) {
        const found = findDistrict(adcode, district.districtList as CascaderOption[]);
        if (found) return found;
      }
    }
    return null;
  };

  adcodes.forEach((adcode) => {
    const districtInfo = findDistrict(adcode, districts);
    if (districtInfo) {
      result.push(districtInfo);
    }
  });

  return result;
};

const handleCascaderChange = (value: CascaderValue) => {
  emit("update:modelValue", value ? JSON.stringify(value) : "");
  const names = value ? getDistrictInfoByAdcodes(value as string[], options.value, true) : [];
  emit("change", value ? JSON.stringify(names) : "");
  const districtInfo = value ? getDistrictDetailInfo(value as string[], options.value) : [];
  emit("getSelectCityInfo", districtInfo);
};

const handleGetCityList = async () => {
  const type = props.isOnlyCity ? "city" : "country";
  let cachedData = null;
  if (type === "city") {
    cachedData = sessionStorage.getItem("city_amap_district_data");
  } else {
    cachedData = sessionStorage.getItem("country_amap_district_data");
  }
  // 先检查缓存
  if (cachedData) {
    options.value = JSON.parse(cachedData);
    return;
  }
  handleSendApiGetCityList(type);
};
const handleSendApiGetCityList = (type: string) => {
  const script = document.createElement("script");
  script.type = "text/javascript";
  script.src = "https://webapi.amap.com/maps?v=1.4.15&key=c6130a5c754a18b366aff20082aa91de";
  document.getElementsByTagName("head")[0].appendChild(script);
  console.log("script", script);
  script.onload = () => {
    const AMap = window.AMap;
    AMap.plugin("AMap.DistrictSearch", () => {
      const districtSearch = new AMap.DistrictSearch({
        // 关键字对应的行政区级别，country表示国家
        level: props.isOnlyCity ? "city" : "country",
        //  显示下级行政区级数，1表示返回下一级行政区
        subdistrict: props.isOnlyCity ? 2 : 3,
      });

      // 搜索所有省/直辖市信息
      districtSearch.search("中国", (status: any, result: any) => {
        const districtData = result.districtList[0].districtList;
        // 存入缓存
        if (type === "city") {
          sessionStorage.setItem("city_amap_district_data", JSON.stringify(districtData));
        } else {
          sessionStorage.setItem("country_amap_district_data", JSON.stringify(districtData));
        }
        options.value = districtData;
      });
    });
  };
};

onMounted(() => {
  // 获取城市列表
  handleGetCityList();
});
</script>

<style lang="scss" scoped></style>
