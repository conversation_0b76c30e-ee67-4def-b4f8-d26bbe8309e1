<template>
  <el-cascader
    v-model="modelValue"
    :options="options"
    :props="cascaderProps"
    collapse-tags
    collapse-tags-tooltip
    :filterable="props.filterable"
    :disabled="props.disabled"
    :placeholder="props.placeholder"
    :clearable="props.clearable"
    :show-all-levels="props.showAllLevels"
    :max-collapse-tags="props.maxCollapseTags"
    :empty-values="[null, undefined]"
    :value-on-clear="() => undefined"
    @change="emit('change', $event)"
  />
</template>

<script setup lang="ts">
import { CascaderOption, CascaderValue } from "element-plus";

const kEnableDebug = true;
defineOptions({
  name: "MultipleCityPicker",
});

const emit = defineEmits<{
  change: [CascaderValue];
}>();

const cascaderProps = { multiple: true, value: "adcode", label: "name", children: "districtList" };
const props = withDefaults(
  defineProps<{
    filterable?: boolean;
    placeholder?: string;
    disabled?: boolean;
    clearable?: boolean;
    showAllLevels?: boolean;
    maxCollapseTags?: number;
    onlyCity?: boolean;
  }>(),
  {
    filterable: true,
    placeholder: "请选择地区",
    disabled: false,
    clearable: true,
    showAllLevels: true,
    maxCollapseTags: 1,
    onlyCity: true,
  }
);

// 选择的地区id数组
const modelValue = defineModel<string[][] | undefined>();

// 城市列表数据
const options = ref<CascaderOption[]>([]);

// 获取城市列表
const getCityList = async () => {
  const type = props.onlyCity ? "city" : "country";
  let cachedData = null;
  if (type === "city") {
    cachedData = sessionStorage.getItem("city_amap_district_data");
  } else {
    cachedData = sessionStorage.getItem("country_amap_district_data");
  }

  // 先检查缓存
  if (cachedData) {
    options.value = JSON.parse(cachedData);
    return;
  }

  requestCityList(type);
};

// 请求城市列表
const requestCityList = (type: string) => {
  const script = document.createElement("script");
  script.type = "text/javascript";
  script.src = "https://webapi.amap.com/maps?v=1.4.15&key=c6130a5c754a18b366aff20082aa91de";
  document.getElementsByTagName("head")[0].appendChild(script);
  kEnableDebug && console.debug("script", script);

  script.onload = () => {
    const AMap = window.AMap;
    AMap.plugin("AMap.DistrictSearch", () => {
      const districtSearch = new AMap.DistrictSearch({
        // 关键字对应的行政区级别，country表示国家
        level: props.onlyCity ? "city" : "country",
        //  显示下级行政区级数，1表示返回下一级行政区
        subdistrict: props.onlyCity ? 2 : 3,
      });

      // 搜索所有省/直辖市信息
      districtSearch.search("中国", (status: any, result: any) => {
        const districtData = result.districtList[0].districtList;
        // 存入缓存
        if (type === "city") {
          sessionStorage.setItem("city_amap_district_data", JSON.stringify(districtData));
        } else {
          sessionStorage.setItem("country_amap_district_data", JSON.stringify(districtData));
        }
        options.value = districtData;
      });
    });
  };
};

onMounted(() => {
  // 获取城市列表
  getCityList();
});
</script>

<style lang="scss" scoped></style>
