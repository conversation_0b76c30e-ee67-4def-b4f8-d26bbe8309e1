<template>
  <el-select-v2
    v-model="selectedVal"
    :options="userList"
    placeholder="请选择"
    :empty-values="['', undefined, null]"
    :value-on-clear="() => null"
    :multiple="props.multiple"
    clearable
    collapse-tags
    reserve-keyword
    collapse-tags-tooltip
    filterable
    remote
    :remote-method="handleFetchOptionsByKeyword"
    :disabled="props.disabled"
    :props="{
      label: 'Name',
      value: props.keyId,
    }"
    @change="handleUserSelectChange"
    @clear="handleSelectClear"
  />
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";

const userOptions = computed(() => {
  // 创建一个新数组来存储最终结果
  // const result = [...userList.value];
  // // 遍历 beforeSelected，检查每个项是否存在于 userList 中
  // beforeSelected.value.forEach((item) => {
  //   const exists = result.some((option) => option.Id === item.Id);
  //   // 如果不存在，则添加到结果数组中
  //   if (!exists) {
  //     result.push(item);
  //   }
  // });
  return beforeSelected.value;
});
const userList = ref<BaseUserProfile[]>([]);
const beforeSelected = ref<BaseUserProfile[]>([]);

interface Props {
  modelValue: string | string[] | null | undefined;
  roleTypes?: string[] | null;
  deptIds?: string[] | null;
  orgIds?: string[] | null;
  disabled?: boolean;
  pageable?: boolean;
  scopeable?: boolean;
  multiple?: boolean;
  isImmediate?: boolean;
  keyId?: string;
  dtoTypeName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  roleTypes: null,
  deptIds: null,
  orgIds: null,
  disabled: false,
  pageable: true,
  scopeable: true,
  multiple: false,
  isImmediate: false,
  keyId: "Id",
  dtoTypeName: "QueryUserOutputDto",
});

const selectedVal = ref<string | string[] | null>(null);

const emit = defineEmits<{
  (e: "update:modelValue", value: string | string[] | null | undefined): void;
  (e: "change", value: string | string[] | null | undefined): void;
}>();

const handleUserSelectChange = (value: string | string[] | null | undefined) => {
  // 如果是多选
  if (Array.isArray(value)) {
    // 多选
    const allUsers = [...beforeSelected.value, ...userList.value];
    beforeSelected.value = allUsers.filter((s) => value.includes(s.Id));
  } else {
    // 单选
    beforeSelected.value = userList.value.filter((s) => s.Id === value);
  }
  emit("update:modelValue", value);
  emit("change", value);
};

const handleSelectClear = () => {
  beforeSelected.value = [];
  emit("update:modelValue", null);
  emit("change", null);
};

const handleFetchOptionsByKeyword = async (keyword: string) => {
  if (!keyword && selectedVal.value) return;
  handleFetchOptions({
    Keyword: keyword,
  });
};
const getUserOptions = async (params: { Keyword?: string; UserId?: string[] | string | null }) => {
  const ids = params.UserId
    ? typeof params.UserId === "string"
      ? [params.UserId]
      : params.UserId
    : undefined;
  const res = await Passport_Api.getUserProfile({
    PageIndex: 1,
    PageSize: 500,
    RoleTypes: props.roleTypes ?? [],
    DeptIds: props.deptIds,
    IsEnabled: !props.disabled,
    Pageable: props.pageable,
    Scopeable: props.scopeable,
    OrgIds: props.orgIds,
    Keyword: params.Keyword || "",
    Ids: ids,
    DtoTypeName: props.dtoTypeName,
  });
  if (res.Type === 200) {
    userList.value = res.Data.Row;
    if (ids) {
      beforeSelected.value = res.Data.Row.filter((s) => ids.includes(s.Id));
    }
  } else {
    userList.value = [];
  }
};
const handleFetchOptions = async (params: {
  Keyword?: string;
  UserId?: string[] | string | null;
}) => {
  getUserOptions(params);
};
const handleGetUserInfoByUserId = async (userId: string) => {
  selectedVal.value = [userId];
  handleFetchOptions({
    UserId: userId,
  });
};

watch(
  [() => props.orgIds, () => props.deptIds, () => props.roleTypes],
  ([newOrgIds, newDeptIds, newRoleTypes], [oldOrgIds, oldDeptIds, oldRoleTypes]) => {
    if (
      JSON.stringify(newOrgIds) !== JSON.stringify(oldOrgIds) ||
      JSON.stringify(newDeptIds) !== JSON.stringify(oldDeptIds) ||
      JSON.stringify(newRoleTypes) !== JSON.stringify(oldRoleTypes)
    ) {
      selectedVal.value = null;
      userList.value = [];
      emit("update:modelValue", null);
      handleFetchOptions({
        Keyword: "",
      });
    }
  },
  { deep: true }
);
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal === selectedVal.value) return;
    selectedVal.value = newVal ?? null;
    if (!newVal || !newVal.length) return;
    handleFetchOptions({
      UserId: newVal,
    });
  },
  { immediate: true }
);

onMounted(() => {});
defineExpose({
  handleGetUserInfoByUserId,
  userOptions,
});
</script>

<style scoped lang="scss"></style>
