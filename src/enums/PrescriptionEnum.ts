export enum PrescriptionState {
  /** 待确认 未执行 已执行 已失效 待生效 */
  PendingConfirm = 0,
  PendingExecution = 1,
  Executed = 2,
  Expired = 3,
  PendingActivation = 6,
}
/** 押金状态 */
export enum DeviceBizStateEnum {
  /**
   * 待付款
   */
  PendingPayment = 1,
  /**
   * 已支付-待发货
   */
  PaidToBeShipped = 2,
  /**
   * 已支付-待收货
   */
  PaidToBeReceived = 3,
  /**
   * 已支付-使用中
   */
  InUse = 4,
  /**
   * 待退还
   */
  PendingReturn = 5,
  /**
   * 退还中
   */
  Returning = 6,
  /**
   * 已入库
   */
  Inbound = 7,
  /**
   * 已完成
   */
  Completed = 8,
}
