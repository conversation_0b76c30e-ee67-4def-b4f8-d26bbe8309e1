import { type RouteVO } from "@/api/system/menu";

const routes: RouteVO[] = [
  {
    path: "/third-party",
    component: "Layout",
    name: "third-party",
    redirect: "/third-party/DeviceBinding",
    meta: {
      title: "第三方设备",
      hidden: false,
      roles: ["assistant", "finance", "operations", "storage"],
    },
    children: [
      {
        path: "DeviceBinding",
        name: "DeviceBinding",
        component: "third-party/DeviceBinding",
        meta: {
          title: "设备绑定",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default routes;
