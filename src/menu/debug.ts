import { type RouteVO } from "@/api/system/menu";
import { kDebug } from "@/utils";

const debugRoutes: RouteVO[] = [];
if (kDebug) {
  const rootRoute: RouteVO = {
    path: "/debug",
    component: "Layout",
    name: "debug",
    meta: {
      title: "测试",
    },
    redirect: "/debug/index",
    children: [],
  };
  const debug1 = {
    path: "index",
    component: "debug/Debug",
    name: "debugPage",
    meta: {
      title: "测试页面1",
    },
  };
  const debug2 = {
    path: "2",
    component: "debug/Debug2",
    name: "debugPage2",
    meta: {
      title: "测试页面2",
    },
  };
  rootRoute.children?.push(debug1, debug2);
  debugRoutes.push(rootRoute);
}
export default debugRoutes;
