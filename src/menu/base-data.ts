import { type RouteVO } from "@/api/system/menu";

const baseDataRoutes: RouteVO[] = [
  {
    path: "/base-data",
    component: "Layout",
    name: "BaseData",
    sort: -200,
    meta: {
      title: "基础数据管理",
      icon: "system",
      hidden: false,
      alwaysShow: false,
      roles: ["superOperate", "internetHospitalAdmin"],
    },
    children: [
      {
        path: "dysfunctionManagement",
        component: "base-data/dysfunctionManagement/index",
        name: "DysfunctionManagement",
        meta: {
          title: "功能障碍管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "bodyPartManagement",
        component: "base-data/bodyPartManagement/index",
        name: "BodyPartManagement",
        meta: {
          title: "部位管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "rehabilitationClassificationManagement",
        component: "base-data/rehabilitationClassificationManagement/index",
        name: "RehabilitationClassificationManagement",
        meta: {
          title: "康复分类",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "diseaseManagement",
        component: "base-data/diseaseManagement/index",
        name: "DiseaseManagement",
        meta: {
          title: "病种管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "icd10Management",
        component: "base-data/icd10Management/index",
        name: "Icd10Management",
        meta: {
          title: "ICD-10",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "professionalTitleManagement",
        component: "base-data/professionalTitleManagement/index",
        name: "ProfessionalTitleManagement",
        meta: {
          title: "职称管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "workerTypeManagement",
        component: "base-data/workerTypeManagement/index",
        name: "WorkerTypeManagement",
        meta: {
          title: "职业类型管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "departmentManagement",
        component: "base-data/departmentManagement/index",
        name: "DepartmentManagement",
        meta: {
          title: "科别管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "deviceTypeManagement",
        component: "base-data/deviceTypeManagement/index",
        name: "DeviceTypeManagement",
        meta: {
          title: "设备类型管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "consumablesManagement",
        component: "base-data/consumablesManagement/index",
        name: "ConsumablesManagement",
        meta: {
          title: "耗材管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "commonPhraseManagement",
        component: "base-data/commonPhraseManagement/index",
        name: "CommonPhraseManagement",
        meta: {
          title: "常用语管理",
          icon: "el-icon-Message",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
    ],
  },
];
export default baseDataRoutes;
