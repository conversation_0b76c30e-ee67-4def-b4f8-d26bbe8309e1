import { type RouteVO } from "@/api/system/menu";

const platformResourceRoutes: RouteVO[] = [
  {
    path: "/platform-resource",
    component: "Layout",
    name: "PlatformResource",
    sort: -100,
    meta: {
      title: "平台医疗资源管理",
      icon: "system", // system resources
      hidden: false,
      alwaysShow: false,
      roles: ["superOperate", "internetHospitalAdmin"],
    },
    children: [
      {
        path: "adviceManagement",
        component: "platform-resource/adviceManagement/index",
        name: "AdviceManagement",
        meta: {
          title: "医嘱管理",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "gaugeManagement",
        component: "platform-resource/gaugeManagement/index",
        name: "GaugeManagement",
        meta: {
          title: "表单管理",
          icon: "el-icon-Tickets",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "servicePackageManagement",
        component: "platform-resource/servicePackageManagement/index",
        name: "ServicePackageManagement",
        meta: {
          title: "服务包管理",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "recoveryMissionManagement",
        component: "platform-resource/recoveryMissionManagement/index",
        name: "RecoveryMissionManagement",
        meta: {
          title: "康复宣教管理",
          icon: "el-icon-Memo",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
    ],
  },
];
export default platformResourceRoutes;
