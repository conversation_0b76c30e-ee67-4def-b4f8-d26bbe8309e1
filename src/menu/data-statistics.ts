import { type RouteVO } from "@/api/system/menu";

const dataStatisticsRoutes: RouteVO[] = [
  {
    path: "/data-statistics",
    component: "Layout",
    name: "DataStatistics",
    sort: 10,
    meta: {
      title: "数据统计",
      icon: "system",
      hidden: false,
      alwaysShow: false,
      roles: [
        "scienceTechnologyExhibition",
        "shopOperate",
        "sales",
        "finance",
        "operations",
        "assistant",
        "externalSeller",
      ],
    },
    children: [
      {
        path: "data-large-screen",
        name: "dataLargeScreen",
        component: "data-statistics/data-large-screen/index",
        meta: {
          title: "平台运营数据大屏",
          roles: [
            "operations",
            "sales",
            "dataQuerer",
            "assistant",
            "finance",
            "storage",
            "superOperate",
            "promoter",
          ],
        },
      },
      {
        path: "hospital-data-large-screen",
        name: "hospitalDataLargeScreen",
        component: "data-statistics/hospital-data-large-screen/index",
        meta: {
          title: "医院数据大屏",
          roles: ["operations", "superAdmin", "assistant"],
        },
      },
      {
        path: "operationalGoals",
        component: "data-statistics/operationalGoals/index",
        name: "OperationalGoals",
        meta: {
          title: "运营目标",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["shopOperate", "sales", "operations", "superAdmin"],
        },
      },
      {
        path: "doctorPrescriptionStatistics",
        component: "data-statistics/doctorPrescriptionStatistics/index",
        name: "DoctorPrescriptionStatistics",
        meta: {
          title: "医生开方统计",
          icon: "el-icon-Operation",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["operations", "superAdmin", "assistant", "externalSeller"],
        },
      },
      {
        path: "patientAnalysis",
        component: "data-statistics/patientAnalysis/index",
        name: "PatientAnalysis",
        meta: {
          title: "患者分析",
          icon: "el-icon-Histogram",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["operations", "superAdmin", "assistant"],
        },
      },
      {
        path: "doctorStatusStatistics",
        component: "data-statistics/doctorStatusStatistics/index",
        name: "DoctorStatusStatistics",
        meta: {
          title: "医生情况统计",
          icon: "el-icon-DataLine",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "operations"],
        },
      },
      {
        path: "patientDataStatistics",
        component: "data-statistics/patientDataStatistics/index",
        name: "PatientDataStatistics",
        meta: {
          title: "患者数据统计",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["shopOperate", "sales", "finance", "operations", "assistant"],
        },
      },
      {
        path: "treatmentRegimenDataStatistics",
        component: "data-statistics/treatmentRegimenDataStatistics/index",
        name: "TreatmentRegimenDataStatistics",
        meta: {
          title: "治疗方案数据统计",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: [
            "scienceTechnologyExhibition",
            "shopOperate",
            "sales",
            "finance",
            "operations",
            "assistant",
          ],
        },
      },
      {
        path: "executedTreatmentRegimenDataStatistics",
        component: "data-statistics/executedTreatmentRegimenDataStatistics/index",
        name: "ExecutedTreatmentRegimenDataStatistics",
        meta: {
          title: "已执行方案数据统计",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "operations", "finance", "sales", "externalSeller"],
        },
      },
      {
        path: "treatmentSettlementReport",
        component: "data-statistics/treatmentSettlementReport/index",
        name: "TreatmentSettlementReport",
        meta: {
          title: "财务结算报表(治疗)",
          icon: "el-icon-FirstAidKit",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["shopOperate", "finance", "operations"],
        },
      },
      {
        path: "visitSettlementReport",
        component: "data-statistics/visitSettlementReport/index",
        name: "VisitSettlementReport",
        meta: {
          title: "财务结算报表(就诊)",
          icon: "el-icon-Money",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["shopOperate", "finance", "operations"],
        },
      },
      {
        path: "doctorSettlementStatistics",
        component: "data-statistics/doctorSettlementStatistics/index",
        name: "DoctorSettlementStatistics",
        meta: {
          title: "医生结算统计",
          icon: "el-icon-Avatar",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["finance", "operations"],
        },
      },
    ],
  },
];
export default dataStatisticsRoutes;
