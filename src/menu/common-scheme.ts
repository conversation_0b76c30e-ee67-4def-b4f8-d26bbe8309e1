import { type RouteVO } from "@/api/system/menu";
const commonSchemeRoutes: RouteVO[] = [
  {
    path: "/common-scheme",
    component: "Layout",
    name: "CommonScheme",
    meta: {
      title: "常用方案管理",
      icon: "system", // system resources
      hidden: false,
      alwaysShow: false,
      roles: ["operations", "assistant"],
    },
    children: [
      {
        path: "commonSchemeManagement",
        component: "common-scheme/commonSchemeManagement/index",
        name: "CommonSchemeManagement",
        meta: {
          title: "常用方案管理",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default commonSchemeRoutes;
