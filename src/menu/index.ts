import { type RouteVO } from "@/api/system/menu";

// 使用 Vite 的 import.meta.glob 动态导入所有路由文件
const modules = import.meta.glob("./*.ts", { eager: true });
// console.log(modules);

export const asyncRoutes: RouteVO[] = Object.values(modules)
  .filter((module: any) => {
    // 排除 index.ts 本身
    return module.default && !module.default.__esModule;
  })
  .reduce((routes: RouteVO[], module: any) => {
    // 合并所有路由配置
    return routes.concat(module.default);
  }, [])
  .sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
