export default {
  // 菜单国际化
  route: {
    dashboard: "Dashboard",
    document: "Document",
  },
  // 登录页面国际化
  login: {
    username: "Userna<PERSON>",
    password: "Password",
    login: "Login",
    captchaCode: "Verify Code",
    capsLock: "Caps Lock is On",
    rememberMe: "Remember Me",
    forgetPassword: "Forget Password",
    message: {
      username: {
        required: "Please enter Username",
      },
      password: {
        required: "Please enter Password",
        min: "The password can not be less than 6 digits",
      },
      captchaCode: {
        required: "Please enter Verify Code",
      },
    },
    otherLoginMethods: "Other login methods",
  },
  // 导航栏国际化
  navbar: {
    dashboard: "Dashboard",
    logout: "Logout",
    document: "Document",
    gitee: "Gitee",
    profile: "User Profile",
  },
  sizeSelect: {
    tooltip: "Layout Size",
    default: "Default",
    large: "Large",
    small: "Small",
    message: {
      success: "Switch Layout Size Successful!",
    },
  },
  langSelect: {
    message: {
      success: "Switch Language Successful!",
    },
  },
  settings: {
    project: "Project Settings",
    theme: "Theme",
    interface: "Interface",
    navigation: "Navigation",
    themeColor: "Theme Color",
    tagsView: "Tags View",
    sidebarLogo: "Sidebar Logo",
    sidebarColorScheme: "Sidebar Color Scheme",
    watermark: "Watermark",
    classicBlue: "Classic Blue",
    minimalWhite: "Minimal White",
  },
};
