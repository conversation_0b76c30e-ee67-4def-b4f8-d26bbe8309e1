<template>
  <div @click="handleShowDownloadTask">
    <el-badge>
      <el-icon>
        <Download />
      </el-icon>
    </el-badge>
    <el-dialog
      v-model="showDownloadTask"
      title="下载列表"
      width="800px"
      class="download-task-dialog"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="dialog-header">
        <h3 class="dialog-title">文件下载任务</h3>
        <el-button
          type="danger"
          size="default"
          :icon="Delete"
          class="delete-btn"
          :disabled="!selectedTableIds.length"
          :loading="deleteLoading"
          @click="handleDeletedSelect"
        >
          删除选中项
        </el-button>
      </div>
      <div class="table-container">
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          align="center"
          class="download-table"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" reserve-selection width="55" />
          <el-table-column
            prop="FileName"
            label="文件名称"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template #default="scope">
              <div class="file-name">
                <el-icon class="file-icon"><Document /></el-icon>
                <span>{{ scope.row.FileName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template #default="scope">
              <div class="time-info">
                <el-icon><Calendar /></el-icon>
                <span>{{ scope.row.CreatedTime }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskFinishedTime"
            label="完成时间"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template #default="scope">
              <div class="time-info">
                <el-icon><Timer /></el-icon>
                <span>{{ scope.row.TaskFinishedTime }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="文件状态" align="center" width="120">
            <template #default="scope">
              <el-tag
                v-if="scope.row.Status === 1"
                type="success"
                class="status-tag"
                effect="light"
                round
              >
                <span class="tag-content">
                  <el-icon><CircleCheck /></el-icon>
                  <span>已完成</span>
                </span>
              </el-tag>
              <el-tag v-else type="danger" class="status-tag" effect="light" round>
                <span class="tag-content">
                  <el-icon><Loading /></el-icon>
                  <span>处理中</span>
                </span>
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template #default="scope">
              <el-button
                type="primary"
                circle
                size="small"
                :disabled="scope.row.Status !== 1"
                :icon="Download"
                class="download-btn"
                @click="handleDownTask(scope.row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="params.pageIndex"
          v-model:limit="params.pageSize"
          class="custom-pagination"
          @pagination="handleShowDownloadTask"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { EventBus } from "@/utils/eventBus";
import { useTableConfig } from "@/hooks/useTableConfig";
import Report_Api from "@/api/report";
import { useUserStore } from "@/store";
import dayjs from "dayjs";
import {
  Delete,
  Download,
  Document,
  Calendar,
  Timer,
  CircleCheck,
  Loading,
} from "@element-plus/icons-vue";
const userStore = useUserStore();
const { tableLoading, pageData, total, tableRef, selectedTableIds } = useTableConfig<BaseTask>();
const showDownloadTask = ref<boolean>(false);
const deleteLoading = ref<boolean>(false);
const params = ref<{ userId: string; pageIndex: number; pageSize: number }>({
  userId: userStore.userInfo.Id,
  pageIndex: 1,
  pageSize: 10,
});

const handleShowDownloadTask = () => {
  showDownloadTask.value = true;
  handleGetTableList();
};
const handleGetTableList = async () => {
  const res = await Report_Api.getExportTasks(params.value);
  if (res.Type === 200) {
    res.Data.Rows.forEach((item) => {
      item.CreatedTime = dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
      item.TaskFinishedTime = dayjs(item.TaskFinishedTime).format("YYYY-MM-DD HH:mm:ss");
    });
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};
const handleTableSelect = (selection: BaseTask[]) => {
  selectedTableIds.value = selection.map((item) => item.Id);
};
const handleDeletedSelect = async () => {
  deleteLoading.value = true;
  const res = await Report_Api.removeExportTask(selectedTableIds.value);
  if (res.Type === 200) {
    ElNotification.success("删除成功");
    selectedTableIds.value = [];
    tableRef.value?.clearSelection();
    handleGetTableList();
  } else {
    ElNotification.warning(res.Content);
  }
  deleteLoading.value = false;
};
const handleDownTask = async (row: BaseTask) => {
  const res = await Report_Api.queryResultsExportV2({
    TaskId: row.Id,
  });
  console.log(res);
  if (res.Type === 200) {
    const { Data } = res;
    if (Data) {
      var elementLink = document.createElement("a");
      var blob = new Blob([Data], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      elementLink.href = window.URL.createObjectURL(blob);
      elementLink.download = row.FileName;
      elementLink.style.display = "none";
      document.body.appendChild(elementLink);
      elementLink.click();
      document.body.removeChild(elementLink);
    } else {
      ElNotification.error("导出异常");
    }
  }
};
onMounted(() => {
  EventBus.on("triggerDownloadTask", () => {
    handleShowDownloadTask();
  });
});
</script>

<style scoped lang="scss">
.download-task-dialog {
  :deep(.el-dialog__header) {
    padding: 0;
    margin: 0;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }

  :deep(.el-dialog__headerbtn) {
    top: 15px;
    right: 15px;

    .el-dialog__close {
      color: var(--el-text-color-primary);
      font-size: 18px;
    }
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);

    .dialog-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .delete-btn {
      margin: 0;
    }
  }

  .table-container {
    padding: 0;
    margin: 0;
    background-color: var(--el-bg-color);
    height: 400px;
    overflow: hidden;

    .download-table {
      height: 350px;
      margin: 0;
      border-radius: 4px;
      overflow: hidden;

      :deep(.el-table__header) {
        th {
          background-color: var(--el-bg-color-page);
          color: var(--el-text-color-primary);
          font-weight: 600;
          padding: 12px 0;
        }
      }

      :deep(.el-table__row) {
        background-color: var(--el-bg-color);

        &:hover > td {
          background-color: var(--el-fill-color-light) !important;
        }

        td {
          padding: 12px 0;
          border-bottom: 1px solid var(--el-border-color-lighter);
          color: var(--el-text-color-regular);
          transition: background-color 0.3s;
        }
      }

      .file-name {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .file-icon {
          color: var(--el-color-primary);
        }
      }

      .time-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;

        .el-icon {
          color: var(--el-color-info);
          font-size: 14px;
        }
      }

      .status-tag {
        padding: 0 12px;
        height: 32px;

        .tag-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
          height: 100%;

          .el-icon {
            font-size: 14px;
            display: flex;
            align-items: center;
          }
        }
      }

      .download-btn {
        &.is-disabled {
          opacity: 0.6;
        }
      }
    }
  }

  .pagination-container {
    padding: 15px 20px;
    background-color: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    display: flex;
    justify-content: flex-end;

    .custom-pagination {
      justify-content: flex-end;
    }
  }
}

html.dark {
  .download-task-dialog {
    .dialog-header {
      background-color: var(--el-bg-color-overlay);
    }

    .table-container {
      background-color: var(--el-bg-color);

      .download-table {
        :deep(.el-table__header) {
          th {
            background-color: var(--el-bg-color-overlay);
          }
        }

        :deep(.el-table__row) {
          background-color: var(--el-bg-color);

          &:hover > td {
            background-color: var(--el-fill-color-darker) !important;
          }
        }
      }
    }

    .pagination-container {
      background-color: var(--el-bg-color-overlay);
    }
  }
}
</style>
