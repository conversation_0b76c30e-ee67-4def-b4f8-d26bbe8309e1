/**
 * 深度移除对象中的 null 和 undefined 值
 *
 * @description
 * - 如果输入为 null，直接返回 null
 * - 如果输入为数组，递归处理每个元素，并过滤掉 null 和 undefined（数字类型除外）
 * - 如果输入为对象，递归处理每个属性，跳过值为 null 或 undefined 的属性
 * - 其他类型直接返回原值
 *
 * @param {any} obj - 需要处理的目标对象
 * @returns {any} 处理后的新对象，移除了所有的 null 和 undefined 值
 *
 * @example
 * // 处理对象
 * deepRemoveNullValues({ a: 1, b: null, c: { d: undefined, e: 2 } })
 * // 返回 { a: 1, c: { e: 2 } }
 *
 * // 处理数组
 * deepRemoveNullValues([1, null, { a: null }, undefined])
 * // 返回 [1, { }]
 */
export function deepRemoveNullValues(obj: any): any {
  if (obj == null) return null;

  if (Array.isArray(obj)) {
    return obj.map(deepRemoveNullValues).filter((item) => item !== null && item !== undefined);
  }

  if (typeof obj === "object") {
    return Object.fromEntries(
      Object.entries(obj)
        .map(([key, value]) => [key, deepRemoveNullValues(value)])
        .filter(([, value]) => value !== null && value !== undefined)
    );
  }

  return obj;
}

/**
 * 获取对象中某个键的String值
 *
 * @param {*} obj 目标对象
 * @param {string} keyField 键
 * @returns {null | string[]}
 */
export function deepGetValueByKey(obj: any, keyField: string): null | string[] {
  if (typeof obj !== "object" || obj === null) return null;

  if (Array.isArray(obj)) {
    const results = obj
      .map((item) => deepGetValueByKey(item, keyField))
      .filter((item) => item !== null)
      .flat();
    return results.length > 0 ? results : null;
  }

  const results = Object.entries(obj).reduce<string[]>((acc, [key, value]) => {
    if (key === keyField) {
      if (typeof value === "string") {
        acc.push(value);
      } else {
        const nestedValues = deepGetValueByKey(value, keyField);
        if (nestedValues) acc.push(...nestedValues);
      }
    } else {
      const nestedValues = deepGetValueByKey(value, keyField);
      if (nestedValues) acc.push(...nestedValues);
    }
    return acc;
  }, []);

  return results.length > 0 ? results : null;
}

// /**
//  * 创建防抖函数
//  * @param {Function} fun 原始函数
//  * @param {number} wait 延迟执行毫秒数
//  * @param {boolean} immediate  true 表立即执行，false 表非立即执行
//  * @return {Function} 返回 fun 的防抖函数
//  */
// export function debounce(fun: Function, wait: number = 500, immediate: boolean = false): Function {
//   // console.log('debounce create', fun.name);

//   let lastArgs, lastThis, result, timerId, lastCallTime;

//   if (typeof fun !== "function") {
//     throw new TypeError("Expected a function");
//   }

//   const later = function () {
//     // 据上一次触发时间间隔
//     const timeSinceLastCall = +new Date() - lastCallTime;

//     // 被包装函数上次被调用时间间隔 timeSinceLastCall 小于设定时间间隔 wait
//     if (timeSinceLastCall < wait && timeSinceLastCall > 0) {
//       timerId = setTimeout(later, wait - timeSinceLastCall);
//     } else {
//       timerId = null;
//       // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
//       if (!immediate) {
//         // console.log('debounce', fun.name, 'invoke');
//         result = fun.apply(lastThis, lastArgs);
//         if (!timerId) lastThis = lastArgs = null;
//       }
//     }
//   };

//   return function () {
//     // console.log('debounce call');
//     lastArgs = arguments;
//     lastThis = this;
//     lastCallTime = +new Date();

//     const callNow = immediate && !timerId;
//     // 如果延时不存在，重新设定延时
//     if (!timerId) timerId = setTimeout(later, wait);
//     if (callNow) {
//       // console.log('debounce', fun.name, 'invoke');
//       result = fun.apply(lastThis, lastArgs);
//       lastThis = lastArgs = null;
//     }

//     return result;
//   };
// }

// /**
//  * 创建节流函数
//  * @param {Function} fun 原始函数
//  * @param {number} limit 节流时间间隔，单位为毫秒
//  * @returns {Function} 返回 func 的节流函数
//  */
// export function throttle(fun, limit) {
//   // 标记是否处于节流状态
//   let inThrottle;
//   return function () {
//     // 获取函数调用时的参数
//     const args = arguments;
//     // 获取函数调用时的上下文
//     const context = this;
//     // 如果当前不处于节流状态
//     if (!inThrottle) {
//       // 调用原始函数，并传入上下文和参数
//       fun.apply(context, args);
//       // 进入节流状态
//       inThrottle = true;
//       // 在指定的时间间隔后，解除节流状态
//       setTimeout(() => (inThrottle = false), limit);
//     }
//   };
// }
