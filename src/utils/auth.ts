// 访问 token 缓存的 key
const ACCESS_TOKEN_KEY = "access_token";
// 刷新 token 缓存的 key
const REFRESH_TOKEN_KEY = "refresh_token";

function getToken(): string {
  return sessionStorage.getItem(ACCESS_TOKEN_KEY) || "";
}

function setToken(token: string) {
  sessionStorage.setItem(ACCESS_TOKEN_KEY, token);
}

function getRefreshToken(): string {
  return sessionStorage.getItem(REFRESH_TOKEN_KEY) || "";
}

function setRefreshToken(token: string) {
  sessionStorage.setItem(REFRESH_TOKEN_KEY, token);
}

function clearToken() {
  sessionStorage.removeItem(ACCESS_TOKEN_KEY);
  sessionStorage.removeItem(REFRESH_TOKEN_KEY);
}

export { getToken, setToken, clearToken, getRefreshToken, setRefreshToken };
