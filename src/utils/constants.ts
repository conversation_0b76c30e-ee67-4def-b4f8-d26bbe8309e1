import { kDebug } from ".";

export const response400MessageMappers = [
  { reg: new RegExp("^Invalid value of"), message: "参数无效" },
];

export const keywordField = "Keyword";
export const keywordRegExp = new RegExp("^[*+()（）.,，\u4e00-\u9fbb°a-zA-Z0-9_-]*$");
export const keywordWarnText = "关键字不能包含特殊字符";

/**自定义Header */
export const CUSTOM_HEADERS = {
  "hwkj-custom-client": import.meta.env.VITE_APP_CLIENT_ID,
  "hwkj-custom-clientVersion": kDebug ? "debug" : "2.0.0",
};
