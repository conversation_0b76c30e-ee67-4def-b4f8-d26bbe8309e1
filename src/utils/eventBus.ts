// src/utils/eventBus.ts
import { reactive } from "vue";

// 定义事件监听器函数类型
type EventListener<T = any> = (...args: T[]) => void;

// 定义监听器映射类型
interface EventListeners {
  [event: string]: EventListener[];
}

// 定义响应式状态类型
interface EventState {
  listeners: EventListeners;
}

// 定义EventBus接口
interface IEventBus {
  on(event: string, listener: EventListener): void;
  emit<T>(event: string, ...args: T[]): void;
  off(event: string, listener: EventListener): void;
}

const state = reactive<EventState>({
  listeners: {},
});

const EventBus: IEventBus = {
  on(event: string, listener: EventListener): void {
    if (!state.listeners[event]) {
      state.listeners[event] = [];
    }
    state.listeners[event].push(listener);
  },

  emit<T>(event: string, ...args: T[]): void {
    if (state.listeners[event]) {
      state.listeners[event].forEach((listener) => listener(...args));
    }
  },

  off(event: string, listener: EventListener): void {
    if (!state.listeners[event]) return;
    state.listeners[event] = state.listeners[event].filter((l) => l !== listener);
  },
};

export { EventBus };
export type { IEventBus, EventListener, EventListeners };
