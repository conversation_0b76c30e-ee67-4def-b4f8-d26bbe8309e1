import Report_Api from "@/api/report";
import Decimal from "decimal.js";
import { EventBus } from "./eventBus";
import { type ExportTaskRedashDTO, type ExportTaskInputDTO } from "@/api/report/types";
import { toRaw, unref } from "vue";

interface ExportColumn {
  label: string;
}

/** 计算单个医嘱的总价 */
export const calculateMedicalAdviceTotalPrice = (item: any): number => {
  let money = 0;
  switch (item.ChargeMode) {
    case 1: // 按穴位 单价*总次数*部位数
      money = item.Price * item.TotalCount * (item.Part ?? 1);
      break;
    case 2: // 按次 单价*总次数
      money = item.Price * item.TotalCount;
      break;
    case 3: // 按天 单价*天数
      money = item.Price * item.MoDay;
      break;
    case 4: // 按项目 单价*1
      money = item.Price * 1;
      break;
    case 5: // 按月 单价*月数
      money = item.Price * (item.MoMonth ?? 1);
      break;
    default:
      money = 0;
  }
  // 保留两位小数
  return new Decimal(money).toDecimalPlaces(2).toNumber();
};

/** 通过角色类型获取角色名称 */
export const getRoleNamesByTypes = (types: string[]): string[] => {
  const roleMap = {
    doctor: "医生",
    assistant: "医助",
    therapist: "治疗师",
  };
  return types.map((type) => roleMap[type as keyof typeof roleMap] || "未知角色");
};

/** 将undefined的数据转换为null */
export const convertUndefinedToNull = <T extends Record<string, any>>(baseParams: T): T => {
  // 此处单独处理，因为undefined会被自动过滤
  // 1. baseParams 可能是 ref 对象，需要 unref 获取其值
  // 2. baseParams 可能是响应式对象，需要 toRaw 获取原始值
  const rawParams = toRaw(unref(baseParams));

  // 递归处理函数
  const conversionValue = (value: any): any => {
    // 处理 undefined
    if (value === undefined) {
      return null;
    }

    // 处理数组
    if (Array.isArray(value)) {
      return value.map((item) => conversionValue(item));
    }

    // 处理对象
    if (value && typeof value === "object" && !(value instanceof Date)) {
      const result: Record<string, any> = {};
      Object.keys(value).forEach((key) => {
        result[key] = conversionValue(value[key]);
      });
      return result;
    }

    // 其他类型直接返回
    return value;
  };

  // 创建新的对象，避免修改原始数据
  return conversionValue({ ...rawParams });
};

/** 分页参数类型，支持大小写 */
type PageParams = {
  [K in "PageIndex" | "PageSize" | "pageIndex" | "pageSize"]?: number;
};

/** 将普通的数据转换为redash的参数 */
export const convertToRedashParams = <T extends PageParams>(
  baseParams: T,
  queryName: string
): RedashParamsInputDTO<T> => {
  if (!queryName) {
    throw new Error("queryName is required");
  }
  // 此处单独处理，因为undefined会被自动过滤
  // 1. baseParams 可能是 ref 对象，需要 unref 获取其值
  // 2. baseParams 可能是响应式对象，需要 toRaw 获取原始值
  const rawParams = toRaw(unref(baseParams));
  const params = Object.assign({}, rawParams);
  // 将params（对象）里面数据为null转化为'*' || 空数组转化为'*'
  Object.keys(params).forEach((key) => {
    if (
      params[key as keyof T] === null ||
      params[key as keyof T] === undefined ||
      (Array.isArray(params[key as keyof T]) && !(params[key as keyof T] as unknown[]).length)
    ) {
      params[key as keyof T] = "*" as any;
    }
    if (key.toLocaleUpperCase() === "KEYWORD" && !params[key as keyof T]) {
      params[key as keyof T] = "*" as any;
    }
    if (key.toLocaleUpperCase() === "KEYWORDS" && !params[key as keyof T]) {
      params[key as keyof T] = "*" as any;
    }
  });

  // 处理分页参数，支持大小写
  const pageIndex = params.PageIndex ?? params.pageIndex ?? 1;
  const pageSize = params.PageSize ?? params.pageSize ?? 10;

  // 删除分页参数，避免重复
  delete params.PageIndex;
  delete params.PageSize;
  delete params.pageIndex;
  delete params.pageSize;

  const redashParams: RedashParamsInputDTO<T> = {
    queryName,
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex,
    pageSize,
    // PageStart: pageIndex,
    // PageSize: pageSize,
    parameters: params,
  };
  return redashParams;
};

/** 导出 */
export const exportExcel = async (params: ExportTaskInputDTO | ExportTaskRedashDTO) => {
  const res = await Report_Api.createExportTask(params);
  if (res.Type === 200) {
    const notify = ElNotification.success({
      message: "<p>已加入下载任务，详情请在上方下载列表查看详情</p><br /><span>点击前往查看</span>",
      dangerouslyUseHTMLString: true,
      duration: 0,
      onClick: () => {
        EventBus.emit("triggerDownloadTask");
        notify.close();
      },
    });
  } else {
    ElNotification.warning(res.Content);
  }
};
/**
 * 获取导出列
 * @param exportArr 导出列
 * @param splitKey 分割方式（默认是@ 可以自定义）
 * @returns 导出列
 */
export const getExportCols = (exportArr: ExportColumn[], splitKey = "@"): string => {
  let reqString = "";
  // 将最前面的选择按钮列去掉以及最后的操作按钮列去掉
  const newExportArr = exportArr.filter((item) => item.label !== "操作" && item.label);
  newExportArr.forEach((element, index) => {
    if (element.label && element.label !== "操作") {
      const indexId = index;
      reqString +=
        element.label + splitKey + indexId + (index === newExportArr.length - 1 ? "" : ",");
    }
  });
  reqString += `&split=${splitKey}`;
  return reqString;
};

/** 判断是否为分页请求 */
export const isPageableRequest = (params: Record<string, any>): boolean => {
  return (
    params &&
    typeof params === "object" &&
    "PageIndex" in params &&
    "PageSize" in params &&
    "Pageable" in params
  );
};
