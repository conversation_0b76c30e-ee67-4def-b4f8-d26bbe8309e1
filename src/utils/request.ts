import axios, { type InternalAxiosRequestConfig, type AxiosResponse } from "axios";
import qs from "qs";
import { useUserStoreHook } from "@/store/modules/user";
import { getRefreshToken, getToken } from "@/utils/auth";
import router from "@/router";
import { kDebug } from ".";
import {
  CUSTOM_HEADERS,
  keywordField,
  keywordRegExp,
  keywordWarnText,
  response400MessageMappers,
} from "./constants";
import { deepGetValueByKey } from "./utils";
import AuthAPI from "@/api/auth";
import type { AxiosRequestConfig } from "axios";

let myKeywordField: RegExp = keywordRegExp;
export function setupKeywordRegexp(regExp: RegExp | null) {
  myKeywordField = regExp ?? keywordRegExp;
}

function getRequestParams(config: InternalAxiosRequestConfig) {
  let data;
  switch (config.method) {
    case "get":
      data = config.params;
      break;
    // 'PUT', 'POST', 'DELETE 和 'PATCH'
    case "post":
    case "put":
    case "patch":
    case "delete":
      data = config.data;
      // console.log('post 参数', data);
      if (typeof data === "string") {
        data = JSON.parse(data);
      }
      break;
  }
  return data;
}

// 创建 axios 实例
const service = axios.create({
  baseURL: kDebug ? import.meta.env.VITE_PROXY_API : import.meta.env.VITE_APP_API_URL,
  timeout: 15000,
  withCredentials: true,
  headers: { ...CUSTOM_HEADERS },
  validateStatus: (status) => {
    return status >= 200 && status < 400;
  },
  paramsSerializer: (params) => {
    return qs.stringify(params, { arrayFormat: "repeat" });
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = getToken();
    // 如果 Authorization 设置为 no-auth，则不携带 Token，用于登录、刷新 Token 等接口
    if (config.headers.Authorization != "no-auth" && accessToken) {
      config.headers.Authorization = "Bearer " + accessToken;
    }
    return config;
  }
  // (error) => Promise.reject(error)
);

/** 检查搜索关键字 */
service.interceptors.request.use(async (config) => {
  let data = getRequestParams(config);

  if (data) {
    // console.log('check 参数', data);
    const values = deepGetValueByKey(data, keywordField);
    // console.log('check 结果', values);
    if (values && values.length > 0) {
      for (const value of values) {
        if (!myKeywordField.test(value)) {
          throw new Error(keywordWarnText);
        }
      }
    }
  }

  return config;
});

// service.interceptors.request.use(async () => {
//   throw new Error("'request interceptor throw error");
// });

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // if (kDebug) {
    // 响应拦截器中抛出错误不会进入错误响应
    // throw new Error("'response interceptor throw error");
    // return Promise.reject("response interceptor reject");
    // }

    // 如果响应是二进制流，则直接返回，用于下载文件、Excel 导出等
    if (response.config.responseType === "blob") {
      return response;
    }

    if (response.config.headers.Authorization === "no-auth") {
      return response;
    }

    // console.log("响应", response.data);
    /* 确保 businessResponse 格式 */
    if (response.data == null) {
      return {
        Type: 200,
        Content: "无业务标准响应数据",
        Data: null,
        Log: "client 添加的包装",
      };
    } else if (response.data.Type == null && response.data.Data == null) {
      return {
        Type: 200,
        Content: "",
        Data: response.data,
        Log: "client 添加的包装",
      };
    }

    return response.data;

    // ElMessage.error(Content || "系统出错");
  },

  // eslint-disable-next-line complexity
  async (error: any) => {
    // console.error(error);

    let message: string = "系统错误";
    let type: "error" | "success" | "warning" | "info" = "error";
    if (error.response) {
      // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
      if (error.response.status === 401) {
        if (!error.config.isRetry) {
          // 刷新token
          const refresh_token = getRefreshToken();
          if (refresh_token) {
            // console.log('刷新token');

            const token = await AuthAPI.refreshToken(refresh_token).catch((e) => {
              console.warn(e);
              return false;
            });
            // console.log("刷新token", token);

            if (token) {
              // 重新发送
              const { config } = error;
              config.headers["Authorization"] = "Bearer " + token;
              config.isRetry = true;
              // console.log('重新发送', token, config);
              const response = await service.request(config).catch((e) => {
                console.warn(e);
                return { Type: 401, Content: "登录过期" };
              });
              return response;
            }
          }
        }
        ElNotification({
          title: "提示",
          message: "您的会话已过期，请重新登录",
          type: "info",
        });
        useUserStoreHook().logout();
        useUserStoreHook()
          .clearUserData()
          .then(() => {
            router.push("/login");
          });
        return { Type: 401, Content: "登录过期" };
      }

      switch (error.response.status) {
        case 400:
          message = error.response.data.Message || error.response.data.error_description;
          type = "error";
          for (let mapper of response400MessageMappers) {
            if (mapper.reg.test(message)) {
              message = mapper.message;
              break;
            }
          }
          break;
        case 403: // token 没有权限操作
          message = "您没有操作权限";
          type = "warning";
          break;
        case 413:
          message = "您上传的文件过大,请压缩文件再试试";
          type = "warning";
          break;
        case 429:
          message = "请求过多，请稍后再试";
          type = "warning";
          break;
        case 500:
          message = error.response.data.message || error.response.data.Message;
          type = "warning";
          break;
      }
    } else if (error.request) {
      // 请求已经成功发起，但没有收到响应
      // `error.request` 在浏览器中是 XMLHttpRequest 的实例，
      console.warn(error);
      message = "网络异常";
      type = "error";
    } else {
      // 发送请求时出了点问题
      if (error.message.includes("timeout")) {
        message = "请求超时, 请重试!";
        type = "warning";
      } else {
        message = error.message || "未知错误";
        type = "error";
      }
    }

    // ElNotification({
    //   message,
    //   type,
    // });
    ElMessage({
      message,
      type,
      duration: 5 * 1000,
    });

    return { Type: 4000, Content: message };
  }
);

//  get<T = any, R = AxiosResponse<T>, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
//   delete<T = any, R = AxiosResponse<T>, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
//   head<T = any, R = AxiosResponse<T>, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
//   options<T = any, R = AxiosResponse<T>, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
//   post<T = any, R = AxiosResponse<T>, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
//   put<T = any, R = AxiosResponse<T>, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
//   patch<T = any, R = AxiosResponse<T>, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
//   postForm<T = any, R = AxiosResponse<T>, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
//   putForm<T = any, R = AxiosResponse<T>, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
//   patchForm<T = any, R = AxiosResponse<T>, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
const request = {
  get<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>) {
    return service.get<T, ServerResult<T>>(url, config);
  },
  post<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>) {
    return service.post<T, ServerResult<T>>(url, data, config);
  },
  put<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>) {
    return service.put<T, ServerResult<T>>(url, data, config);
  },
  delete<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>) {
    return service.delete<T, ServerResult<T>>(url, config);
  },
  patch<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>) {
    return service.patch<T, ServerResult<T>>(url, data, config);
  },
  postForm<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>) {
    return service.postForm<T, ServerResult<T>>(url, data, config);
  },
  putForm<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>) {
    return service.putForm<T, ServerResult<T>>(url, data, config);
  },
  patchForm<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>) {
    return service.patchForm<T, ServerResult<T>>(url, data, config);
  },
};

export default request;
export const httpClient = service;

// // 刷新 Token 的锁
// let isRefreshing = false;
// // 因 Token 过期导致失败的请求队列
// let requestsQueue: Array<() => void> = [];

// // 刷新 Token 处理
// async function handleTokenRefresh(config: InternalAxiosRequestConfig) {
//   return new Promise((resolve) => {
//     const requestCallback = () => {
//       config.headers.Authorization = getToken();
//       resolve(service(config));
//     };

//     requestsQueue.push(requestCallback);

//     if (!isRefreshing) {
//       isRefreshing = true;

//       // 刷新 Token
//       refreshToken()
//         .then(() => {
//           // Token 刷新成功，执行请求队列
//           requestsQueue.forEach((callback) => callback());
//           requestsQueue = [];
//         })
//         .catch((error) => {
//           console.log("handleTokenRefresh error", error);
//           // Token 刷新失败，清除用户数据并跳转到登录
//           ElNotification({
//             title: "提示",
//             message: "您的会话已过期，请重新登录",
//             type: "info",
//           });
//           useUserStoreHook()
//             .clearUserData()
//             .then(() => {
//               router.push("/login");
//             });
//         })
//         .finally(() => {
//           isRefreshing = false;
//         });
//     }
//   });
// }
