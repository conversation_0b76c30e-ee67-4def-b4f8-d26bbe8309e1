import router from "./router";
import { useUserStore } from "./store";
import { kDebug } from "./utils";

const userStore = useUserStore();
const trustedOrigin: string = kDebug
  ? import.meta.env.VITE_DEV_OLD_ORIGIN
  : import.meta.env.VITE_APP_OLD_ORIGIN;

// 监听消息事件
window.addEventListener("message", function (event) {
  // console.log("receive message event from", event.origin);

  // 确保消息来自预期的源
  if (event.origin !== trustedOrigin) {
    return;
  }

  const message = event.data;
  console.log("Received message:", message, event.origin);

  switch (message.event) {
    case "login":
      console.log("自动登录");
      userStore.loginWithToken(message.token, message.refreshToken).catch((e) => {
        console.error("自动登录失败", e);
      });
      event.source?.postMessage({ event: "loginOk" }, { targetOrigin: event.origin });
      break;
    case "logout":
      userStore.logoutImmediately();
      event.source?.postMessage({ event: "logoutOk" }, { targetOrigin: event.origin });
      break;
    case "route":
      const path = message.path;
      // console.log("加载路由", path, message.query);
      router.push({ path, query: message.query });
      event.source?.postMessage({ event: "routeOk" }, { targetOrigin: event.origin });
      break;
    default:
      break;
  }
});
