import { type Directive } from "vue";

import { useUserStore } from "@/store";

// 定义按钮权限判断函数

const hasPermission = (value: string[]): boolean => {
  const roles = useUserStore().userInfo.Roles as string[];
  if (!roles || !roles.length) return false;
  return roles.some((role) => value.includes(role));
};

// 定义添加元素的函数

const addElement = (el: HTMLElement) => {
  el.style.display = "block";
};

// 定义移除元素的函数

const removeElement = (el: HTMLElement) => {
  el.style.display = "none";
};

// 定义自定义指令

const hasPermissionDirective: Directive = {
  // 当指令绑定元素挂载到DOM时调用

  mounted(el, binding) {
    const { value } = binding;

    if (!hasPermission(value)) {
      removeElement(el);
    }
  },

  // 当指令绑定元素的父组件更新时调用

  updated(el, binding) {
    const { value } = binding;

    const oldValue = binding.oldValue;

    if (value !== oldValue) {
      if (hasPermission(value)) {
        addElement(el);
      } else {
        removeElement(el);
      }
    }
  },
};

export default hasPermissionDirective;
