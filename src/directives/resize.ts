// import { debounce } from '@/utils';

import { type Directive } from "vue";

const resizeDirective: Directive = {
  mounted(el, binding) {
    // update 要高效，否则会导致 ResizeObserver loop completed with undelivered notifications.
    // 这里通过 setTimeout 延迟实际更新到下个 tick
    let update = (entry: ResizeObserverEntry) => {
      setTimeout(() => {
        binding.value(entry);
      }, 0);
    };
    if (binding.arg) {
      try {
        let delay = Number.parseInt(binding.arg);
        console.log(delay);
        // update = debounce(binding.value, delay);
      } catch (error) {
        console.log(error);
      }
    }
    // 创建 ResizeObserver 实例
    const resizeObserver = (el.__resizeObserver__ = new ResizeObserver((entries) => {
      // 当目标元素的大小发生变化时，执行回调函数
      update(entries[0]);
    }));
    // 开始监听目标元素的大小变化
    resizeObserver.observe(el);
  },
  unmounted(el) {
    el.__resizeObserver__.disconnect();
  },
};

export default resizeDirective;
