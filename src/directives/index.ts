// import hasPermission from "./hasPermission";
// import hasNoPermission from "./noPermission";
// // 自定义指令对象，用于遍历注册
// const directives = {
//   hasPermission,
//   hasNoPermission,
// };
// // 批量注册指令并暴露到main.js中去便于注册
// export default {
//   install(app: { directive: (arg0: string, arg1: any) => void }) {
//     Object.keys(directives).forEach((key) => {
//       app.directive(key, directives[key as keyof typeof directives]);
//     });
//   },
// };
import type { App } from "vue";

import hasPermission from "./hasPermission";
import hasNoPermission from "./noPermission";
import resize from "./resize";

// 全局注册 directive
export function setupDirective(app: App<Element>) {
  // 使 v-hasPerm 在所有组件中都可用
  app.directive("hasPermission", hasPermission);
  app.directive("hasNoPermission", hasNoPermission);
  app.directive("resize", resize);
}
