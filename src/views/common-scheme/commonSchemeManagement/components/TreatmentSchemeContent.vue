<template>
  <div ref="serviceContentRef" class="service-content">
    <el-tabs v-model="activeName" type="card" class="advice-tabs" @tab-click="handleTabsClick">
      <el-tab-pane label="方案内容" name="content">
        <el-form
          ref="formRef"
          :disabled="isOnlyPreview"
          :model="formData"
          :rules="rules"
          label-position="right"
          scroll-to-error
        >
          <!-- 类型选择 -->
          <el-row>
            <el-col :span="9">
              <el-form-item label="类型" prop="TreatType">
                <el-radio-group
                  v-model="formData.TreatType"
                  @change="(e) => handleTypeChange(e as number)"
                >
                  <el-radio :value="AdviceMoItemUseScope.Home">居家</el-radio>
                  <el-radio :value="AdviceMoItemUseScope.Community">线下</el-radio>
                  <el-radio :value="AdviceMoItemUseScope.Hospital">院内</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="方案名称" prop="PrescriptionName">
                <el-input v-model="formData.PrescriptionName" placeholder="请输入方案名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="执行天数" prop="ExecutDay">
                <el-input-number v-model="formData.ExecutDay" placeholder="请输入执行天数" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 用户选择 -->
          <FormItemContainer>
            <el-form-item label="机构">
              <HospitalSelect v-model="selectedOrganizationId" />
            </el-form-item>
            <el-form-item label="创建人" prop="CreatorId">
              <UserSelect
                ref="creatorSelectRef"
                v-model="formData.CreatorId"
                :org-ids="selectedOrganizationId ? [selectedOrganizationId] : null"
                :role-types="['doctor', 'nurse', 'therapist']"
                @change="handleCreatorChange"
              />
            </el-form-item>
            <el-form-item label="指导医生">
              <UserSelect
                v-model="formData.DoctorId"
                :role-types="['doctor']"
                :disabled="formData.Role === 'doctor'"
              />
            </el-form-item>
            <el-form-item label="指导治疗师">
              <UserSelect
                v-model="formData.TherapistId"
                :role-types="['therapist']"
                :disabled="formData.Role === 'therapist'"
              />
            </el-form-item>
            <el-form-item label="指导护士">
              <UserSelect
                v-model="formData.NurseId"
                :role-types="['nurse']"
                :disabled="formData.Role === 'nurse'"
              />
            </el-form-item>
          </FormItemContainer>
          <!-- 文本域 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="方案说明" prop="TherapistRemark">
                <el-input
                  v-model="formData.TherapistRemark"
                  type="textarea"
                  :maxlength="200"
                  show-word-limit
                  placeholder="请输入方案说明"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="部位">
                <el-select-v2
                  v-model="formData.Part"
                  filterable
                  multiple
                  collapse-tags
                  reserve-keyword
                  :max-collapse-tags="5"
                  placeholder="请选择部位"
                  :props="{ label: 'Key', value: 'Id' }"
                  :options="bodyPartList"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="西医诊断">
                <div class="diagnosis-input">
                  <el-tag
                    v-for="item in westernMedicineDiagnosis"
                    :key="item.DiagnoseCode"
                    class="mx-5px"
                    :closable="!isOnlyPreview"
                    @close="handleRemoveWesternDiagnosis(item)"
                  >
                    {{ item.DiagnoseName }}
                  </el-tag>
                  <el-button
                    v-if="!showWesternDiagnosisSelect"
                    type="primary"
                    @click="handleShowWesternDiagnosisSelect"
                  >
                    添加
                  </el-button>
                  <el-select
                    v-else
                    ref="westernDiagnosisSelectRef"
                    v-model="selectedWesternDiagnosisIds"
                    multiple
                    filterable
                    remote
                    :remote-method="handleWesternDiagnosisSearch"
                    placeholder="请选择西医诊断"
                    style="width: 240px"
                    @blur="handleWesternDiagnosisBlur"
                    @visible-change="handleWesternDiagnosisVisibleChange"
                  >
                    <el-option
                      v-for="item in westernDiagnosisList"
                      :key="item.Id"
                      :label="item.Key"
                      :value="item.Value!"
                    />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 中医诊断 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="中医诊断">
                <div class="diagnosis-input">
                  <el-tag
                    v-for="item in tCMDiagnosis"
                    :key="item.DiagnoseCode"
                    class="mx-5px"
                    :closable="!isOnlyPreview"
                    @close="handleRemoveTCMDiagnosis(item)"
                  >
                    {{ item.DiagnoseName }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item label="中医病证">
                <el-select
                  v-model="chineseMedicine.diseaseId"
                  filterable
                  remote
                  :remote-method="handleCMDiseaseSearch"
                  placeholder="请选择中医病证"
                  @change="handleCMDiseaseChange"
                >
                  <el-option
                    v-for="item in cmdDiseasesList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Value!"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="中医症候">
                <el-select
                  v-model="chineseMedicine.symptom"
                  placeholder="请选择中医症候"
                  filterable
                >
                  <el-option v-for="item in symptomList" :label="item.Key" :value="item.Value!" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="handleAddCMDisease">添加</el-button>
            </el-col>
          </el-row>
          <!-- 医嘱表格 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="medical-advice">
                <div class="advice-header">
                  <el-select
                    v-model="medicalAdvice"
                    placeholder="请选择医嘱"
                    filterable
                    :disabled="!formData.OrganizationId"
                    style="width: 280px"
                  >
                    <el-option
                      v-for="item in medicalAdviceList"
                      :label="item.Name"
                      :value="item.Id"
                    />
                  </el-select>
                  <el-button
                    type="primary"
                    class="ml-10px"
                    :disabled="!formData.OrganizationId"
                    @click="handleAddMedicalAdvice"
                  >
                    添加
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="medical-advice mt-10px">
                <div class="advice-header">
                  <el-select
                    v-model="servicePackId"
                    placeholder="请选择服务包"
                    filterable
                    remote
                    :remote-method="handleServicePackSearch"
                    :disabled="!formData.OrganizationId"
                    style="width: 280px"
                  >
                    <el-option
                      v-for="item in servicePackList"
                      :label="item.Name"
                      :value="item.Id"
                    />
                  </el-select>
                  <el-button
                    type="primary"
                    class="ml-10px"
                    :disabled="!formData.OrganizationId"
                    @click="handleAddServicePack"
                  >
                    添加
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form>
        <el-table
          :data="formData.PrescriptionDetailInputs"
          style="width: 100%; margin-top: 10px"
          align="center"
          border
        >
          <el-table-column prop="MoName" label="医嘱名称" align="center" fixed="left" />
          <el-table-column label="频次" align="center">
            <template #default="scope">
              <span v-if="scope.row.FreqDay && scope.row.Freq">
                {{ scope.row.FreqDay }}天 {{ scope.row.Freq }}次
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="MoDay" label="天数" align="center" />
          <el-table-column prop="TotalCount" label="总次数" align="center" />
          <el-table-column label="穴位" align="center" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.AcupointInputDtos && scope.row.AcupointInputDtos.length > 0">
                {{ scope.row.AcupointInputDtos.map((item: AcuPointInfo) => item.Name).join(",") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="训练动作" align="center" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.ActionInputDtos && scope.row.ActionInputDtos.length > 0">
                {{ scope.row.ActionInputDtos.map((item: MoItemAction) => item.Name).join(",") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="Price" label="单价" align="center" />
          <el-table-column prop="TotalPrice" label="金额" align="center" />
          <el-table-column label="操作" align="center" width="180px" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handleEditAdvice(scope.row)">
                {{ isOnlyPreview ? "查看" : "编辑" }}
              </el-button>
              <el-button
                link
                type="danger"
                :disabled="isOnlyPreview"
                @click="handleDeleteMedicalAdvice(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="total-amount">
          <span>合计金额：{{ totalAmount }}元</span>
        </div>
      </el-tab-pane>
      <el-tab-pane label="回院提醒" name="remind">
        <div ref="remindContainerRef" class="remind-container">
          <div v-if="rxTempBackRemind && rxTempBackRemind.length > 0" class="remind-list">
            <el-card
              v-for="(item, index) in rxTempBackRemind"
              :key="index"
              class="mb-20px remind-item"
            >
              <div class="remind-content">
                <div class="remind-row">
                  <el-form-item label="提醒类型" class="mb-0">
                    <el-radio-group v-model="item.Type" :disabled="isOnlyPreview">
                      <el-radio :label="1">出院</el-radio>
                      <el-radio :label="2">术后</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="提醒天数" class="mb-0 ml-20px">
                    <el-input-number
                      v-model="item.Day"
                      :min="1"
                      :precision="0"
                      :step="1"
                      :disabled="isOnlyPreview"
                    />
                  </el-form-item>
                  <el-button
                    type="danger"
                    link
                    :disabled="isOnlyPreview"
                    class="delete-btn"
                    @click="handleDeleteRemind(index)"
                  >
                    删除
                  </el-button>
                </div>
                <el-form-item label="备注" class="mb-0 mt-20px">
                  <el-input
                    v-model="item.Remark"
                    type="textarea"
                    :rows="2"
                    :disabled="isOnlyPreview"
                    placeholder="请输入备注信息"
                  />
                </el-form-item>
              </div>
            </el-card>
          </div>
          <el-empty v-else description="暂无提醒" />
          <div class="remind-header">
            <el-button type="primary" :disabled="isOnlyPreview" @click="handleAddRemind">
              添加提醒
            </el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      v-model="showDialog.general"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <SchemeGeneralAdviceContent
        ref="generalAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.general = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('generalAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.acupoint"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <SchemeAcupointAdviceContent
        ref="acupointAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.acupoint = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('acupointAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.scale"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <SchemeScaleAdviceContent
        ref="scaleAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
        :org-id="formData.OrganizationId"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.scale = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('scaleAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.consumables"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <SchemeConsumablesAdviceContent
        ref="consumablesAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.consumables = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('consumablesAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.missionary"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <SchemeMissionaryAdviceContent
        ref="missionaryAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
        :org-id="formData.OrganizationId"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.missionary = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('missionaryAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules, TabsPaneContext } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { AdviceMoItemMethod, AdviceMoItemUseScope } from "@/enums/AdviceEnum";
import {
  getWesternDiagnosisList,
  getCMDiseasesDictList,
  getCMDiseasesSymptomList,
  getBodyPartList,
} from "@/utils/dict";
import Content_Api from "@/api/content";
import {
  MoItemPageData,
  MoItemAction,
  MoItemPackItem,
  MoItemGroupItem,
  MoItemPackInputDTO,
} from "@/api/content/types";
import { calculateMedicalAdviceTotalPrice } from "@/utils/serviceUtils";
import {
  RxTempBackRemindItem,
  RxTemplateDetail,
  RxTemplateInputDTO,
  RxTemplateMoItemDetailInputDTO,
  RxTemplateMoItemInputDTO,
} from "@/api/consult/types";
import UserSelect from "@/components/UserSelect/index.vue";
import SchemeGeneralAdviceContent from "./SchemeGeneralAdviceContent.vue";
import SchemeScaleAdviceContent from "./SchemeScaleAdviceContent.vue";
import SchemeAcupointAdviceContent from "./SchemeAcupointAdviceContent.vue";
import SchemeConsumablesAdviceContent from "./SchemeConsumablesAdviceContent.vue";
import SchemeMissionaryAdviceContent from "./SchemeMissionaryAdviceContent.vue";
const generalAdviceContentRef = ref<InstanceType<typeof SchemeGeneralAdviceContent> | null>(null);
const scaleAdviceContentRef = ref<InstanceType<typeof SchemeScaleAdviceContent> | null>(null);
const acupointAdviceContentRef = ref<InstanceType<typeof SchemeAcupointAdviceContent> | null>(null);
const consumablesAdviceContentRef = ref<InstanceType<typeof SchemeConsumablesAdviceContent> | null>(
  null
);
const missionaryAdviceContentRef = ref<InstanceType<typeof SchemeMissionaryAdviceContent> | null>(
  null
);
const remindContainerRef = ref<HTMLElement>();
const serviceContentRef = ref<HTMLElement>();

interface PageRxTemplateMoItemInputDTO extends RxTemplateMoItemInputDTO {}

const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
const formRef = ref<FormInstance>();
const medicalAdviceDialogTitle = ref<string>("");
const activeName = ref<string>("content");
const medicalAdviceList = ref<MoItemPageData[]>([]);
let formData = reactive<PageRxTemplateMoItemInputDTO>({
  CreatorId: "",
  ExecutDay: 0,
  DoctorId: "",
  NurseId: "",
  TherapistId: "",
  OrganizationId: "",
  PrescriptionDetailInputs: [],
  PrescriptionName: "",
  Role: "",
  TherapistRemark: "",
  TreatType: 1,
  Part: [],
});
const rxTempBackRemind = ref<RxTempBackRemindItem[]>([]);
const dialogConfirmLoading = ref<boolean>(false);
const medicalAdvice = ref<string>("");
const selectedOrganizationId = ref<string | null>(null);
const creatorSelectRef = ref<InstanceType<typeof UserSelect> | null>(null);
const servicePackId = ref<string>("");
const servicePackList = ref<MoItemPackItem[]>([]);
const showDialog = reactive({
  general: false,
  acupoint: false,
  scale: false,
  consumables: false,
  missionary: false,
  setPrice: false,
});
const moItemId = ref<string>("");
const adviceMoItemInfo = ref<RxTemplateMoItemDetailInputDTO | null>(null);

// 西医诊断相关
const showWesternDiagnosisSelect = ref(false);
const westernDiagnosisSelectRef = ref();
const selectedWesternDiagnosisIds = ref<string[]>([]);
const westernDiagnosisList = ref<ReadDict[]>([]);
// 部位相关
const bodyPartList = ref<ReadDict[]>([]);

// 中医诊断相关
const chineseMedicine = reactive({
  diseaseId: "", // 中医病证ID
  symptom: "", // 中医症候
});
const cmdDiseasesList = ref<ReadDict[]>([]);
const symptomList = ref<ReadDict[]>([]);

// 诊断相关数据
const westernMedicineDiagnosis = ref<BaseDiagnosis[]>([]);
const tCMDiagnosis = ref<BaseDiagnosis[]>([]);

// 表单验证规则
const rules = reactive<FormRules>({
  PrescriptionName: [{ required: true, message: "请输入方案名称", trigger: "blur" }],
  ExecutDay: [
    { required: true, message: "请输入执行天数", trigger: "blur" },
    { type: "number", message: "执行天数必须为数字" },
  ],
  CreatorId: [{ required: true, message: "请选择创建人", trigger: "blur" }],
});

const handleTabsClick = (tab: TabsPaneContext) => {
  activeName.value = tab.paneName as string;
};
const handleAddServicePack = async () => {
  if (!servicePackId.value) {
    ElMessage.warning("请选择服务包");
    return;
  }
  const item = servicePackList.value.find((item) => item.Id === servicePackId.value);
  if (item?.Id) {
    const res = await Content_Api.getPackDetailByID({
      packId: item.Id,
      loadPackAmount: false,
    });
    if (res.Type === 200) {
      handleProcessPackDetailToRxTemplateDetail(res.Data);
    }
  }
};
const handleProcessPackDetailToRxTemplateDetail = (item: MoItemPackInputDTO) => {
  Object.assign(formData, {
    ...formData,
    ExecutDay: item.ExecutDay,
    PrescriptionDetailInputs: handleProcessPackAdviceDetail(item.MoItemGroupDetails, item.Id!),
    PrescriptionName: item.Name,
    TherapistRemark: item.PackExplain,
  });
  // 设置诊断
  if (item.MoItemPackDiagnosis && item.MoItemPackDiagnosis.length) {
    // 直接覆盖现在的tCMDiagnosis和westernMedicineDiagnosis
    tCMDiagnosis.value = item.MoItemPackDiagnosis.filter((v) => v.DiagnoseTypeName === "中医诊断");
    westernMedicineDiagnosis.value = item.MoItemPackDiagnosis.filter(
      (v) => v.DiagnoseTypeName === "西医诊断"
    );
  }
};
const handleProcessPackAdviceDetail = (
  lists: MoItemGroupItem[],
  packId: string
): RxTemplateMoItemDetailInputDTO[] => {
  const list: RxTemplateMoItemDetailInputDTO[] = [];
  lists.forEach((v) => {
    const item: RxTemplateMoItemDetailInputDTO = {
      MoItemId: v.MoItemId,
      MoName: v.MoName,
      MoRemark: v.MoRemark,
      MoDay: v.MoDay,
      Part: v.Part,
      Freq: v.Freq,
      TotalCount: v.TotalCount,
      Price: handleGetPriceAndTotalPrice(v).Price,
      FreqDay: v.FreqDay,
      IsSpecialFreq: v.IsSpecialFreq,
      MoItemMethod: v.MoItemMethod,
      MoItemUseScope: v.MoItemUseScope,
      LogisticsDay: v.LogisticsDay,
      ActionInputDtos: v.PackActions,
      AcupointInputDtos: v.PackAcupoints,
      ScaleInputDtos: v.PackScales,
      TotalPrice: handleGetPriceAndTotalPrice(v).TotalPrice,
      RecoveryMissionRelations: v.RecoveryMissionRelations?.map((item) => ({
        RecoveryMissionId: item,
        Id: "",
      })),
      MoItemChargeMode: v.ChargeMode,
      PackId: packId, // 这里必须传递 因为通过服务包转的就必须传递
    };
    list.push(item);
  });
  return list;
};
const handleGetPriceAndTotalPrice = (s: MoItemGroupItem) => {
  let signPrice = 0;
  if (s.ChargeItem && s.ChargeItem.PackChargeItemDetails?.length) {
    s.ChargeItem.PackChargeItemDetails.forEach((j) => {
      signPrice += j.Amount;
    });
    s.Price = signPrice;
  } else {
    s.Price = 0;
  }
  s.TotalPrice = calculateMedicalAdviceTotalPrice(s);
  return {
    Price: s.Price,
    TotalPrice: s.TotalPrice,
  };
};
const handleServicePackSearch = async (query: string) => {
  if (!query) return;
  const res = await Content_Api.getMoItemPackList({
    packType: formData.TreatType === 1 || formData.TreatType === 2 ? [1, 2] : [3],
    keywords: query,
    pageIndex: 1,
    pageSize: 9999,
    isEnable: true,
    orgId: formData.OrganizationId,
  });
  if (res.Type === 200) {
    servicePackList.value = res.Data.Data;
  } else {
    servicePackList.value = [];
  }
};

// 计算合计金额
const totalAmount = computed(() => {
  return formData.PrescriptionDetailInputs.reduce((sum, item) => sum + (item.TotalPrice || 0), 0);
});

const handleCreatorChange = async (value: string | string[] | null | undefined) => {
  formData.DoctorId = "";
  formData.TherapistId = "";
  formData.NurseId = "";
  if (!value) return;
  formData.CreatorId = value as string;
  const userOptions = creatorSelectRef.value?.userOptions;
  const selectUser = userOptions?.find((item) => item.Id === value);
  const selectUserOrgId = selectUser?.Organization?.Id!;
  if (formData.OrganizationId !== selectUserOrgId) {
    formData.PrescriptionDetailInputs = [];
  }
  formData.OrganizationId = selectUserOrgId;
  const selectUserRole = selectUser?.Roles as string[];
  handleRoleChange(selectUserRole, value);
  loadMedicalAdviceList();
  if (selectUser) {
    sessionStorage.setItem("schemeCreatorInfo", JSON.stringify(selectUser));
  }
};
const handleRoleChange = (selectUserRole: string[], value: string | string[] | null) => {
  const roleMapping = {
    doctor: "DoctorId",
    therapist: "TherapistId",
    nurse: "NurseId",
  };
  const role = Object.keys(roleMapping).find((role) => selectUserRole?.includes(role));
  if (role) {
    formData.Role = role;
  }
  switch (role) {
    case "doctor":
      formData.DoctorId = value as string;
      break;
    case "therapist":
      formData.TherapistId = value as string;
      break;
    case "nurse":
      formData.NurseId = value as string;
      break;
    default:
      break;
  }
};
/** 加载部位数据 */
const loadBodyPartList = async () => {
  const list = await getBodyPartList({
    PageSize: 9999,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
  });
  bodyPartList.value = list;
};

// 初始化加载西医诊断数据
const loadWesternDiagnosisList = async () => {
  const list = await getWesternDiagnosisList({
    PageSize: 30,
    Key: "",
    IsEnabled: true,
  });
  westernDiagnosisList.value = list;
};

// 显示西医诊断选择器
const handleShowWesternDiagnosisSelect = async () => {
  showWesternDiagnosisSelect.value = true;
  await loadWesternDiagnosisList();
  await nextTick();
  const select = westernDiagnosisSelectRef.value;
  select?.focus();
  if (select) {
    select.expanded = true;
  }
};

// 处理西医诊断搜索
const handleWesternDiagnosisSearch = async (query: string) => {
  if (query) {
    const list = await getWesternDiagnosisList({
      PageSize: 5000,
      Key: query,
      IsEnabled: true,
      IsPublish: true,
    });
    westernDiagnosisList.value = list;
  } else {
    await loadWesternDiagnosisList();
  }
};

// 处理西医诊断选择框失焦
const handleWesternDiagnosisBlur = () => {
  const selectedItems = selectedWesternDiagnosisIds.value
    .map((id) => westernDiagnosisList.value.find((item) => item.Value === id))
    .filter((item): item is ReadDict => item !== undefined);

  // 转换为BaseDiagnosis格式并添加
  const newDiagnoses = selectedItems.map((item) => ({
    DiagnoseTypeName: "西医诊断",
    DiagnoseName: item.Key!,
    DiagnoseCode: item.Value!,
    IsMain: westernMedicineDiagnosis.value.length === 0,
  }));

  // 过滤掉已存在的诊断
  const uniqueDiagnoses = newDiagnoses.filter(
    (newItem) =>
      !westernMedicineDiagnosis.value.some(
        (existingItem) => existingItem.DiagnoseCode === newItem.DiagnoseCode
      )
  );

  // 添加诊断
  westernMedicineDiagnosis.value.push(...uniqueDiagnoses);

  // 清空选择并隐藏select
  selectedWesternDiagnosisIds.value = [];
  showWesternDiagnosisSelect.value = false;
};

// 处理西医诊断下拉框显示状态变化
const handleWesternDiagnosisVisibleChange = (visible: boolean) => {
  if (!visible) {
    showWesternDiagnosisSelect.value = false;
  }
};

// 移除已选西医诊断
const handleRemoveWesternDiagnosis = (diagnosis: BaseDiagnosis) => {
  westernMedicineDiagnosis.value = westernMedicineDiagnosis.value.filter(
    (item) => item.DiagnoseCode !== diagnosis.DiagnoseCode
  );
};

const handleRemoveTCMDiagnosis = (diagnosis: BaseDiagnosis) => {
  tCMDiagnosis.value = tCMDiagnosis.value.filter(
    (item) => item.DiagnoseCode !== diagnosis.DiagnoseCode
  );
};

// 加载中医病证数据
const loadCMDiseasesList = async () => {
  const list = await getCMDiseasesDictList({
    PageSize: 50,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
  });
  cmdDiseasesList.value = list;
};

// 处理中医病证搜索
const handleCMDiseaseSearch = async (query: string) => {
  if (query) {
    const list = await getCMDiseasesDictList({
      PageSize: 500,
      Key: query,
      IsEnabled: true,
      IsPublish: true,
    });
    cmdDiseasesList.value = list;
  } else {
    await loadCMDiseasesList();
  }
};

// 处理中医病证选择
const handleCMDiseaseChange = async (value: string) => {
  // 找出源数据 items
  const item = cmdDiseasesList.value.find((item) => item.Value === value);
  let rules: {
    Field: string;
    Value: string;
    Operate: number;
  }[] = [];
  if (item?.DictItemRelateds && item?.DictItemRelateds.length) {
    var groups: {
      Field: string;
      Value: string;
      Operate: number;
    }[] = [];
    item?.DictItemRelateds.forEach((item) => {
      groups.push({
        Field: "Id",
        Value: item.DictItemIdA,
        Operate: 3,
      });
    });
    rules = groups;
  } else {
    rules = [];
  }
  chineseMedicine.symptom = "";
  const list = await getCMDiseasesSymptomList({
    PageSize: 9999,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
    Rules: rules,
  });
  symptomList.value = list;
};
/** 添加中医诊断 */
const handleAddCMDisease = () => {
  if (!chineseMedicine.symptom || !chineseMedicine.diseaseId) {
    ElMessage.warning("请选择中医病证和中医症候");
    return;
  }
  // 获取病症和症候数据
  const disease = cmdDiseasesList.value.find((item) => item.Value === chineseMedicine.diseaseId);
  const symptom = symptomList.value.find((item) => item.Value === chineseMedicine.symptom);
  const obj = {
    DiagnoseTypeName: "中医诊断",
    DiagnoseName: disease?.Key + "|" + symptom?.Key,
    DiagnoseCode: disease?.Value + "|" + symptom?.Value,
    IsMain: tCMDiagnosis.value.length === 1,
  };
  // 如果存在就不添加
  if (tCMDiagnosis.value.find((item) => item.DiagnoseCode === obj.DiagnoseCode)) return;
  tCMDiagnosis.value.push(obj);
};
/** 加载医嘱数据 */
const loadMedicalAdviceList = async () => {
  if (!formData.OrganizationId) return [];
  try {
    const res = await Content_Api.getMoItemPageData({
      isEnable: true,
      currentOrganizationId: formData.OrganizationId,
      isOrderBytBeReferenceNum: true,
      page: 1,
      pageSize: 9999,
      moItemUseScope: formData.TreatType === 1 || formData.TreatType === 2 ? [1, 2] : [3],
    });
    if (res.Type === 200) {
      medicalAdviceList.value = res.Data.Data;
    } else {
      medicalAdviceList.value = [];
      ElMessage.error(res.Content);
    }
  } catch (error) {
    ElMessage.error(JSON.stringify(error));
    medicalAdviceList.value = [];
  }
};
/** 设置下一页数据 */
const handleSendInfoToNextPageData = (item: MoItemPageData) => {
  adviceMoItemInfo.value = {
    MoItemId: item.Id,
    MoName: item.Name,
    MoRemark: item.Remark,
    MoDay: item.DefaultMoDay || 1,
    Freq: 1,
    TotalCount: 1,
    Price: item.MoItemAmount,
    FreqDay: 1,
    IsSpecialFreq: item.IsSpecialFreq,
    MoItemMethod: item.MoItemMethod,
    MoItemUseScope: item.MoItemUseScope,
    LogisticsDay: item.LogisticsDay,
    ChargeMode: item.ChargeMode,
    ChargeItem: item.ChargeItems[0],
    MaxDay: item.MaxDay,
    MinDay: item.MinDay,
  };
};

/** 验证医嘱选择 */
const validateMedicalAdvice = (item: MoItemPageData) => {
  if (!medicalAdvice.value) {
    ElMessage.error("请选择医嘱");
    return false;
  }

  const moItemUseScope = item.MoItemUseScope;
  if (
    formData.TreatType === AdviceMoItemUseScope.Home ||
    formData.TreatType === AdviceMoItemUseScope.Community
  ) {
    if (moItemUseScope !== 1 && moItemUseScope !== 2) {
      ElMessage.error("请选择使用范围为居家或线下的医嘱");
      return false;
    }
  } else if (moItemUseScope !== AdviceMoItemUseScope.Hospital) {
    ElMessage.error("请选择使用范围为院内的医嘱");
    return false;
  }

  if (formData.PrescriptionDetailInputs.some((s) => s.MoItemId === item.Id)) {
    ElMessage.error("已经选择的医嘱不能再添加");
    return false;
  }
  return true;
};

/** 处理医嘱方法选择 */
const handleMedicalAdviceMethod = (item: MoItemPageData) => {
  medicalAdviceDialogTitle.value = item.Name;
  moItemId.value = item.Id;

  switch (item.MoItemMethod) {
    case AdviceMoItemMethod.General:
      handleSendInfoToNextPageData(item);
      showDialog.general = true;
      break;
    case AdviceMoItemMethod.Acupoint:
      handleSendInfoToNextPageData(item);
      showDialog.acupoint = true;
      break;
    case AdviceMoItemMethod.Consultation:
      handleConsultationAdvice(item);
      break;
    case AdviceMoItemMethod.Evaluation:
      handleSendInfoToNextPageData(item);
      showDialog.scale = true;
      break;
    case AdviceMoItemMethod.Equipment:
      ElMessage.info("暂未支持辅具类医嘱");
      break;
    case AdviceMoItemMethod.Consumables:
      handleSendInfoToNextPageData(item);
      showDialog.consumables = true;
      break;
    case AdviceMoItemMethod.Education:
      handleSendInfoToNextPageData(item);
      showDialog.missionary = true;
      break;
  }
};

/** 处理咨询类医嘱 */
const handleConsultationAdvice = (item: MoItemPageData) => {
  const obj: RxTemplateMoItemDetailInputDTO = {
    MoDay: 1,
    Freq: 1,
    TotalCount: 1,
    FreqDay: 1,
    Price: item.MoItemAmount,
    MoItemId: item.Id,
    MoName: item.Name,
    ChargeMode: item.ChargeMode,
    Part: 1,
    MoItemMethod: item.MoItemMethod,
    MoRemark: item.Remark,
    IsSpecialFreq: item.IsSpecialFreq,
    MoItemUseScope: item.MoItemUseScope,
    LogisticsDay: item.LogisticsDay,
    ChargeItem: item.ChargeItems[0],
  };
  obj.TotalPrice = calculateMedicalAdviceTotalPrice(obj as any);
  handleTransformServicePackageData(obj);
};

/** 添加医嘱 */
const handleAddMedicalAdvice = () => {
  const item = medicalAdviceList.value.find((item) => item.Id === medicalAdvice.value)!;
  const isExistHospital = formData.PrescriptionDetailInputs.some(
    (s) => s.MoItemUseScope === AdviceMoItemUseScope.Hospital
  );
  if (formData.PrescriptionDetailInputs.length) {
    if (
      (isExistHospital && formData.TreatType !== AdviceMoItemUseScope.Hospital) ||
      (!isExistHospital && formData.TreatType === AdviceMoItemUseScope.Hospital)
    ) {
      ElMessage.error("院内医嘱不能和居家、线下医嘱同时存在");
      return;
    }
  }
  if (!validateMedicalAdvice(item)) return;
  handleMedicalAdviceMethod(item);
};
/** 编辑医嘱 */
const handleEditAdvice = (item: RxTemplateMoItemDetailInputDTO) => {
  moItemId.value = item.MoItemId;
  adviceMoItemInfo.value = item;
  switch (item.MoItemMethod) {
    case AdviceMoItemMethod.General:
      showDialog.general = true;
      break;
    case AdviceMoItemMethod.Acupoint:
      showDialog.acupoint = true;
      break;
    case AdviceMoItemMethod.Consultation:
      break;
    case AdviceMoItemMethod.Evaluation:
      showDialog.scale = true;
      break;
    case AdviceMoItemMethod.Equipment:
      ElMessage.info("暂未支持辅具类医嘱");
      break;
    case AdviceMoItemMethod.Consumables:
      showDialog.consumables = true;
      break;
    case AdviceMoItemMethod.Education:
      showDialog.missionary = true;
      break;
  }
};

// 计算执行天数
const handleSetExecutDay = () => {
  // 重新计算执行天数 将MoItemGroupDetails中MoItemMethod != 2的筛选出来之后，获取最大的MoDay
  const maxMoDay = formData.PrescriptionDetailInputs.filter(
    (item) => item.MoItemMethod !== 2
  ).reduce((max, item) => Math.max(max, item.MoDay), 0);
  formData.ExecutDay = maxMoDay;
};
// 重新更新咨询类医嘱的天数（取所有医嘱的最大天数）
const handleSetAdvisoryMoItemInfo = () => {
  // 如果有MoItemMethod == 2的数据 将MoItemGroupDetails中MoItemMethod ！= 2的筛选出来之后，获取最大的MoDay 然后赋值给MoItemMethod == 2的数据
  const advisoryMoItem = formData.PrescriptionDetailInputs.find((item) => item.MoItemMethod === 2);
  const maxMoDay = formData.PrescriptionDetailInputs.filter(
    (item) => item.MoItemMethod !== 2
  ).reduce((max, item) => Math.max(max, item.MoDay), 0);
  if (advisoryMoItem) {
    advisoryMoItem.MoDay = maxMoDay;
  }
};
// 对新增或者编辑的常用方案数据进行转化为该页面的数据结构
const handleTransformServicePackageData = (data: RxTemplateMoItemDetailInputDTO) => {
  // 看MoItemGroupDetails中是否存在 some
  const index = formData.PrescriptionDetailInputs.findIndex(
    (item) => item.MoItemId === data.MoItemId
  );
  if (index !== -1) {
    // 如果存在就更新（直接把 data 覆盖源数据）
    formData.PrescriptionDetailInputs[index] = data;
  } else {
    // 不存在就添加
    formData.PrescriptionDetailInputs.push(data);
  }
  // 重新计算执行天数
  handleSetExecutDay();
  // 重新更新咨询类医嘱的天数（取所有医嘱的最大天数）
  handleSetAdvisoryMoItemInfo();
};
const dialogRefs = {
  generalAdviceContentRef: { ref: generalAdviceContentRef, dialog: "general" },
  acupointAdviceContentRef: { ref: acupointAdviceContentRef, dialog: "acupoint" },
  scaleAdviceContentRef: { ref: scaleAdviceContentRef, dialog: "scale" },
  consumablesAdviceContentRef: { ref: consumablesAdviceContentRef, dialog: "consumables" },
  missionaryAdviceContentRef: { ref: missionaryAdviceContentRef, dialog: "missionary" },
} as const;

const handleGetNextData = async (refName: keyof typeof dialogRefs) => {
  const { ref, dialog } = dialogRefs[refName];
  const params = await ref.value?.handleSubmit();

  if (params) {
    params.TotalPrice = calculateMedicalAdviceTotalPrice(params as any);
    handleTransformServicePackageData(params);
    showDialog[dialog] = false;
  }
};
const handleDeleteMedicalAdvice = (row: RxTemplateMoItemDetailInputDTO) => {
  if (row.PackId) {
    const packNames = formData.PrescriptionDetailInputs.filter(
      (item) => item.PackId === row.PackId
    ).map((item) => item.MoName);
    ElMessageBox.confirm(`${packNames}为绑定项目，是否确认一起删除？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        // 从数组中移除指定医嘱
        formData.PrescriptionDetailInputs = formData.PrescriptionDetailInputs.filter(
          (item) => !item.PackId || item.PackId !== row.PackId
        );
        // 重新计算执行天数
        handleSetExecutDay();
        // 重新更新咨询类医嘱的天数
        handleSetAdvisoryMoItemInfo();
        ElMessage.success("删除成功");
      })
      .catch(() => {
        // 取消删除操作
      });
    return;
  }
  ElMessageBox.confirm("确定要删除该医嘱吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 从数组中移除指定医嘱
      formData.PrescriptionDetailInputs = formData.PrescriptionDetailInputs.filter(
        (item) => item.MoItemId !== row.MoItemId
      );
      // 重新计算执行天数
      handleSetExecutDay();
      // 重新更新咨询类医嘱的天数
      handleSetAdvisoryMoItemInfo();
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除操作
    });
};
const handleTransformSchemeSendData = (
  data: PageRxTemplateMoItemInputDTO | null
): RxTemplateInputDTO | null => {
  if (!data) return null;
  const copyData: RxTemplateMoItemInputDTO = JSON.parse(JSON.stringify(data));
  const moItemGroupDetails = copyData.PrescriptionDetailInputs.map((element) => {
    if (element.ActionInputDtos && element.ActionInputDtos.length) {
      element.ActionInputDtos.forEach((item) => {
        if (item.InstrumentParameter) {
          const instrumentParameter = JSON.parse(item.InstrumentParameter);
          instrumentParameter.forEach((item: BaseInstrumentParameter) => {
            delete item.NotShow;
            delete item.OptionValue;
          });
          item.InstrumentParameter = JSON.stringify(instrumentParameter);
        }
      });
    }
    if (element.ChargeMode) {
      element.MoItemChargeMode = element.ChargeMode;
      delete element.ChargeMode;
    }
    if (element.Consumables && element.Consumables.length) {
      element.PrescriptionConsumableInputDtos = element.Consumables.map((item) => {
        return {
          ConsumableId: item,
        };
      });
    }
    return element;
  });
  if (copyData.Id) {
    copyData.RxTempId = copyData.Id; // RxTempId是当修改的时候传递的
  }
  let rxTempBackRemindList = JSON.parse(JSON.stringify(rxTempBackRemind.value)) || [];
  const userOptions = creatorSelectRef.value!.userOptions!;
  rxTempBackRemindList.forEach((s: RxTempBackRemindItem) => {
    if (!s.DctId) {
      s.DctId = copyData.CreatorId;
    }
    if (!s.DeptId) {
      s.DeptId =
        (userOptions?.find((item) => item.Id === copyData.CreatorId) as any)?.Department?.Id || "";
    }
    s.Name = s.Name ?? "";
  });
  copyData.PrescriptionDetailInputs = moItemGroupDetails;
  const newData: RxTemplateInputDTO = {
    IsTemp: true,
    PrescriptionInput: copyData,
    RxTempBackRemind: rxTempBackRemindList,
    RxTempDiagnoses: [...westernMedicineDiagnosis.value, ...tCMDiagnosis.value],
  };
  return newData;
};
/** 类型改变 */
const handleTypeChange = (e: number) => {
  medicalAdvice.value = "";
  // 重新加载医嘱数据
  loadMedicalAdviceList();
};
// 验证表单数据
const validateFormData = (): boolean => {
  if (!handleCheckBaseData()) return false;
  if (props.schemeDetail?.Id && formData.CreatorId !== props.schemeDetail?.CreatorId) {
    ElMessage.warning("编辑的创建人不可修改！");
    return false;
  }
  if (rxTempBackRemind.value && rxTempBackRemind.value.length) {
    // 同提醒类型同提醒天数只能有一条
    const isHaveSameRemind = checkDuplicateReminders(rxTempBackRemind.value);
    if (isHaveSameRemind) {
      ElMessage.warning("同提醒类型同提醒天数只能有一条");
      return false;
    }
  }
  return true;
};

const handleCheckBaseData = (): boolean => {
  if (!formRef.value) return false;
  if (!formData.PrescriptionDetailInputs || !formData.PrescriptionDetailInputs.length) {
    ElMessage.error("请添加医嘱");
    return false;
  }
  if (!tCMDiagnosis.value.length && !westernMedicineDiagnosis.value.length) {
    ElMessage.error("请添加西医诊断或中医诊断");
    return false;
  }
  const isHaveHospital = formData.PrescriptionDetailInputs.some((s) => s.MoItemUseScope === 3);
  if (
    (isHaveHospital && formData.TreatType !== 3) ||
    (!isHaveHospital && formData.TreatType === 3)
  ) {
    // 如果有院内并且选择的医嘱类型不是院内. 如果没有院内但是类型选择了院内
    ElMessage.warning("医嘱类型和方案类型不符");
    return false;
  }
  return true;
};

// 检查是否有重复的提醒
const checkDuplicateReminders = (reminders: RxTempBackRemindItem[]): boolean => {
  // 使用Map存储已见过的类型和天数组合
  const typeAndDaySet = new Map<string, boolean>();

  for (const item of reminders) {
    const key = `${item.Type}-${item.Day}`;
    if (typeAndDaySet.has(key)) {
      return true; // 发现重复
    }
    typeAndDaySet.set(key, true);
  }

  return false;
};

// 表单提交
const handleSubmit = async (): Promise<RxTemplateInputDTO | null> => {
  if (!validateFormData()) return null;

  try {
    await formRef.value!.validate();
    const data = handleTransformSchemeSendData(formData);
    return data;
  } catch {
    return null;
  }
};
const handleProcessPackDetail = (
  data: RxTemplateDetail | null
): PageRxTemplateMoItemInputDTO | null => {
  if (!data) return null;
  const copyData: RxTemplateDetail = JSON.parse(JSON.stringify(data));
  if (copyData.RxTempDiagnosisOutputDtos && copyData.RxTempDiagnosisOutputDtos.length) {
    copyData.RxTempDiagnosisOutputDtos.forEach((v) => {
      if (v.DiagnoseTypeName === "中医诊断") {
        tCMDiagnosis.value.push(v);
      } else if (v.DiagnoseTypeName === "西医诊断") {
        westernMedicineDiagnosis.value.push(v);
      }
    });
  }
  // 计算每个医嘱的单价
  copyData.RxDetailTemplateOutputDtos.forEach((s) => {
    s.ChargeMode = s.MoItemChargeMode;
    s.TotalPrice = calculateMedicalAdviceTotalPrice(s);
    delete s.ChargeMode;
  });
  const prescriptionDetailInputs: RxTemplateMoItemDetailInputDTO[] =
    copyData.RxDetailTemplateOutputDtos.map((s) => {
      return {
        MoItemId: s.MoItemId,
        MoItemUseScope: s.MoItemUseScope,
        MoItemMethod: s.MoItemMethod,
        MoDay: s.MoDay,
        Freq: s.Freq,
        TotalCount: s.TotalCount,
        Price: s.Price,
        FreqDay: s.FreqDay,
        IsSpecialFreq: s.IsSpecialFreq,
        MoName: s.MoName,
        MoRemark: s.MoRemark,
        LogisticsDay: s.LogisticsDay,
        MoItemChargeMode: s.MoItemChargeMode,
        ChargeMode: s.ChargeMode,
        Part: s.Part,
        TotalPrice: s.TotalPrice,
        MaxDay: s.MaxDay,
        MinDay: s.MinDay,
        ScaleInputDtos: s.Scales,
        ActionInputDtos: s.RxActionTemplateOutputDtos,
        AcupointInputDtos: s.AcuPoints,
        RecoveryMissionRelations: s.RecoveryMissionRelations,
        Manufacturer: s.Manufacturer,
        MoMonth: s.MoMonth,
        Consumables: s.RxTempConsumableOutputDtos?.map((item) => item.ConsumableId) || [],
        PackId: s.PackId,
      };
    });
  if (copyData.RxTempBackRemind && copyData.RxTempBackRemind.length) {
    rxTempBackRemind.value = copyData.RxTempBackRemind;
  }
  return {
    CreatorId: copyData.CreatorId,
    ExecutDay: copyData.ExecutDay,
    DoctorId: copyData.DoctorId,
    NurseId: copyData.NurseId,
    TherapistId: copyData.TherapistId,
    OrganizationId: copyData.OrganizationId,
    PrescriptionDetailInputs: prescriptionDetailInputs,
    PrescriptionName: copyData.PrescriptionName,
    Role: copyData.Role,
    RxTempId: copyData.Id,
    TherapistRemark: copyData.TherapistRemark,
    TreatType: copyData.TreatType,
    Part: copyData.Part || [],
  };
};

const handleInitFormData = () => {
  const creatorInfo = JSON.parse(sessionStorage.getItem("schemeCreatorInfo") as string);
  formData.CreatorId = creatorInfo.Id;
  formData.OrganizationId = creatorInfo.Organization.Id;
  handleRoleChange(creatorInfo.Roles as string[], creatorInfo.Id);
  loadMedicalAdviceList();
};
interface Props {
  schemeDetail: RxTemplateDetail | null;
}
const props = defineProps<Props>();
watch(
  () => props.schemeDetail,
  (newVal) => {
    if (!props.schemeDetail?.CreatorId && sessionStorage.getItem("schemeCreatorInfo")) {
      handleInitFormData();
    }
    const data = handleProcessPackDetail(newVal);
    if (data) {
      Object.assign(formData, data);
    }
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
// 初始化时加载数据
onMounted(() => {
  loadWesternDiagnosisList();
  loadCMDiseasesList();
  // 加载医嘱数据
  loadMedicalAdviceList();
  // 加载部位数据
  loadBodyPartList();
});

// 添加提醒
const handleAddRemind = () => {
  if (!rxTempBackRemind.value) {
    rxTempBackRemind.value = [];
  }
  rxTempBackRemind.value.push({
    Type: 1,
    Day: 1,
    Remark: "",
    DctId: "",
    DeptId: "",
  });
  // 等待DOM更新后滚动到底部
  nextTick(() => {
    if (serviceContentRef.value) {
      serviceContentRef.value.scrollTo({
        top: serviceContentRef.value.scrollHeight,
        behavior: "smooth",
      });
    }
  });
};

// 删除提醒
const handleDeleteRemind = (index: number) => {
  if (rxTempBackRemind.value) {
    rxTempBackRemind.value.splice(index, 1);
  }
};
</script>

<style lang="scss" scoped>
.advice-tabs {
  :deep(.el-tabs__header) {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: var(--el-bg-color);
  }
}
.service-content {
  padding: 20px;
  height: 500px;
  overflow-y: auto;
  width: 100%;
  padding-top: 0px !important;
}

.remind-container {
  padding: 0 20px;
  position: relative;

  .remind-header {
    padding: 20px 0;
    z-index: 10;
    width: 100%;
    border-bottom: 1px solid #ebeef5;
  }

  .remind-list {
    padding-top: 20px;
    padding-bottom: 20px;

    .remind-item {
      transition: all 0.3s;
      margin-bottom: 20px;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .remind-content {
        .remind-row {
          display: flex;
          align-items: center;

          .delete-btn {
            margin-left: auto;
          }
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.mb-0 {
  margin-bottom: 0;
}

.ml-20px {
  margin-left: 20px;
}

.mt-20px {
  margin-top: 20px;
}

.total-amount {
  margin-top: 10px;
  text-align: left;
  font-weight: bold;

  span {
    margin-left: 20px;
  }
}

.disease-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 32px;
}

.mx-5px {
  margin: 0 5px;
}
</style>
