<template>
  <div class="container">
    <div class="flex items-center justify-start">
      <span>总次数</span>
      <span class="text-red-500">*</span>
      <el-input-number
        v-model="formData.TotalCount"
        :step="1"
        :disabled="isOnlyPreview"
        step-strictly
        :min="1"
        :max="99"
        class="m-x-10px"
      />
      <span class="m-x-10px">耗材</span>
      <el-select
        v-model="formData.Consumables"
        :disabled="isOnlyPreview"
        placeholder="请选择耗材"
        multiple
        :multiple-limit="1"
        style="width: 150px"
      >
        <el-option
          v-for="item in consumableList"
          :key="item.Id"
          :label="item.Spec"
          :value="item.Id"
        />
      </el-select>
    </div>
  </div>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { RxTemplateMoItemDetailInputDTO } from "@/api/consult/types";
const consumableList = ref<BaseConsumables[]>([]);
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
const formData = reactive<RxTemplateMoItemDetailInputDTO>({
  MoDay: 1,
  FreqDay: 1,
  Freq: 1,
  TotalCount: 1,
  Consumables: [],
  MoItemId: "",
  MoName: "",
  MoRemark: "",
  Price: 0,
  IsSpecialFreq: false,
  MoItemMethod: 0,
  MoItemUseScope: 0,
  LogisticsDay: 0,
});

const handleFetchConsumablesListData = async () => {
  const res = await Content_Api.getMoItemByIds({
    Ids: [props.moItemId],
  });
  if (res.Type === 200) {
    if (res.Data[0].ConsumablesOutputDtos && res.Data[0].ConsumablesOutputDtos.length) {
      handleGetConsumablesListData(res.Data[0].ConsumablesOutputDtos[0].Type);
      if (!props.info?.Consumables || !props.info?.Consumables.length) {
        formData.Consumables = [res.Data[0].ConsumablesOutputDtos[0].Id];
      }
    }
  } else {
    consumableList.value = [];
  }
};
const handleGetConsumablesListData = async (type: string) => {
  const res = await Content_Api.getConsumablesPageData({
    type,
    page: 1,
    pageSize: 1000,
    keywords: "",
    isEnable: true,
  });
  if (res.Type === 200) {
    consumableList.value = res.Data.Data;
  }
};
const handleSubmit = (): RxTemplateMoItemDetailInputDTO => {
  return {
    MoItemId: formData.MoItemId,
    MoName: formData.MoName,
    MoRemark: formData.MoRemark,
    MoDay: formData.MoDay || 1,
    Freq: formData.Freq || 1,
    Part: formData.Part || 1,
    TotalCount: formData.TotalCount,
    FreqDay: formData.FreqDay || 1,
    IsSpecialFreq: formData.IsSpecialFreq,
    MoItemMethod: formData.MoItemMethod,
    MoItemUseScope: formData.MoItemUseScope,
    LogisticsDay: formData.LogisticsDay,
    AcupointInputDtos: [],
    ActionInputDtos: [],
    ScaleInputDtos: [],
    Price: formData.Price,
    ChargeMode: formData.ChargeMode,
    ChargeItem: formData.ChargeItem,
    Consumables: formData.Consumables,
    PackId: formData.PackId,
    MinDay: formData.MinDay,
    MaxDay: formData.MaxDay,
  };
};
interface Props {
  moItemId: string;
  info: RxTemplateMoItemDetailInputDTO | null;
}
const props = defineProps<Props>();
watch(
  () => props.info,
  (newVal) => {
    if (newVal) {
      console.log("newVal", newVal);
      // 使用 Object.assign 更新属性，保持响应式
      Object.assign(formData, newVal);
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmit,
});
onMounted(() => {
  handleFetchConsumablesListData();
});
</script>

<style lang="scss" scoped>
.container {
  height: 400px;
  overflow-y: auto;
  width: 100%;
  padding: 20px;
}
</style>
