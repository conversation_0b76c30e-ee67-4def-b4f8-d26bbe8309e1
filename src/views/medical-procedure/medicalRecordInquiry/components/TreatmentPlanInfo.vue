<template>
  <div class="overflow-y-auto h-500px">
    <el-col>
      <div class="title">治疗费</div>
      <el-table
        class="mb-10px"
        :data="rxDetails"
        row-key="Id"
        :header-cell-style="{ textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }"
        max-height="400px"
        border
        highlight-current-row
      >
        <el-table-column prop="MoName" label="医嘱名称" min-width="120px" />
        <el-table-column label="频次" min-width="100px">
          <template #default="scope">{{ scope.row.FreqDay }}天{{ scope.row.Freq }}次</template>
        </el-table-column>
        <el-table-column prop="MoDay" label="天数" min-width="80px" />
        <el-table-column prop="Part" label="部位数" min-width="80px" />
        <el-table-column prop="TotalCount" label="总次数" min-width="80px" />
        <el-table-column prop="Price" label="单价" min-width="80px" />
        <el-table-column label="金额" min-width="80px">
          <template #default="scope">￥{{ getAmount(scope.row) }}</template>
        </el-table-column>
      </el-table>
      <div class="title">设备费</div>
      <el-table
        :data="orderDetails"
        row-key="Id"
        :header-cell-style="{ textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }"
        max-height="400px"
        border
        highlight-current-row
      >
        <el-table-column prop="Title" label="设备名称" />
        <el-table-column prop="Price" label="押金" width="200px" />
      </el-table>
    </el-col>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

const kEnableDebug = true;
defineOptions({
  name: "TreatmentPlanInfo",
});

const props = defineProps<{
  data: ConsultRecordInfo;
}>();

const rxDetails = computed(() => props.data.RxDetails ?? []);
const orderDetails = computed(() => props.data.Order?.OrderDetails ?? []);

const getAmount = (item: BaseRxMoItem) => {
  const price = item.Price ?? 0;
  const totalCount = item.TotalCount ?? 0;
  if (item.MoItemChargeMode === 2) {
    // 按次数
    return price * totalCount;
  } else if (item.MoItemChargeMode === 1) {
    // 按部分
    return price * totalCount * (item.Part ?? 0);
  } else if (item.MoItemChargeMode === 3) {
    // 按天
    return price * (item.MoDay ?? 0);
  } else {
    return price;
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: 600;
  margin: 20px 0 10px;
}
</style>
