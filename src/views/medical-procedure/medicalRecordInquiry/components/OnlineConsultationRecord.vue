<template>
  <div class="overflow-y-auto h-500px">
    <MessageList v-if="msgList.length" :session-users="sessionUsers" :msg-list="msgList" />
    <div v-else class="empty-chat">暂无聊天记录</div>
  </div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";

const sessionUsers = ref<{ [key: string]: PageBaseUserProfile }>({});
const msgList = ref<RoomMsg[]>([]);

const getMessageList = async () => {
  if (!props.consultId) return;

  try {
    const res = await Consult_Api.getRoomMsgs(props.consultId);
    if (res.Type === 200 && res.Data) {
      const user: PageBaseUserProfile[] = res.Data.Users;
      const tempSessionUsers: { [key: string]: PageBaseUserProfile } = {};
      if (user.length) {
        user.forEach((item) => {
          const userRole = res.Data.RoomInfo?.Members.find((v) => v.UserGuid === item.Id)
            ?.ExpContent?.WorkRole;
          item.UserRole = userRole;
          tempSessionUsers[item.Id] = item;
        });
      }
      sessionUsers.value = tempSessionUsers;
      msgList.value = res.Data.Msg || [];
    }
  } catch (error) {
    console.log(error);
  }
};

const props = defineProps<{
  consultId?: string | undefined;
}>();
watch(
  () => props.consultId,
  (newVal) => {
    if (newVal) {
      getMessageList();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped></style>
