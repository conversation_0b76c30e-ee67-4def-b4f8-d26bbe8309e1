<template>
  <div v-loading="tabsLoading" class="p-20px overflow-y-auto">
    <el-tabs type="card">
      <template v-if="props.data.ConsultWay === 1">
        <el-tab-pane label="患者症状">
          <PatientSymptoms :data="tabsData" />
        </el-tab-pane>
        <el-tab-pane label="病历">
          <MedicalRecordInfo :data="tabsData" />
        </el-tab-pane>
      </template>
      <el-tab-pane label="治疗方案">
        <TreatmentPlanInfo :data="tabsData" />
      </el-tab-pane>
      <el-tab-pane label="在线问诊记录">
        <OnlineConsultationRecord :consult-id="props.data.Id" />
      </el-tab-pane>
    </el-tabs>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
  </div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";

const kEnableDebug = true;

defineOptions({
  name: "MedicalRecordTabs",
});

const emit = defineEmits<{
  cancel: [];
}>();

const props = defineProps<{
  data: ConsultRecord;
}>();

onMounted(() => {
  requestConsultRecordInfo();
});

const tabsLoading = ref(false);
const tabsData = ref<ConsultRecordInfo>({});

// 请求就诊记录详情数据
async function requestConsultRecordInfo() {
  tabsLoading.value = true;
  const r = await Consult_Api.getConsultRecordInfo(props.data.Id!);
  tabsLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  tabsData.value = r.Data;
}
</script>

<style lang="scss" scoped></style>
