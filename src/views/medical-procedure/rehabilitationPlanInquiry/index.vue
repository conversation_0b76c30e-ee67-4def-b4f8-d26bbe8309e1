<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer is-show-toggle>
          <template #left>
            <el-form ref="formRef" :model="queryParams" inline>
              <el-form-item label="方案开始时间">
                <el-date-picker
                  v-model="dateRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  @change="handleDateRangeChange"
                />
              </el-form-item>

              <el-form-item label="所属医院">
                <HospitalSelect
                  v-model="queryParams.OrgId"
                  placeholder="请选择医院"
                  multiple
                  :multiple-limit="1"
                  :scopeable="true"
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="医生">
                <UserSelect
                  v-model="queryParams.DoctorId"
                  placeholder="请选择医生"
                  clearable
                  filterable
                  :role-types="['doctor']"
                  :org-ids="queryParams.OrgId"
                />
              </el-form-item>

              <el-form-item label="治疗师">
                <UserSelect
                  v-model="queryParams.TherapistId"
                  placeholder="请选择治疗师"
                  clearable
                  filterable
                  :role-types="['therapist']"
                  :org-ids="queryParams.OrgId"
                />
              </el-form-item>

              <el-form-item label="护士">
                <UserSelect
                  v-model="queryParams.NurseId"
                  placeholder="请选择护士"
                  clearable
                  filterable
                  :role-types="['nurse']"
                  :org-ids="queryParams.OrgId"
                />
              </el-form-item>

              <el-form-item label="状态">
                <el-select
                  v-model="queryParams.State"
                  placeholder="请选择状态"
                  clearable
                  style="width: 100px"
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="执行中" :value="1" />
                  <el-option label="已结束" :value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="是否测试数据">
                <el-select
                  v-model="queryParams.IsTest"
                  placeholder="请选择"
                  clearable
                  style="width: 100px"
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="1" />
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>

              <el-form-item label="医嘱简称">
                <el-select
                  v-model="queryParams.AliasName"
                  placeholder="请选择医嘱简称"
                  clearable
                  filterable
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="1"
                  style="width: 180px"
                >
                  <el-option
                    v-for="item in aliasNameList"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="关键词">
                <el-input
                  v-model="queryParams.KeyWords"
                  placeholder="请输入关键词"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" :loading="tableLoading" @click="handleQuery">
              搜索
            </el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              :disabled="pageData.length <= 0"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          :height="tableFluidHeight"
          highlight-current-row
          row-key="Id"
          style="text-align: center; flex: 1"
        >
          <el-table-column label="患者信息" width="250" align="center">
            <template #default="scope">
              <p>姓名：{{ scope.row.PatientName }} {{ scope.row.Age }} {{ scope.row.Sex }}</p>
              <p>手机：{{ scope.row.PatientPhone }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="Name" label="方案名称" width="150" align="center" />
          <el-table-column prop="DiagnosisName" label="诊断" width="200" align="center" />
          <el-table-column prop="StartTime" label="方案开始时间" width="200" align="center">
            <template #default="scope">
              {{ dayjs(scope.row.StartTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </el-table-column>
          <el-table-column prop="FinishedTime" label="方案结束时间" width="200" align="center">
            <template #default="scope">
              {{ dayjs(scope.row.FinishedTime).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </el-table-column>
          <el-table-column label="已执行天数/总天数" width="120" align="center">
            <template #default="scope">
              {{
                scope.row.ActualFinishedTime
                  ? scope.row.PerformDays
                  : onGerCompletedDay(scope.row.StartTime)
              }}
              /{{ scope.row.PerformDays }}
            </template>
          </el-table-column>
          <el-table-column prop="FinishRate" label="完成度" width="100" align="center">
            <template #default="scope">
              {{ (scope.row.FinishRate * 100).toFixed(2) + "%" }}
            </template>
          </el-table-column>
          <el-table-column prop="FirstDayRate" label="首日完成度" width="120" align="center">
            <template #default="scope">
              {{ (scope.row.FirstDayRate * 100).toFixed(2) + "%" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="ActionName"
            label="动作明细"
            width="250"
            align="center"
            :show-overflow-tooltip="true"
          />
          <el-table-column prop="OrganizationName" label="医院" width="100" align="center" />
          <el-table-column prop="DoctorName" label="医生" width="100" align="center" />
          <el-table-column prop="TherapistName" label="治疗师" width="100" align="center" />
          <el-table-column prop="NurseName" label="护士" width="120" align="center" />
          <el-table-column prop="AssistantName" label="医助" width="120" align="center" />
          <el-table-column prop="VisitNo" label="就诊号" width="180" align="center" />
          <el-table-column label="状态" width="100" align="center">
            <template #default="scope">
              {{ scope.row.ActualFinishedTime ? "已结束" : "执行中" }}
            </template>
          </el-table-column>
          <el-table-column prop="IsTest" label="是否测试数据" width="120" align="center">
            <template #default="scope">
              {{ scope.row.IsTest === 1 ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="100" align="center">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleViewDetails(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="detailVisible" :title="title" width="80%" destroy-on-close>
      <PlanDetails ref="planDetailsRef" :plan-info="detailInfo" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import { TrainingListInputDTO, TrainingScheme } from "@/api/training/types";
import { ExportEnum } from "@/enums/Other";
import { useTableConfig } from "@/hooks/useTableConfig";
import { getMoItemAliasNameList } from "@/utils/dict";
import { exportExcel } from "@/utils/serviceUtils";
import dayjs from "dayjs";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<TrainingScheme>();

const queryParams = ref<TrainingListInputDTO>({
  OrgId: null,
  StartTime: dayjs().format("YYYY-MM-01 00:00:00"),
  EndTime: dayjs().format("YYYY-MM-DD 23:59:59"),
  DoctorId: null,
  TherapistId: null,
  NurseId: null,
  State: null,
  KeyWords: "",
  PageIndex: 1,
  PageSize: 10,
  IsTest: 0,
  AliasName: null,
});

const exportLoading = ref<boolean>(false);
const detailInfo = ref<TrainingScheme | null>(null);
const detailVisible = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const title = ref<string>("");
const aliasNameList = ref<string[]>([]);
const dateRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const handleDateRangeChange = (val: [string, string]) => {
  if (val) {
    queryParams.value.StartTime = dayjs(val[0]).format("YYYY-MM-DD 00:00:00");
    queryParams.value.EndTime = dayjs(val[1]).format("YYYY-MM-DD 23:59:59");
  } else {
    queryParams.value.StartTime = "";
    queryParams.value.EndTime = "";
  }
};

const handleExportExcel = async () => {
  const params = {
    ServiceExportCode: "TrainingPageReport", //后端
    ExportWay: ExportEnum.ServiceInvoke, // O:Redash, 1：后端
    ExecutingParams: queryParams.value,
    FileName: `康复计划查询-${Date.now()}.xlsx`,
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } finally {
    exportLoading.value = false;
  }
};

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handleGetTableList = async () => {
  tableLoading.value = true;
  try {
    const res = await Training_Api.getTrainingPage(queryParams.value);
    if (res.Type === 200) {
      pageData.value = res.Data.Data;
      total.value = res.Data.Total;
    }
  } finally {
    tableLoading.value = false;
  }
};

// 计算已完成天数
const onGerCompletedDay = (startTime: string) => {
  // 获取当前日期
  const currentDate = dayjs();
  // 计算两个日期之间的差异（以天为单位）
  const differenceInDays = currentDate.diff(dayjs(startTime, "YYYY-MM-DD"), "days");
  return differenceInDays + 1;
};

// 查看详情
const handleViewDetails = (row: TrainingScheme) => {
  console.log("查看详情", row);
  // TODO: 实现查看详情逻辑
  title.value = row.Name;
  detailInfo.value = row;
  detailVisible.value = true;
};

const handleGetAliasNameList = async () => {
  aliasNameList.value = await getMoItemAliasNameList({
    OrgId: null,
    FilterOrg: true,
    IsEnable: true,
    DeptId: null,
  });
};

onMounted(() => {
  // 获取医嘱简称
  handleGetAliasNameList();
});
onActivated(() => {
  handleGetTableList();
});

defineOptions({
  name: "RehabilitationPlanInquiry",
  inheritAttrs: false,
});
</script>

<style lang="scss" scoped></style>
