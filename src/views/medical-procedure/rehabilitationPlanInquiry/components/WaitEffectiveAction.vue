<template>
  <el-card
    v-loading="loading"
    class="h-300px mb-4 overflow-hidden"
    :body-style="{ height: '100%', padding: '0' }"
    shadow="never"
  >
    <div class="flex flex-col h-full">
      <!-- 周标题行 -->
      <div class="flex border-b border-gray-100">
        <div
          class="cursor-pointer p-2 flex items-center transition-all duration-300"
          @click="handleWeekChange('prev')"
        >
          <el-icon><ArrowLeft /></el-icon>
        </div>
        <div
          v-for="day in weekDays"
          :key="day.format('YYYY-MM-DD') + '-header'"
          class="flex-1 p-4 text-center transition-all duration-300"
          :style="{
            background: isToday(day) ? '#409EFF' : '#fff',
            color: isToday(day) ? '#fff' : 'inherit',
          }"
        >
          <div class="font-500 text-12px">
            {{
              `周${["一", "二", "三", "四", "五", "六", "日"][day.day() === 0 ? 6 : day.day() - 1]}`
            }}
          </div>
          <div class="mt-1 text-12px">
            {{ day.format("YYYY-MM-DD") }}
          </div>
        </div>
        <div
          class="cursor-pointer p-2 flex items-center transition-all duration-300"
          @click="handleWeekChange('next')"
        >
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>

      <!-- 数据滚动区域 -->
      <div class="flex flex-1 overflow-y-auto p-x-30px" :class="animation">
        <div
          v-for="day in weekDays"
          :key="day.format('YYYY-MM-DD')"
          class="flex-1 transition-all duration-300"
        >
          <div v-if="handleIsShowInfo(day)" class="p-2 flex flex-col gap-2 bg-white h-full">
            <el-popover
              v-for="action in getActionsForDay(day.format('YYYY-MM-DD'))"
              :key="action.Id"
              placement="right"
              trigger="hover"
              :width="200"
            >
              <template #reference>
                <div
                  class="p-3 rounded cursor-pointer transition-all duration-300"
                  :style="{
                    background: isActionSelected(day.format('YYYY-MM-DD'), action.Id)
                      ? '#F08C1E'
                      : '#f9f9f9',
                    color: isActionSelected(day.format('YYYY-MM-DD'), action.Id) ? '#fff' : '#333',
                    border: '1px solid #d4d6d5f5',
                  }"
                  @click="handleActionClick(day.format('YYYY-MM-DD'), action)"
                >
                  <div class="flex items-start justify-start relative text-12px">
                    <div class="line-clamp-2">{{ action.Name }}</div>
                    <div
                      v-if="isShowIdentification(day.format('YYYY-MM-DD'))"
                      class="w-2 h-2 rounded-full ml-2 absolute top-0 right-0"
                      :style="{
                        background: handleGetActionStatusColor(action),
                      }"
                    />
                  </div>
                  <div
                    class="mt-1"
                    :style="{
                      color: isActionSelected(day.format('YYYY-MM-DD'), action.Id)
                        ? '#fff'
                        : '#666',
                    }"
                  >
                    {{
                      action.TrainingActionDetailExecutes
                        ? action.TrainingActionDetailExecutes.length
                        : 0
                    }}/
                    {{ action.TrainingActionDetailPlan?.Freq || 0 }}
                  </div>
                </div>
              </template>
              <div>
                <div class="flex items-center justify-between">
                  <div>{{ action.Name }}</div>
                </div>
                <div>
                  <span class="text-[#10b25c]">
                    {{ action.TrainingActionDetailPlan.GroupCount }}
                  </span>
                  组/每组
                  <span class="text-[#10b25c]">
                    {{ action.TrainingActionDetailPlan.EachGroupCount }}
                  </span>
                  次/每次
                  <span class="text-[#10b25c]">
                    {{ action.TrainingActionDetailPlan.Duration }}
                  </span>
                  {{ action.TrainingActionDetailPlan.DurationUnit === 0 ? "分钟" : "秒" }}
                </div>
              </div>
            </el-popover>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/zh-cn";
import isoWeek from "dayjs/plugin/isoWeek";
import Training_Api from "@/api/training";
import {
  TrainingActionDetailByDate,
  TrainingActionDetailByDateType,
  TrainingProgram,
} from "@/api/training/types";
dayjs.extend(isoWeek);
dayjs.locale("zh-cn");

const props = defineProps<{
  currentProgram: TrainingProgram;
}>();

const emit = defineEmits(["getCheckRecordInfo"]);

const currentWeek = ref(dayjs());
const weekDays = ref<dayjs.Dayjs[]>([]);
const animation = ref("");
const selectedAction = ref<{
  dayId: string;
  actionId: string;
} | null>(null);
const actions = ref<TrainingActionDetailByDate[]>([]);
const loading = ref<boolean>(false);

const handleCreateDay = (currentProgram: TrainingProgram) => {
  let anchorDate = dayjs().format("YYYY-MM-DD");
  const today = dayjs(new Date()).format("YYYY-MM-DD");
  const end = dayjs(currentProgram.StartTime)
    .add(currentProgram.PerformDays, "days")
    .format("YYYY-MM-DD");

  switch (currentProgram.State) {
    case 0: // 未执行
      anchorDate = currentProgram.StartTime;
      break;
    case 1: // 执行中
      anchorDate = today;
      break;
    case 2: // 已完成
      anchorDate = end;
      break;
    case 3: // 未完成
      const todayStamp = dayjs(today).valueOf();
      const endStamp = dayjs(end).valueOf();
      anchorDate = todayStamp > endStamp ? end : today;
      break;
    default:
      break;
  }
  currentWeek.value = dayjs(anchorDate);
  generateWeekDays(dayjs(anchorDate));
};

const handleGetTrainingActionDetailByDate = async (days: Dayjs[]) => {
  if (!days || days.length === 0) return;
  try {
    const res = await Training_Api.getTrainingActionDetailByDate({
      programId: props.currentProgram.Id,
      startTime: days[0].format("YYYY-MM-DD"),
      endTime: days[6].add(1, "day").format("YYYY-MM-DD"),
    });

    if (res.Type === 200) {
      res.Data.forEach((s) => {
        s.Time = dayjs(s.Time).format("YYYY-MM-DD");
      });
      actions.value = res.Data;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

// 生成一周的日期
const generateWeekDays = (current: Dayjs) => {
  const days = [];
  const startOfWeek = current.startOf("week");

  for (let i = 0; i < 7; i++) {
    days.push(startOfWeek.add(i, "day"));
  }

  weekDays.value = days;
  handleGetTrainingActionDetailByDate(days);
};

// 切换周
const handleWeekChange = (type: "prev" | "next") => {
  animation.value = type === "prev" ? "slide-right" : "slide-left";
  loading.value = true;
  setTimeout(() => {
    const newWeek =
      type === "prev" ? currentWeek.value.subtract(1, "week") : currentWeek.value.add(1, "week");
    currentWeek.value = newWeek;
    generateWeekDays(newWeek);
    animation.value = "";
  }, 150);
};

const isToday = (day: Dayjs): boolean => {
  return day.isSame(dayjs(), "day");
};

const getActionsForDay = (dayId: string): TrainingActionDetailByDateType[] => {
  const currentAction = actions.value.find((s) => s.Time === dayId);
  return currentAction ? currentAction.TrainingActionDetailByDateType0s : [];
};

const handleActionClick = (dayId: string, action: TrainingActionDetailByDateType) => {
  if (selectedAction.value?.dayId === dayId && selectedAction.value?.actionId === action.Id) {
    selectedAction.value = null;
  } else {
    selectedAction.value = { dayId, actionId: action.Id };
  }

  emit(
    "getCheckRecordInfo",
    JSON.parse(JSON.stringify(action.TrainingActionDetailExecutes)) || [],
    action.TrainingActionDetailPlan.Freq,
    action.Manufacturer,
    action.MFType
  );
};

const isActionSelected = (dayId: string, actionId: string): boolean => {
  return selectedAction.value?.dayId === dayId && selectedAction.value?.actionId === actionId;
};

const isShowIdentification = (dayId: string): boolean => {
  const today = dayjs(new Date()).format("YYYY-MM-DD");
  const startTime = dayjs(props.currentProgram.StartTime).format("YYYY-MM-DD");
  const endTime = dayjs(startTime)
    .add(props.currentProgram.PerformDays, "days")
    .format("YYYY-MM-DD");

  const startStamp = new Date(startTime).getTime();
  let endStamp = new Date(endTime).getTime();

  switch (props.currentProgram.State) {
    case 0: // 未执行
      endStamp = startStamp;
      break;
    case 1: // 执行中
      endStamp = new Date(today).getTime();
      break;
    case 2: // 已完成
      endStamp = new Date(endTime).getTime();
      break;
    case 3: // 未完成
      const todayStamp = new Date(today).getTime();
      endStamp = todayStamp > endStamp ? endStamp : todayStamp;
      break;
    default:
      break;
  }

  const dateStamp = new Date(dayId).getTime();
  return startStamp <= dateStamp && endStamp >= dateStamp;
};

const handleIsShowInfo = (day: Dayjs): boolean => {
  const startDate = dayjs(props.currentProgram.StartTime);
  const endDate = dayjs(props.currentProgram.StartTime).add(
    props.currentProgram.PerformDays,
    "days"
  );

  if (day.isSame(startDate, "day")) {
    return true;
  }

  if (day.isBefore(startDate, "day")) {
    return false;
  }

  if (day.isAfter(endDate, "day")) {
    return false;
  }

  return true;
};

const handleGetActionStatusColor = (action: TrainingActionDetailByDateType): string => {
  // 没有打卡
  if (
    !action ||
    !action.TrainingActionDetailExecutes ||
    !action.TrainingActionDetailExecutes.length
  ) {
    return "#f56c6c";
  }
  // 打卡次数大于等于计划次数
  if (
    action.TrainingActionDetailExecutes &&
    action.TrainingActionDetailExecutes.length >= (action.TrainingActionDetailPlan?.Freq || 0)
  ) {
    return "#67c23a";
  }
  // 打卡次数小于计划次数 并且打了卡
  return "#3695FF";
};

// 监听 currentProgram 变化
watch(
  () => props.currentProgram,
  (newVal) => {
    if (newVal) {
      handleCreateDay(newVal);
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.slide-left {
  animation: slide-left 0.3s ease-in-out;
}

.slide-right {
  animation: slide-right 0.3s ease-in-out;
}

@keyframes slide-left {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes slide-right {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}
</style>
