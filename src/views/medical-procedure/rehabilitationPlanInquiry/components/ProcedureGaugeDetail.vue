<template>
  <div>
    <el-table :data="patGaugeList" :border="true" :row-key="'EvaluateGaugeId'" :pagination="false">
      <el-table-column prop="Name" label="量表名称" align="center" width="200" fixed="left" />
      <el-table-column
        prop="Remark"
        label="量表说明"
        align="center"
        width="300"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="最终得分" align="center" width="120">
        <template #default="{ row }">
          <div>{{ row.IsEvaluate ? row.SumPoint : "--" }}</div>
        </template>
      </el-table-column>
      <el-table-column label="结果" align="center" width="200" :show-overflow-tooltip="true">
        <template #default="{ row }">
          <div>{{ row.IsEvaluate ? row.SuccessEvaluateContent : "--" }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="{ row }">
          <el-button v-if="row.IsEvaluate" link type="primary" @click="handleView(row)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="isModalOpen"
      :title="currentPatGauge?.Name"
      :width="900"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <GaugeListDetails v-if="isModalOpen" :pat-gauge-id="currentPatGauge?.EvaluateGaugeId || ''" />
      <template #footer>
        <el-button @click="isModalOpen = false">取消</el-button>
        <el-button type="primary" @click="isModalOpen = false">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { PatGauge } from "@/api/training/types";
import GaugeListDetails from "./GaugeListDetails.vue";

interface Props {
  patGauge: PatGauge[];
}

const props = defineProps<Props>();

const patGaugeList = ref<PatGauge[]>([]);
const isModalOpen = ref(false);
const currentPatGauge = ref<PatGauge | null>(null);

const handleProcessPatGauge = (patGauge: PatGauge[]) => {
  console.log(patGauge);
  patGaugeList.value = patGauge;
};

const handleView = (record: PatGauge) => {
  currentPatGauge.value = record;
  isModalOpen.value = true;
};

watchEffect(() => {
  handleProcessPatGauge(props.patGauge);
});
</script>

<style lang="scss" scoped>
:deep(.el-table) {
  .el-table__body-wrapper {
    overflow-x: auto;
  }
}
</style>
