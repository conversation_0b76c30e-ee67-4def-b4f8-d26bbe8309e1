<template>
  <el-card class="h-300px mb-4" :body-style="{ height: '100%', padding: '0' }" shadow="never">
    <el-row>
      <!-- 左侧打卡记录 -->
      <el-col :span="12" class="border-r border-solid border-[#f0f0f0] p-4">
        <div class="text-4 font-500 mb-4">打卡记录({{ listData.length }}/{{ checkShowTotal }})</div>
        <div class="flex flex-col gap-2">
          <div v-for="(execute, index) in listData" :key="execute.Id">
            <div class="flex items-center gap-2">
              <div class="check-dot" />
              <div>{{ execute.CreatedTime }}第{{ index + 1 }}次打卡</div>
              <div class="text-[#666] ml-auto">打卡人: {{ patName }}</div>
            </div>
            <template v-if="execute.Media">
              <video
                v-if="JSON.parse(execute.Media).type === 0"
                class="w-50px h-50px"
                :src="JSON.parse(execute.Media).url"
              />
              <el-image v-else class="w-50px h-50px" :src="JSON.parse(execute.Media).url" />
            </template>
          </div>
        </div>
      </el-col>

      <!-- 右侧报告 -->
      <el-col :span="12" class="p-4">
        <div class="text-4 font-500 mb-4">报告</div>
        <div class="report-content">
          <template v-for="(o, index) in listData" :key="index">
            <!-- 易脑参数 -->
            <el-card
              v-if="
                props.checkManufacturer === 1 && o.Remark && o.Remark.WorkoutPrescriptionRecordVO
              "
              class="mb-3 report-card"
              shadow="hover"
            >
              <div class="block">{{ o.Remark.WorkoutPrescriptionRecordVO.PrescriptionName }}</div>
              <div class="block">
                {{ o.Remark.WorkoutPrescriptionRecordVO.Limb === 4 ? "肢体：上肢" : "肢体：下肢" }}
              </div>
              <div v-if="o.Remark.WorkoutPrescriptionRecordVO.PrescriptionDuration" class="block">
                时长：{{ o.Remark.WorkoutPrescriptionRecordVO.PrescriptionDuration }}
              </div>
              <div v-if="o.Remark.WorkoutPrescriptionRecordVO.PrescriptionDifficulty" class="block">
                难度：{{ o.Remark.WorkoutPrescriptionRecordVO.PrescriptionDifficulty }}星
              </div>
              <div class="block">设备：{{ o.Remark.WorkoutPrescriptionRecordVO.Device || "" }}</div>
              <div v-if="o.Remark.WorkoutPrescriptionRecordVO.ActualDuration" class="block">
                实际训练时长：{{ o.Remark.WorkoutPrescriptionRecordVO.ActualDuration }}
              </div>
              <div v-if="o.Remark.WorkoutPrescriptionRecordVO.CreateTime" class="block">
                训练时间：{{
                  o.Remark.WorkoutPrescriptionRecordVO.CreateTime &&
                  dayjs(o.Remark.WorkoutPrescriptionRecordVO.CreateTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                }}
              </div>
              <div v-if="o.Remark.WorkoutPrescriptionRecordVO.Radar" class="block">
                <div class="block">
                  brunnstrom得分：{{ o.Remark.WorkoutPrescriptionRecordVO.Radar.BrunnstromScore }}
                </div>
                <div class="block">
                  平移得分：{{ o.Remark.WorkoutPrescriptionRecordVO.Radar.Horizontal }}
                </div>
                <div class="block">
                  抬举得分：{{ o.Remark.WorkoutPrescriptionRecordVO.Radar.Vertical }}
                </div>
                <div class="block">
                  爆发力得分：{{ o.Remark.WorkoutPrescriptionRecordVO.Radar.Power }}
                </div>
                <div class="block">
                  稳定性得分：{{ o.Remark.WorkoutPrescriptionRecordVO.Radar.Sstability }}
                </div>
                <div class="block">
                  总分：{{ o.Remark.WorkoutPrescriptionRecordVO.Radar.Total }}
                </div>
              </div>
            </el-card>
            <!-- 吸气训练参数 -->
            <el-card
              v-if="props.checkManufacturer === 2 && props.checkMfType === 2 && o.Remark"
              class="mb-3 report-card"
              shadow="hover"
            >
              <div class="block">训练次数：{{ o.Remark.Result[0].Result.count || "-" }}</div>
              <div class="block">
                训练方式：{{ o.Remark.Result[0].Result.train_type === 1 ? "自动" : "手动" }}
              </div>
              <div v-if="o.Remark.Result[0].Result.train_type == 1" class="block">
                训练难度：{{ (o.Remark.Result[0].Result.difficulty_level || "-") + "星" }}
              </div>
              <div v-if="o.Remark.Result[0].Result.train_type == 2" class="block">
                阻抗：{{ o.Remark.Result[0].Result.target_load || "-" }}
              </div>
              <div class="block">训练的时间：{{ o.Remark.Result[0].Result.date || "-" }}</div>
              <div class="block">
                最大吸气压：{{ o.Remark.Result[0].Result.MIP_max || "-" }}cmH2O
              </div>
              <div class="block">平均吸气量：{{ o.Remark.Result[0].Result.FIVC_avg || "-" }}mL</div>
              <div class="block">总吸气量：{{ o.Remark.Result[0].Result.FIVC_all || "-" }}mL</div>
              <div class="block">
                平均吸气流量：{{ o.Remark.Result[0].Result.PIF_avg || "-" }}L/min
              </div>
            </el-card>
            <!-- 呼气训练参数 -->
            <el-card
              v-if="props.checkManufacturer === 2 && checkMfType === 1 && o.Remark"
              class="mb-3 report-card"
              shadow="hover"
            >
              <div class="block">训练次数：{{ o.Remark.Result[0].Result.count || "-" }}</div>
              <div class="block">
                训练方式：{{ o.Remark.Result[0].Result.train_type === 1 ? "自动" : "手动" }}
              </div>
              <div v-if="o.Remark.Result[0].Result.train_type == 1" class="block">
                训练难度：{{ (o.Remark.Result[0].Result.difficulty_level || "-") + "星" }}
              </div>
              <div v-if="o.Remark.Result[0].Result.train_type == 2" class="block">
                阻抗：{{ o.Remark.Result[0].Result.target_load || "-" }}
              </div>
              <div class="block">训练的时间：{{ o.Remark.Result[0].Result.date || "-" }}</div>
              <div class="block">
                最大呼气压：{{ o.Remark.Result[0].Result.MEP_max || "-" }}cmH2O
              </div>
              <div class="block">平均呼气量：{{ o.Remark.Result[0].Result.FVC_avg || "-" }}mL</div>
              <div class="block">总呼气量：{{ o.Remark.Result[0].Result.FVC_all || "-" }}mL</div>
              <div class="block">
                平均呼气流量：{{ o.Remark.Result[0].Result.PEF_avg || "-" }}L/min
              </div>
            </el-card>
            <!-- 气道廓清 -->
            <el-card
              v-if="props.checkManufacturer === 2 && checkMfType === 3 && o.Remark"
              class="mb-3 report-card"
              shadow="hover"
            >
              <div class="block">
                阻力级别：{{ o.Remark.Result[0].Result.difficulty_level || "-" }}档
              </div>
              <div class="block">
                训练方式：{{ o.Remark.Result[0].Result.train_type === 1 ? "自动" : "手动" }}
              </div>
              <div class="block">呼吸次数：{{ o.Remark.Result[0].Result.count || "-" }}</div>
              <div class="block">
                振动频率：{{ o.Remark.Result[0].Result.train_frequence || "-" }}Hz
              </div>
              <div class="block">振动总时间：{{ o.Remark.Result[0].Result.duration || "-" }}秒</div>
              <div class="block">训练的时间：{{ o.Remark.Result[0].Result.date || "" }}</div>
              <div class="block">
                最小振动频率：{{ o.Remark.Result[0].Result.frequence_min || "" }}Hz
              </div>
              <div class="block">
                平均振动频率：{{ o.Remark.Result[0].Result.frequence_avg || "" }}Hz
              </div>
              <div class="block">
                最大振动频率：{{ o.Remark.Result[0].Result.frequence_max || "" }}Hz
              </div>
              <div class="block">
                最小正向压力：{{ o.Remark.Result[0].Result.pressure_min || "" }}cmH2O
              </div>
              <div class="block">
                平均正向压力：{{ o.Remark.Result[0].Result.pressure_avg || "" }}cmH2O
              </div>
              <div class="block">
                最大正向压力：{{ o.Remark.Result[0].Result.pressure_max || "" }}cmH2O
              </div>
              <div class="block">
                最小振动幅度：{{ o.Remark.Result[0].Result.amplitude_min || "" }}cmH2O
              </div>
              <div class="block">
                平均振动幅度：{{ o.Remark.Result[0].Result.amplitude_avg || "" }}cmH2O
              </div>
              <div class="block">
                最大振动幅度：{{ o.Remark.Result[0].Result.amplitude_max || "" }}cmH2O
              </div>
            </el-card>
            <!-- 脑洞极光 -->
            <el-card
              v-if="props.checkManufacturer === 3 && o.BrainMovementAuroras"
              class="mb-3 report-card"
              shadow="hover"
            >
              <!-- 这里显示报告1、2、3 如果没有就不现实 o.BrainMovementAuroras.Url 然后点击预览pdf文件-->
              <div class="cursor-pointer" @click="handlePreviewPdf(o.BrainMovementAuroras.Url)">
                报告{{ index + 1 }}
              </div>
            </el-card>
          </template>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { ActionExecute } from "@/api/training/types";

interface PageActionExecute extends Omit<ActionExecute, "Remark"> {
  Remark: {
    Result: Remark[];
    WorkoutPrescriptionRecordVO: {
      PrescriptionName: string;
      Limb: number;
      PrescriptionDuration: number;
      PrescriptionDifficulty: number;
      Device: string;
      ActualDuration: number;
      CreateTime: string;
      Radar: {
        BrunnstromScore: number;
        Horizontal: number;
        Vertical: number;
        Power: number;
        Sstability: number;
        Total: number;
      };
    };
  };
  BrainMovementAuroras?: {
    Url: string;
  };
}
interface Remark {
  Result: RemarkResult;
  Ref: null;
  Inhale: null;
  Exhale: null;
}
interface RemarkResult {
  unique_key: string;
  count: number;
  date: string;
  target_load: number;
  train_type: number;
  difficulty_level: number;
  MEP_max: number;
  FVC_avg: number;
  FVC_all: number;
  PEF_avg: number;
  MIP_max: number;
  FIVC_avg: number;
  FIVC_all: number;
  PIF_avg: number;
  train_frequence?: number;
  duration?: number;
  frequence_min?: number;
  frequence_avg?: number;
  frequence_max?: number;
  pressure_min?: number;
  pressure_avg?: number;
  pressure_max?: number;
  amplitude_min?: number;
  amplitude_avg?: number;
  amplitude_max?: number;
}

const listData = ref<PageActionExecute[]>([]);

const handleProcessingData = (list: ActionExecute[]) => {
  const newList = list.map((s): PageActionExecute => {
    const remark = JSON.parse(s.Remark);
    console.log(remark);
    return {
      ...s,
      Remark: remark,
      BrainMovementAuroras:
        props.checkManufacturer === 3 && remark
          ? {
              Url: remark.Result,
            }
          : undefined,
    };
  });
  listData.value = newList;
};

const props = defineProps<{
  checkExecutes: ActionExecute[];
  patName: string;
  checkShowTotal: number;
  checkManufacturer: number;
  checkMfType: number | null;
}>();

// 预览PDF文件的方法
const handlePreviewPdf = (url: string) => {
  if (!url) {
    console.warn("PDF URL is empty");
    return;
  }

  // 使用浏览器自带的方法打开PDF文件
  window.open(url, "_blank");
};

watch(
  () => props.checkExecutes,
  (newVal) => {
    handleProcessingData(newVal);
  },
  { immediate: true }
);
</script>

<style scoped>
.check-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #52c41a;
}

.report-content {
  height: calc(300px - 48px);
  overflow-y: auto;
  padding-right: 4px;
}

.report-content::-webkit-scrollbar {
  width: 4px;
}

.report-content::-webkit-scrollbar-thumb {
  background-color: #e0e0e0;
  border-radius: 2px;
}

.report-content::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

.report-card {
  margin-bottom: 12px;
  padding: 10px;
}
</style>
