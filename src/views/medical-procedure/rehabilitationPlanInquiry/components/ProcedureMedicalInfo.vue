<template>
  <div>
    <div v-if="info" class="px-6">
      <el-card class="medical-info-card" shadow="never">
        <el-descriptions title="基本信息" :column="2" border size="small">
          <el-descriptions-item label="就诊时间">
            {{ info.InDate }}
          </el-descriptions-item>
          <el-descriptions-item label="科别">
            {{ info.DepartmentName }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider class="my-6" />

        <el-descriptions title="病情描述" :column="1" border size="small">
          <el-descriptions-item label="主诉">
            {{ info.Complain || "暂无记录" }}
          </el-descriptions-item>
          <el-descriptions-item label="既往史">
            {{ info.HistoryIllness || "暂无记录" }}
          </el-descriptions-item>
          <el-descriptions-item label="现病史">
            {{ info.PresentIllness || "暂无记录" }}
          </el-descriptions-item>
          <el-descriptions-item label="辅助检查结果">
            {{ info.AuxiliaryDiagnosis || "暂无记录" }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider class="my-6" />

        <el-descriptions title="诊断与处置" :column="1" border size="small">
          <el-descriptions-item label="诊断">
            <div class="whitespace-pre-line">
              {{
                (info.Diagnoses && info.Diagnoses.map((item) => item.DiagnoseName).join("\n")) ||
                "暂无记录"
              }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="处置">
            <div class="whitespace-pre-line">
              {{ info.Disposal || "暂无记录" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
    <div v-else class="text-center py-8 text-gray-400">暂无数据</div>
  </div>
</template>

<script setup lang="ts">
import { VisitInfo } from "@/api/training/types";

const props = defineProps<{
  visitInfo: VisitInfo | null;
}>();

const info = ref<VisitInfo | null>(null);

const handleProcessVisitInfo = (visitInfo: VisitInfo | null) => {
  if (!visitInfo) return;
  info.value = visitInfo;
};

// 监听props.visitInfo的变化
watch(
  () => props.visitInfo,
  (newVisitInfo) => {
    handleProcessVisitInfo(newVisitInfo);
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.medical-info-card {
  :deep(.el-card__body) {
    padding: 20px;
  }

  :deep(.el-descriptions__body) {
    width: 100%;
  }
}
</style>
