<template>
  <div class="plan-details">
    <el-tabs v-model="activeName" type="card" class="advice-tabs" @tab-click="handleTabsClick">
      <el-tab-pane label="指导记录" name="guidanceRecord">
        <BaseChat :program-id="props.planInfo?.Id" />
      </el-tab-pane>
      <el-tab-pane label="训练方案" name="trainingPlan">
        <TrainingProgramme :history-program="historyProgram" :pat-name="patName" />
      </el-tab-pane>
      <el-tab-pane label="评估量表" name="evaluationForm">
        <ProcedureGaugeDetail :pat-gauge="patGauge" />
      </el-tab-pane>
      <el-tab-pane label="就诊信息" name="visitInfo">
        <ProcedureMedicalInfo :visit-info="visitInfo" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import Training_Api from "@/api/training";
import { PatGauge, TrainingProgram, TrainingScheme, VisitInfo } from "@/api/training/types";
import { TabsPaneContext } from "element-plus";

const activeName = ref<string>("guidanceRecord");
const historyProgram = ref<TrainingProgram[]>([]);
const visitInfo = ref<VisitInfo | null>(null);
const patGauge = ref<PatGauge[]>([]);
const patName = ref<string>("");
const handleTabsClick = (tab: TabsPaneContext) => {
  activeName.value = tab.paneName as string;
};

const handleProcessPlanInfo = (newVal: TrainingScheme | null) => {
  if (newVal) {
    if (newVal.Id) {
      handleGetPatientInfoByProgramId(newVal.Id);
    }
    if (newVal.VisitNo) {
      handleHistoryProgramByPatVisitId(newVal.VisitNo);
    }
  }
};

const handleGetPatientInfoByProgramId = async (Id: string) => {
  try {
    const res = await Training_Api.getPatientInfoByProgramId({ programId: Id });
    if (res.Type !== 200) {
      return;
    }
    const patientVisit = res.Data.VisitInfo;
    if (patientVisit) {
      patientVisit.InDate = dayjs(patientVisit.InDate).format("YYYY-MM-DD");
      visitInfo.value = patientVisit;
    }
    patGauge.value = res.Data.PatGauge || [];
    handleProcessTrainingProgram(res.Data.TrainingProgram);
    patName.value = res.Data.UserInfo?.Name || "";
  } catch (error) {
    console.error("获取患者训练信息失败", error);
  }
};

const handleProcessTrainingProgram = (trainingProgram: TrainingProgram | null) => {
  if (!trainingProgram) return;
  trainingProgram.StartTime = dayjs(trainingProgram.StartTime).format("YYYY-MM-DD");
  trainingProgram.FinishedTime = dayjs(trainingProgram.FinishedTime).format("YYYY-MM-DD");
};

const handleHistoryProgramByPatVisitId = async (visitId: string) => {
  const res = await Training_Api.getHistoryProgramByPatVisitId({
    visitId,
    useScene: 2,
  });
  if (res.Type === 200) {
    historyProgram.value = res.Data || [];
  }
};

interface Props {
  planInfo: TrainingScheme | null;
}
const props = defineProps<Props>();
watch(
  () => props.planInfo,
  (newVal) => {
    handleProcessPlanInfo(newVal);
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.plan-details {
  height: 600px;
  padding: 20px;
  overflow-y: auto;
  padding-top: 0;
}
.advice-tabs {
  :deep(.el-tabs__header) {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: var(--el-bg-color);
  }
}
</style>
