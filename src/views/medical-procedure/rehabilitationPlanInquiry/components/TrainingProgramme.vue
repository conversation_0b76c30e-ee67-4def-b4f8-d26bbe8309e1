<template>
  <el-row>
    <!-- 左侧列表 -->
    <el-col :span="4" class="left-column">
      <div class="program-list h-full bg-white">
        <ul class="list-none p-0 m-0">
          <li
            v-for="item in historyProgramData"
            :key="item.Id"
            class="program-item"
            :class="{ 'program-item-active': item.Id === currentProgramData?.Id }"
            @click="handleClickProgram(item)"
          >
            <div class="truncate">
              {{ item.Name }}
            </div>
          </li>
        </ul>
      </div>
    </el-col>

    <!-- 右侧详情 -->
    <el-col :span="20" class="right-column">
      <template v-if="currentProgramData">
        <el-card class="detail-card">
          <div class="detail-header">
            <div class="info-container">
              <span class="info-row">
                <span class="info-item">
                  <span class="info-label">方案名称:</span>
                  <span class="info-value">{{ currentProgramData.Name }}</span>
                </span>
                <span class="info-item">
                  <span class="info-label">开始日期:</span>
                  <span class="info-value">
                    {{
                      currentProgramData.StartTime
                        ? dayjs(currentProgramData.StartTime).format("YYYY-MM-DD HH:mm:ss")
                        : "-"
                    }}
                  </span>
                </span>
                <span class="info-item">
                  <span class="info-label">停止时期:</span>
                  <span class="info-value">
                    {{
                      getStopTime(currentProgramData)
                        ? dayjs(getStopTime(currentProgramData)).format("YYYY-MM-DD HH:mm:ss")
                        : "-"
                    }}
                  </span>
                </span>
                <span class="info-item">
                  <span class="info-label">停止人:</span>
                  <span class="info-value">{{ currentProgramData.LastUpdateName || "-" }}</span>
                </span>
                <span class="info-item">
                  <span class="info-label">剩余天数:</span>
                  <span class="info-value">{{ getRemainingDays(currentProgramData) }}</span>
                </span>
                <span class="info-item">
                  <span class="info-label">完成度:</span>
                  <span class="info-value">{{ getFinishRate(currentProgramData) }}</span>
                </span>
                <span class="info-item">
                  <span class="info-label">方案说明:</span>
                </span>
                <span class="info-item">{{ currentProgramData.Remark || "-" }}</span>
              </span>
            </div>
          </div>
        </el-card>

        <!-- 中间部分 -->
        <WaitEffectiveAction
          :currentProgram="currentProgramData"
          @getCheckRecordInfo="handleGetCheckRecordInfo"
        />

        <!-- 底部部分 -->
        <CheckInformation
          :checkExecutes="checkExecutes"
          :patName="patName"
          :checkShowTotal="checkShowTotal"
          :checkManufacturer="checkManufacturer"
          :checkMfType="checkMfType"
        />
      </template>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { ActionExecute, TrainingProgram } from "@/api/training/types";

const props = defineProps<{
  historyProgram: TrainingProgram[];
  patName: string;
}>();

const currentProgramData = ref<TrainingProgram | null>(null);
const historyProgramData = ref<TrainingProgram[]>([]);
const checkExecutes = ref<ActionExecute[]>([]);
const checkShowTotal = ref<number>(0);
const checkManufacturer = ref<number>(0);
const checkMfType = ref<number>(0);

const handleProcessHistoryProgram = (historyProgram: TrainingProgram[]) => {
  historyProgramData.value = historyProgram;
  // 获取当前执行中的训练方案
  const currentProgram = historyProgram[0];
  currentProgramData.value = currentProgram;
};

// 获取停止时间
const getStopTime = (program: TrainingProgram) => {
  if (program.State === 4) {
    return program.LastUpdatedTime;
  }
  return program.ActualFinishedTime || "";
};

// 获取剩余天数
const getRemainingDays = (program: TrainingProgram) => {
  if (program.ActualFinishedTime) {
    return "0";
  }
  const finishedTime = new Date(program.FinishedTime).getTime();
  const currentTime = new Date().getTime();
  const diffTime = Math.abs(finishedTime - currentTime);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)).toString() || "0";
};

/**
 * 获取完成度
 */
const getFinishRate = (program: TrainingProgram) => {
  return Math.ceil(program.FinishRate * 100) + "%";
};

const handleGetCheckRecordInfo = (
  executes: ActionExecute[],
  total: number,
  manufacturer: number,
  mfType: number
) => {
  console.log(executes, total);
  executes.forEach((execute) => {
    execute.CreatedTime = dayjs(execute.CreatedTime).format("HH:mm");
  });
  checkExecutes.value = executes;
  checkShowTotal.value = total;
  checkManufacturer.value = manufacturer;
  checkMfType.value = mfType;
};

const handleClickProgram = (program: TrainingProgram) => {
  currentProgramData.value = program;
};

watch(
  () => props.historyProgram,
  (newVal) => {
    handleProcessHistoryProgram(newVal);
  },
  { immediate: true }
);
</script>

<style scoped>
.left-column {
  border-right: 1px solid #f0f0f0;
  height: 100%;
  overflow-y: auto;
}

.right-column {
  padding: 0;
  height: 100%;
  overflow-y: auto;
}

.program-list {
  height: 100%;
  overflow-y: auto;
}

.program-item {
  cursor: pointer;
  padding: 12px 24px;
  line-height: 1.5;
  transition: background-color 0.3s;
}

.program-item:hover {
  background-color: #f5f7fa;
}

.program-item-active {
  background-color: #e6f7ff;
  color: #409eff;
  font-weight: 500;
}

.detail-card {
  padding: 0;
  border-radius: 0;
  margin-top: 0;
}

.detail-header {
  padding: 12px 16px;
}

.title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.long-item {
  flex-grow: 1;
}

.info-label {
  color: #606266;
  font-size: 14px;
  margin-right: 4px;
  font-weight: 500;
}

.info-value {
  color: #303133;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-container {
  border-bottom: 1px solid #f0f0f0;
  display: flex;
}

.status-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot-success {
  background-color: #52c41a;
}

.status-dot-error {
  background-color: #ff4d4f;
}

.status-text {
  font-size: 14px;
}

.status-text-success {
  color: #52c41a;
}

.status-text-error {
  color: #ff4d4f;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.el-card__body) {
  padding: 0 !important;
}
</style>
