<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="执行日期">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="开方医院">
                <HospitalSelect v-model="queryParams.OrgId" />
              </el-form-item>
              <el-form-item label="治疗项目">
                <el-select
                  v-model="queryParams.BaseMoItemId"
                  :disabled="!queryParams.OrgId"
                  placeholder="请选择"
                  :empty-values="['', undefined, null]"
                  :value-on-clear="() => null"
                  clearable
                >
                  <el-option
                    v-for="item in treatmentList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="执行医院">
                <HospitalSelect v-model="queryParams.ExecOrgId" :is-treatment="true" />
              </el-form-item>
              <el-form-item label="执行人">
                <UserSelect
                  v-model="queryParams.DctId"
                  :role-types="['doctor', 'therapist', 'nurse']"
                />
              </el-form-item>
              <el-form-item label="方案编号" prop="PreId">
                <el-input
                  v-model="queryParams.PreId"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Name" label="姓名" align="center" />
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column prop="Age" label="年龄" align="center" />
          <el-table-column prop="HasVisitedRegister" label="是否报道" align="center" />
          <el-table-column prop="ConsultFinishCount" label="问诊次数" align="center" />
          <el-table-column prop="TreatFinishCount" label="咨询次数" align="center" />
          <el-table-column prop="PhoneNumber" label="注册电话" align="center" width="180" />

          <el-table-column label="医生/治疗师" prop="inviterUser" align="center">
            <template #default="scope">
              {{ scope.row.inviterUser }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button
                v-hasNoPermission="['promoter']"
                link
                type="primary"
                @click="handlePreviewOrEdit(scope.row, false)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import Content_Api from "@/api/content";
import { MoItemPageData } from "@/api/content/types";

defineOptions({
  name: "OfflineExecutionRecord",
});

const treatmentList = ref<MoItemPageData[]>([]);

const queryParams = ref<any>({
  State: 1,
  BaseMoItemId: null,
  DctId: null,
  OrgId: null,
  ExecOrgId: null,
  PreId: null,
  StarDate: dayjs(new Date()).format("YYYY-MM-01") + " 00:00:00",
  EndDate: dayjs(new Date()).format("YYYY-MM-DD") + " 23:59:59",
  PageIndex: 1,
  PageSize: 10,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  exportLoading,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
} = useTableConfig<unknown>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: BaseOrganization | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
};

const onGetTreatmentList = async (orgId: string) => {
  const res = await Content_Api.getMoItemPageData({
    currentOrganizationId: orgId,
    page: 1,
    pageSize: 1000,
  });
  treatmentList.value = res.Data.Data;
};
const handleGetTableList = () => {};

watch(timeRange, (newVal) => {
  queryParams.value.StarDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});
watch(queryParams.value.OrgId, (newVal) => {
  console.log(newVal);
  if (newVal) {
    queryParams.value.BaseMoItemId = null;
    onGetTreatmentList(newVal);
  } else {
    treatmentList.value = [];
  }
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
