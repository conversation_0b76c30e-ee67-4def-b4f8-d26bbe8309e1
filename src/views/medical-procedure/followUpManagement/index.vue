<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="医院" prop="OrgId">
                <HospitalSelect
                  v-model="queryParams.OrgId"
                  :scopeable="true"
                  @change="(_, option) => (orgName = option?.Name ?? '')"
                />
              </el-form-item>
              <el-form-item label="随访开始日期">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
              <el-form-item label="状态" prop="State">
                <KSelect
                  v-model="queryParams.State"
                  :data="[
                    { label: '未开始', value: 1 },
                    { label: '随访中', value: 2 },
                    { label: '未完成', value: 4 },
                    { label: '已完成', value: 3 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="随访方式" prop="Type">
                <KSelect
                  v-model="queryParams.Type"
                  :data="[
                    { label: '电话随访', value: 1 },
                    { label: '在线随访', value: 2 },
                    { label: '来院', value: 3 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="KeyWords">
                <el-input
                  v-model="queryParams.KeyWords"
                  placeholder="患者姓名/编号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="onAddItem">新建</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="PatNo" label="患者编号" min-width="100" show-overflow-tooltip />
          <el-table-column prop="PatName" label="患者姓名" min-width="100" show-overflow-tooltip />
          <el-table-column prop="Sex" label="性别" min-width="80" />
          <el-table-column prop="Name" label="随访名称" min-width="120" show-overflow-tooltip />
          <el-table-column prop="Type" label="随访方式" min-width="80">
            <template #default="scope">
              {{ ["未知", "电话随访", "在线随访", "来院"][scope.row.Type] }}
            </template>
          </el-table-column>
          <el-table-column prop="ExecName" label="执行人" min-width="100" />
          <el-table-column
            prop="StartTime"
            label="开始时间"
            show-overflow-tooltip
            min-width="120"
            :formatter="tableDateFormatDay"
          />
          <el-table-column
            label="完成时间"
            show-overflow-tooltip
            min-width="120"
            :formatter="tableDateFormatDay"
          />
          <el-table-column prop="StartTime" label="状态" min-width="80">
            <template #default="scope">
              {{ ["", "未开始", "随访中", "已完成", "未完成"][scope.row.State] }}
            </template>
          </el-table-column>
          <el-table-column prop="StartTime" label="随访状态" min-width="80">
            <template #default="scope">
              {{ ["", "未开始", "随访中", "已完成", "未完成"][scope.row.State] }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" min-width="100">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewOrEdit(scope.row, true)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="850"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <FollowUpForm
      :data="showDataDialog.data"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { GetFollowUpPlanPageParams } from "@/api/consult/types";
import { useUserStore } from "@/store";
import Consult_Api from "@/api/consult";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "FollowUpManagement",
});

const {
  kTableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  tableDateFormatDay,
} = useTableConfig<FollowUpPlan>();

// 查询条件
const queryParams = reactive<GetFollowUpPlanPageParams>({
  StartTime: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  EndTime: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  PageIndex: 1,
  PageSize: 10,
  LoginUserId: useUserStore().userInfo.Id,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.StartTime, queryParams.EndTime];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.StartTime = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.EndTime = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

// 当前选择的医院
let orgName: string = "";

// 查看/添加/编辑弹窗
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as FollowUpPlan, // 查看详情
});

// 点击搜索
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

// 点击添加
function onAddItem() {
  kEnableDebug && console.debug("点击添加");
  if (!queryParams.OrgId) {
    ElMessage.error("请先选择医院");
    return;
  }

  showDataDialog.title = "新建随访";
  showDataDialog.disabled = false;
  showDataDialog.data = {
    OrgId: queryParams.OrgId,
    OrgName: orgName,
  };
  showDataDialog.isShow = true;
}

// 点击查看/编辑
async function onPreviewOrEdit(row?: FollowUpPlan, disabled: boolean = false) {
  kEnableDebug && console.debug("查看/编辑", row, disabled);

  if (!row?.ShowId) {
    ElMessage.error("随访Id为空");
    return;
  }

  showDataDialog.title = disabled ? "查看随访" : "编辑随访";
  showDataDialog.disabled = disabled;
  showDataDialog.data = row ?? {};
  showDataDialog.isShow = true;
}

// 确定新增提交
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  requestTableList();
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Consult_Api.getFollowUpPlanPage(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
