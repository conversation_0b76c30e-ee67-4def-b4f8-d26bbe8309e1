<template>
  <div v-loading="formLoading" class="px-20px overflow-y-auto">
    <el-form :ref="kFormRef" :model="formData" :rules="rules">
      <el-form-item label-width="0" prop="ProfileDescription">
        <el-input
          v-model="formData.ProfileDescription"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 8 }"
          placeholder="请输入备注(资料是否完善)"
        />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
import { UpdatePartParams } from "@/api/passport/types";
import Passport_Api from "@/api/passport";
import { DoctorStatusRedash } from "@/api/report/types";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";
defineOptions({
  name: "RemarkForm",
});

const props = defineProps<{
  data: DoctorStatusRedash;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

onMounted(() => {
  const data: DoctorStatusRedash = JSON.parse(JSON.stringify(props.data));
  formData.UserId = data.DocUserId!;
  formData.ProfileDescription = data.profileDescription;
});

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = reactive<UpdatePartParams>({
  OnlyFillNotNull: true,
  UserId: "",
});

// 表单验证规则
const rules = reactive<FormRules<string>>({
  ProfileDescription: [{ required: true, message: "请输入备注", trigger: "blur" }],
});

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("requestAddOrUpdateData", formData);

  formLoading.value = true;
  const r = await Passport_Api.updatePart([formData]);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped></style>
