<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="医院">
                <HospitalSelect
                  v-model="queryParams.orgIdList"
                  :scopeable="true"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                  @change="
                    () => {
                      queryParams.deptIdList = undefined;
                      queryParams.assistantIdList = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.deptIdList"
                  :org-id="queryParams.orgIdList?.[0]"
                  :disabled="queryParams.orgIdList?.length !== 1"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                />
              </el-form-item>
              <el-form-item label="医助">
                <UserSelect
                  v-model="queryParams.assistantIdList"
                  placeholder="请选择医助"
                  :org-ids="queryParams.orgIdList"
                  :dept-ids="queryParams.deptIdList"
                  :scopeable="false"
                  :role-types="['assistant']"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                />
              </el-form-item>
              <el-form-item label="时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  :disabled-date="handleDisabledDate"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length === 0"
              :loading="exportLoading"
              @click="onExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="DocUserId"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="Name" label="姓名" min-width="80" show-overflow-tooltip />
          <el-table-column
            prop="PhoneNumber"
            label="手机号"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column prop="Sex" label="性别" min-width="60" show-overflow-tooltip />
          <el-table-column prop="Age" label="年龄" min-width="60" show-overflow-tooltip />
          <el-table-column prop="OrgName" label="医院" min-width="180" show-overflow-tooltip />
          <el-table-column prop="DeptName" label="科室" min-width="120" show-overflow-tooltip />
          <el-table-column
            prop="AssistantName"
            label="医助"
            min-width="110"
            show-overflow-tooltip
          />
          <el-table-column prop="PracticeLevel" label="职称" min-width="90" show-overflow-tooltip />
          <el-table-column prop="RoleName" label="角色" min-width="70" show-overflow-tooltip />
          <el-table-column
            prop="newPatientCount"
            label="新增患者数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newPatientCount - b.newPatientCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="totalPatientCount"
            label="累计患者数"
            sortable
            :sort-method="
              (a, b) => {
                return a.totalPatientCount - b.totalPatientCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newConsultPatientCount"
            label="新增医疗患者数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newConsultPatientCount - b.newConsultPatientCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newReConsultPatientCount"
            label="新增复诊患者数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newReConsultPatientCount - b.newReConsultPatientCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newPrescriptionCount"
            label="新增方案数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newPrescriptionCount - b.newPrescriptionCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newExecuteCount"
            label="新增执行方案数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newExecuteCount - b.newExecuteCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newPayCount"
            label="新增付费方案数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newPayCount - b.newPayCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newCiReCount"
            label="新增执行磁疗方案数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newCiReCount - b.newCiReCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newCiReUnitCount"
            label="新增执行磁疗人天数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newCiReUnitCount - b.newCiReUnitCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newGeWuCount"
            label="新增执行隔物灸方案数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newGeWuCount - b.newGeWuCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newGeWuUnitCount"
            label="新增隔物灸贴数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newGeWuUnitCount - b.newGeWuUnitCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newMaiZhenCount"
            label="新增执行埋针方案数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newMaiZhenCount - b.newMaiZhenCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newMaiZhenUnitCount"
            label="新增埋针次数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newMaiZhenUnitCount - b.newMaiZhenUnitCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newConsultCount"
            label="新增问诊/咨询数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newConsultCount - b.newConsultCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newPayConsultCount"
            label="新增付费问诊/咨询数"
            sortable
            :sort-method="
              (a, b) => {
                return a.newPayConsultCount - b.newPayConsultCount;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="profileDescription"
            label="资料是否完整"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="enableConsult"
            label="是否开启服务"
            sortable
            :sort-method="
              (a, b) => {
                return (a.enableConsult === '是' ? 100 : 0) - (b.enableConsult === '是' ? 100 : 0);
              }
            "
            min-width="60"
            show-overflow-tooltip
          />
          <el-table-column
            prop="showConsultCost"
            label="服务价格"
            sortable
            :sort-method="
              (a, b) => {
                return a.showConsultCost - b.showConsultCost;
              }
            "
            min-width="70"
            show-overflow-tooltip
          />
          <el-table-column
            prop="isLoginIn7Day"
            label="本周是否登录"
            sortable
            :sort-method="
              (a, b) => {
                return (a.isLoginIn7Day === '是' ? 1 : 0) - (b.isLoginIn7Day === '是' ? 1 : 0);
              }
            "
            min-width="60"
            show-overflow-tooltip
          />
          <el-table-column
            prop="isTest"
            label="是否测试数据"
            sortable
            :sort-method="
              (a, b) => {
                return (a.isTest === '是' ? 1 : 0) - (b.isTest === '是' ? 1 : 0);
              }
            "
            min-width="60"
            show-overflow-tooltip
          />
          <el-table-column fixed="right" label="操作" width="100">
            <template #default="scope">
              <el-button link type="primary" @click="onRemark(scope.row)">备注</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑备注 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    title="备注"
    width="500"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <RemarkForm
      :data="showDataDialog.data"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { ExportEnum } from "@/enums/Other";
import {
  DoctorStatusRedash,
  DoctorStatusStatisticsInputDTO,
  ExportTaskRedashDTO,
} from "@/api/report/types";
import { useUserStore } from "@/store";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

interface QueryParams extends RedashParameters<DoctorStatusStatisticsInputDTO> {
  orgIdList?: string[];
  deptIdList?: string[];
  assistantIdList?: string[];
}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "DoctorStatusStatistics",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  exportLoading,
} = useTableConfig<DoctorStatusRedash>();

let queryResultId = -1;

// 查询条件
const queryParams = reactive<QueryParams>({
  startTimeDt: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  endTimeDt: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  LoginUserId: useUserStore().userInfo.Id,
  assistantIds: undefined,
  deptIds: undefined,
  orgIds: undefined,
  pageIndex: 1,
  pageSize: 10,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.startTimeDt, queryParams.endTimeDt];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.startTimeDt = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.endTimeDt = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

watch(
  () => queryParams.orgIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.orgIds = undefined;
    } else {
      queryParams.orgIds = newVal?.join(",");
    }
  }
);

watch(
  () => queryParams.deptIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.deptIds = undefined;
    } else {
      queryParams.deptIds = newVal?.join(",");
    }
  }
);

watch(
  () => queryParams.assistantIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.assistantIds = undefined;
    } else {
      queryParams.assistantIds = newVal?.join(",");
    }
  }
);

// 添加/编辑备注
const showDataDialog = reactive({
  isShow: false,
  data: {} as DoctorStatusRedash,
});

// 禁用日期
function handleDisabledDate(date: Date) {
  return date.getTime() > Date.now();
}

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const { assistantIdList, deptIdList, orgIdList, ...parameters } = queryParams;
  const exportParams = convertToRedashParams(parameters, "Report_DoctorStatistics");
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `医生情况统计-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
    QueryName: "Report_DoctorStatistics",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 点击备注
function onRemark(row: DoctorStatusRedash) {
  kEnableDebug && console.debug("点击备注", row);

  if (!row.DocUserId) {
    ElMessage.error("医生Id为空");
    return;
  }

  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

// 确认提交
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确认提交备注");

  showDataDialog.isShow = false;
  ElNotification.success("备注成功");

  queryParams.pageIndex = 1;
  requestTableList();
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const { assistantIdList, deptIdList, orgIdList, ...parameters } = queryParams;

  const params = convertToRedashParams(parameters, "Report_DoctorStatistics");
  const r = await Report_Api.getRedashList<DoctorStatusRedash>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  queryResultId = r.Data.QueryResultId;
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
