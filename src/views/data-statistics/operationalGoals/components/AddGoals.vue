<template>
  <div v-loading="loading" class="container">
    <div class="header">
      <div class="header-item">
        <span class="label" style="margin-right: 10px">月份</span>
        <el-date-picker
          v-model="selectedMonth"
          type="month"
          placeholder="选择月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
          class="w180"
          :disabled="props.isEdit"
        />
      </div>
      <div class="header-info">
        <span>总计目标金额：¥{{ TotalAmount }}</span>
        <span>总计自运行目标：{{ TotalRows }}</span>
        <el-button type="primary" size="small" :loading="addRegionLoading" @click="addRegionGoal">
          添加地区目标
        </el-button>
      </div>
    </div>
    <!--  -->
    <el-card
      v-for="(item, index) in pageData"
      :id="`region-card-${index}`"
      :key="item.Region"
      class="mb-20px"
    >
      <div class="card-header flex justify-between">
        <div class="flex flex-1">
          <CityPicker
            v-model="item.Region"
            :is-only-city="true"
            style="width: 200px"
            @change="(val) => handleGetRegionChange(val as string, index)"
          />
        </div>
        <div class="flex justify-start items-center">
          <span class="mr-10px text-red">地区目标金额：¥{{ item.TotalAmount }}</span>
          <span class="mr-10px text-red">地区自运行目标：{{ item.TotalOperation }}</span>
          <el-button type="primary" size="small" @click="addHospitalGoal(index)">
            添加医院目标
          </el-button>
          <el-button type="danger" size="small" @click="deleteRegionGoal(index)">
            删除地区
          </el-button>
        </div>
      </div>
      <div class="card-content">
        <el-table
          border
          :data="item.Hospitals"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="OrgId" label="医院" align="center" width="220px">
            <template #default="{ row }">
              <el-select
                v-model="row.OrgId"
                placeholder="选择"
                filterable
                @change="(val) => handleHospitalChange(val, row, index)"
              >
                <el-option
                  v-for="option in availableHospitals(index)"
                  :key="option.Id"
                  :label="option.Name"
                  :value="option.Id!"
                  :disabled="option.disabled"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="AssistantId" label="医助" align="center" width="150px">
            <template #default="{ row }">
              <el-select
                v-model="row.AssistantId"
                placeholder="选择"
                clearable
                filterable
                @change="(val) => handleAssistantChange(val, row, index)"
              >
                <el-option
                  v-for="option in availableAssistants(index, row.OrgId)"
                  :key="option.Id"
                  :label="option.Name"
                  :value="option.Id!"
                  :disabled="option.disabled"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="TargetAmount" label="目标金额" align="center">
            <template #default="{ row }">
              <el-input
                v-model.number="row.TargetAmount"
                placeholder="请输入目标金额"
                :min="0"
                type="number"
                @change="(e) => resizeTargetAmountAndOperationTarget(item, e)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="OperationTarget" label="自运行目标" align="center">
            <template #default="{ row }">
              <el-input
                v-model.number="row.OperationTarget"
                placeholder="请输入自运行目标"
                type="number"
                :min="0"
                @change="(e) => resizeTargetAmountAndOperationTarget(item, e)"
              />
            </template>
          </el-table-column>
          <el-table-column label="管理负责人" align="center" width="80px">
            <template #default="{ row }">
              <span>{{ handleGetUserName("ManageLeaderName", row.OrgId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="销售负责人" align="center" width="80px">
            <template #default="{ row }">
              <span>{{ handleGetUserName("SaleLeaderName", row.OrgId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center" width="80px">
            <template #default="{ row }">
              <el-button link type="danger" @click="deleteHospitalGoal(index, row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { BusinessGoalInputDTO, BusinessGoalPageParams } from "@/api/passport/types";
import { calculateAmount } from "@/utils";
import { ElMessageBox, EpPropMergeTypeWithNull } from "element-plus";
import { nextTick } from "vue";

const hospitalOptions = ref<BaseOrganization[]>([]);
const doctorOptions = ref<BaseUserProfile[]>([]);
const selectedMonth = ref<string>("");
const TotalAmount = ref<number>(0);
const TotalRows = ref<number>(0);
const addRegionLoading = ref<boolean>(false);
const loading = ref<boolean>(false);
const pageData = ref<GroupedData[]>([]);
interface GroupedData {
  Region: string;
  RegionName: string;
  TotalAmount: number;
  TotalOperation: number;
  Hospitals: HospitalInfo[];
}

interface HospitalInfo {
  AssistantId: string;
  AssistantName: string;
  OperationTarget: number;
  OrgId: string;
  OrgName: string;
  TargetAmount: number;
  Id?: EpPropMergeTypeWithNull<string>;
}

const getHospitalListData = async () => {
  const res = await Passport_Api.getListByRegion({
    PageIndex: 1,
    PageSize: 99999,
    RegionCodes: [],
    IsEnabled: true,
  });
  if (res.Type === 200) {
    hospitalOptions.value = res.Data.Rows;
  } else {
    hospitalOptions.value = [];
  }
};

const getAssistantListData = async () => {
  const res = await Passport_Api.getUserProfile({
    PageIndex: 1,
    PageSize: 99999,
    RoleTypes: ["assistant"],
    IsEnabled: true,
    Pageable: true,
    Scopeable: true,
    Keyword: "",
    DtoTypeName: "QueryUserOutputDto3",
  });
  if (res.Type === 200) {
    doctorOptions.value = res.Data.Row;
  } else {
    doctorOptions.value = [];
  }
};

const onGetLastMonthData = async () => {
  const params = {
    Month: props.month,
  };
  loading.value = true;
  const res = await Passport_Api.getBusinessGoalPage(params);
  if (res.Type === 200) {
    const list = res.Data;
    // 按地区分组
    groupDataByRegion(list);
    // 获取总计目标金额
    TotalAmount.value = calculateAmount(
      list.map((item) => item.TargetAmount),
      "+"
    );
    // 获取总计自运行目标
    TotalRows.value = calculateAmount(
      list.map((item) => item.OperationTarget),
      "+"
    );
    if (props.isEdit) {
      selectedMonth.value = props.month;
    }
  }
  loading.value = false;
};

const groupDataByRegion = (data: BusinessGoalPageParams[]) => {
  const result = data.reduce((acc: GroupedData[], item) => {
    let regionGroup = acc.find((group) => group.Region === item.Region);

    if (!regionGroup) {
      regionGroup = {
        Region: item.Region,
        RegionName: item.RegionName,
        TotalAmount: 0,
        TotalOperation: 0,
        Hospitals: [],
      };
      acc.push(regionGroup);
    }

    regionGroup.TotalAmount += item.TargetAmount;
    regionGroup.TotalOperation += item.OperationTarget;
    regionGroup.Hospitals.push({
      OrgId: item.OrgId,
      OrgName: item.OrgName,
      TargetAmount: item.TargetAmount,
      OperationTarget: item.OperationTarget,
      AssistantId: item.AssistantId,
      AssistantName: item.AssistantName,
      Id: item.IdStr || null,
    });

    return acc;
  }, []);
  console.log(result);
  pageData.value = result;
};

const availableHospitals = (index: number) => {
  if (!pageData.value[index] || !pageData.value[index].Region) return [];
  // 只检查当前地区已选择的医院
  const currentRegion = JSON.parse(pageData.value[index].Region).slice(-1)[0].toString();
  const regionHospitals: BaseOrganization[] = hospitalOptions.value.filter(
    (item) => item.City && JSON.parse(item.City).includes(currentRegion)
  );
  // 返回处理后的选项列表
  return regionHospitals.map((option) => ({
    ...option,
    disabled: false,
    tip: "",
  }));
};

const availableAssistants = (index: number, orgId: string) => {
  if (!pageData.value[index] || !pageData.value[index].Hospitals) return [];
  let selectedAssistantIds: string[] = [];
  if (orgId) {
    // 选择了医院
    // 只检查当前医院已选择的医助
    selectedAssistantIds = pageData.value[index].Hospitals.filter((s) => s.OrgId === orgId).map(
      (s) => s.AssistantId
    );
  }
  // 返回处理后的选项列表
  return doctorOptions.value.map((option) => ({
    ...option,
    disabled: selectedAssistantIds.includes(option.Id!),
    tip: selectedAssistantIds.includes(option.Id!)
      ? `已在当前医院的第${selectedAssistantIds.indexOf(option.Id!) + 1}个医助选择`
      : "",
  }));
};

const handleGetUserName = (type: "ManageLeaderName" | "SaleLeaderName", OrgId: string) => {
  const hospital = hospitalOptions.value.find((item) => item.Id === OrgId);
  if (hospital) {
    return (hospital as any)[type];
  }
  return "";
};

const addHospitalGoal = (index: number) => {
  const currentRegion = pageData.value[index];
  currentRegion.Hospitals.push({
    OrgId: "",
    OrgName: "",
    AssistantId: "",
    AssistantName: "",
    OperationTarget: 0,
    TargetAmount: 0,
  });

  // 使用nextTick确保DOM已更新
  nextTick(() => {
    // 获取当前地区card内的所有行
    const currentCard = document.querySelector(`#region-card-${index}`);
    if (currentCard) {
      const rows = currentCard.querySelectorAll(".el-table__row");
      // 获取最后一行（新增的行）
      const lastRow = rows[rows.length - 1];
      if (lastRow) {
        // 平滑滚动到新增的行
        lastRow.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }
  });
};

const addRegionGoal = () => {
  pageData.value.unshift({
    Region: "",
    RegionName: "",
    TotalAmount: 0,
    TotalOperation: 0,
    Hospitals: [],
  });
};

const deleteHospitalGoal = async (index: number, row: HospitalInfo) => {
  const currentRegion = pageData.value[index];
  const rowIndex = currentRegion.Hospitals.findIndex((h) => h === row);
  currentRegion.Hospitals.splice(rowIndex, 1);
  resizeTargetAmountAndOperationTarget(currentRegion);
};

const deleteRegionGoal = (index: number) => {
  ElMessageBox.confirm("是否删除整个地区？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    pageData.value.splice(index, 1);
  });
};

const resizeTargetAmountAndOperationTarget = (currentRegion?: GroupedData, e?: string) => {
  if (currentRegion) {
    // 更新地区总计
    currentRegion.TotalAmount = calculateAmount(
      currentRegion.Hospitals.map((h) => h.TargetAmount),
      "+"
    );
    currentRegion.TotalOperation = calculateAmount(
      currentRegion.Hospitals.map((h) => h.OperationTarget),
      "+"
    );
  }
  // 更新总计
  TotalAmount.value = calculateAmount(
    pageData.value.map((region) => region.TotalAmount),
    "+"
  );
  TotalRows.value = calculateAmount(
    pageData.value.map((region) => region.TotalOperation),
    "+"
  );
};

const handleHospitalChange = (hospitalId: string, row: HospitalInfo, index: number) => {
  const hospital = hospitalOptions.value.find((h) => h.Id === hospitalId);
  if (hospital) {
    row.OrgName = hospital.Name ?? "";
  }
  if (checkDuplicateHospitalAndAssistant(index, hospitalId, row.AssistantId)) {
    ElMessage.warning("该医院和医助已存在，请选择其他医院和医助");
  }
};

const handleAssistantChange = (assistantId: string, row: HospitalInfo, index: number) => {
  const assistant = doctorOptions.value.find((d) => d.Id === assistantId);
  if (assistant) {
    row.AssistantName = assistant.Name;
  }
  if (checkDuplicateHospitalAndAssistant(index, row.OrgId, assistantId)) {
    ElMessage.warning("该医院和医助已存在，请选择其他医院和医助");
  }
};

const handleGetRegionChange = (name: string, index: number) => {
  if (!name) {
    pageData.value[index].RegionName = "";
    return;
  }
  const region = JSON.parse(name);
  const regionName = region.slice(-1)[0].toString();
  if (checkDuplicateRegion(regionName, index)) {
    ElMessage.warning("该地区已被选择，请选择其他地区");
  }
  pageData.value[index].RegionName = regionName;
  pageData.value[index].Hospitals.forEach((s) => {
    s.OrgName = "";
    s.OrgId = "";
  });
};

// 检查地区是否重复
const checkDuplicateRegion = (regionName: string, currentIndex: number) => {
  return pageData.value.some((item, index) => {
    return index !== currentIndex && item.RegionName && item.RegionName === regionName;
  });
};
// 检查医院和医助是否有重复的
const checkDuplicateHospitalAndAssistant = (index: number, orgId: string, assistantId: string) => {
  return (
    pageData.value[index].Hospitals.filter(
      (s) => assistantId && orgId && s.OrgId === orgId && s.AssistantId === assistantId
    ).length > 1
  );
};

const validIsCanSubmit = (): boolean => {
  if (!selectedMonth.value) {
    ElMessage.warning("请选择月份");
    return false;
  }
  // 2. 验证地区数据
  const invalidRegions = pageData.value.filter((Region, index) => {
    if (!Region.Region) return false; // 跳过未选择地区的数据
    if (checkDuplicateRegion(Region.RegionName, index)) {
      ElMessage.warning(`${Region.RegionName}地区已存在，请选择其他地区`);
      return true;
    }
    return !validateRegion(Region, index);
  });
  if (invalidRegions.length) {
    return false;
  }

  return true;
};

const validateRegion = (region: GroupedData, index: number): boolean => {
  if (!region.Region || !region.Hospitals || !region.Hospitals.length) {
    ElMessage.warning(`第${index + 1}个地区没有选择医院，请选择医院`);
    return false;
  }
  // 检查医院数据
  const invalidHospitals = region.Hospitals.filter(
    (hospital) => !validateHospital(hospital, index)
  );
  // 如果有无效的医院数据，显示具体提示
  if (invalidHospitals.length > 0) {
    return false;
  }

  return true;
};

const validateHospital = (hospital: HospitalInfo, index: number): boolean => {
  if (!hospital.OrgId) {
    ElMessage.warning("医院没有选择，请选择医院");
    return false;
  }
  // 检查医助数据 不管是否选择了医助 只要目标金额为空 就认为不完整
  const invalidDoctors = hospital.TargetAmount;
  if (!invalidDoctors) {
    ElMessage.warning(`第${index + 1}个地区目标金额必填，请输入目标金额`);
    return false;
  }
  if (hospital.AssistantId && !hospital.OperationTarget && hospital.OperationTarget !== 0) {
    ElMessage.warning(`第${index + 1}个地区医助自运行目标必填，请输入自运行目标`);
    return false;
  }
  if (checkDuplicateHospitalAndAssistant(index, hospital.OrgId, hospital.AssistantId)) {
    ElMessage.warning(
      `在第${index + 1}个地区中，${hospital.OrgName}医院和${hospital.AssistantName}医助存在相同的数据，请选择其他医院和医助`
    );
    return false;
  }
  return true;
};

const handleSubmit = (): BusinessGoalInputDTO[] | null => {
  if (!validIsCanSubmit()) {
    return null;
  }
  const params: BusinessGoalInputDTO[] = [];
  pageData.value.forEach((s) => {
    if (!s.Region) return;
    s.Hospitals.forEach((hospital) => {
      params.push({
        Month: selectedMonth.value,
        Region: s.Region,
        RegionName: s.RegionName,
        OrgId: hospital.OrgId,
        OrgName: hospital.OrgName,
        AssistantId: hospital.AssistantId,
        AssistantName: hospital.AssistantName,
        TargetAmount: Number(hospital.TargetAmount),
        OperationTarget: Number(hospital.OperationTarget),
        Id: props.isEdit ? hospital.Id : null,
      });
    });
  });
  return JSON.parse(JSON.stringify(params));
};

onMounted(async () => {
  // 获取所有机构
  await getHospitalListData();
  // 获取所有医助
  getAssistantListData();
});
defineExpose({
  handleSubmit,
});

interface Props {
  month: string;
  isEdit: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  month: "",
  isEdit: false,
});
watch(
  () => props.month,
  (newVal) => {
    if (newVal) {
      // 获取上个月的数据
      onGetLastMonthData();
    }
  },
  { immediate: true }
);
</script>
<style scoped lang="scss">
.container {
  position: relative;
  height: 600px;
  padding: 20px;
  overflow-y: auto;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-info {
    display: flex;
    align-items: center;
    gap: 20px;

    span {
      color: #f56c6c;
      font-weight: bold;
    }
  }
}
.card-header {
  margin-bottom: 10px;
}
</style>
