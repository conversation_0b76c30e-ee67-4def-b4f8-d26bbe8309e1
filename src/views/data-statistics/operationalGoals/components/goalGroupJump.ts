import dayjs from "dayjs";
import { type BusinessGoalCompletionDetailWithChildren } from "./GroupDetails.vue";
import router from "@/router";

// 定义 group 可接受的值类型
type GroupType = "region" | "assistant" | "personCharge";

// 提取获取组织ID的辅助函数
const getChildrenIds = (row: BusinessGoalCompletionDetailWithChildren): string[] => {
  console.log("row", row);
  return row.Children?.map((item) => item.Id) ?? [];
};

// 构建参数映射的辅助函数
const buildGroupParams = (
  row: BusinessGoalCompletionDetailWithChildren,
  group: GroupType,
  isHaveChildren: boolean
): Record<string, any> => {
  console.log("isHaveChildren", isHaveChildren);
  const mappings = {
    region: {
      OrgIds: isHaveChildren ? getChildrenIds(row) : [row.Id],
    },
    assistant: { AssistantId: row.Id },
    personCharge: isHaveChildren ? { OrgIds: getChildrenIds(row) } : {},
  };
  console.log("mappings[group]", mappings[group]);
  return mappings[group] || {};
};

export const handleGoalGroupJump = (
  row: BusinessGoalCompletionDetailWithChildren,
  type: string,
  group: GroupType,
  dataRange: string[],
  dayRange: string[]
) => {
  console.log("row", row);
  const isHaveChildren = (row.Children?.length ?? 0) > 0;
  const dateRange = type.includes("Month") ? dayRange : dataRange;

  const params: Record<string, any> = {
    TimeType: 2,
    BeginTime: dayjs(dateRange[0]).format("YYYY-MM-DD"),
    EndTime: dayjs(dateRange[1]).format("YYYY-MM-DD"),
    ...(type.includes("SelfReliance") && { SelfReliance: true }),
  };

  Object.assign(params, buildGroupParams(row, group, isHaveChildren));
  console.log("params", params);
  const queryString = new URLSearchParams(params).toString();
  console.log(queryString);
  router.push({
    path: "/medical-procedure/treatmentSchemeQuery",
    query: {
      ...params,
    },
  });
};
