<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="是否展示无数据医院">
                <el-select
                  v-model="queryParams.DisplayEmptyData"
                  placeholder="请选择"
                  clearable
                  style="width: 100px"
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length <= 0"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          :header-cell-style="headerCellStyle"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column
            prop="Name"
            label="医院"
            show-overflow-tooltip
            fixed
            :filters="filterName"
            :filter-method="filterMethod"
            width="100"
            align="center"
          />
          <el-table-column
            prop="CityName"
            label="地区"
            fixed
            :filters="filterCityName"
            :filter-method="filterMethod"
            show-overflow-tooltip
            width="60"
            align="center"
          />
          <el-table-column
            prop="DailyConsultCount"
            label="新增问诊数"
            show-overflow-tooltip
            align="center"
            header-cell-class-name="daily-consult-header"
          />
          <el-table-column
            prop="DailyAdvisoryCount"
            label="新增咨询数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyDoctorQuickConsultCount"
            label="新增快速下方数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyDoctorPositiveCount"
            label="新增医生主动开出方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyThePositiveCount"
            label="新增治疗师主动开出方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyPaitentConsultCount"
            label="新增患者问诊开出方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyPaitentAdvisoryCount"
            label="新增患者咨询开出方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyYunDongCount"
            label="新增含运动疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyGeWuCount"
            label="新增含隔物灸法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyMaiZhenCount"
            label="新增含埋针疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyCiReCount"
            label="新增含磁热疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyKangFuPingDingCount"
            label="新增含康复评定方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyZeroAmountCount"
            label="新增无费用方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyNonZeroAmountCount"
            label="新增含费用方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyExecutedContainAmountCount"
            label="新增已执行含费用方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyExecutedAmount"
            label="新增已执行含费用方案费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyExecutedCount"
            label="新增已执行方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="DailyPrescriptionCount"
            label="新增方案总数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalConsultCount"
            label="累计问诊数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalAdvisoryCount"
            label="累计咨询数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalYunDongCount"
            label="累计含运动疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalGeWuCount"
            label="累计含隔物灸法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalMaiZhenCount"
            label="累计含埋针疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalCiReCount"
            label="累计含磁热疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalKangFuPingDingCount"
            label="累计含康复评定方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalExecutedContainAmountCount"
            label="累计已执行含费用方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalExecutedAmount"
            label="累计已执行含费用方案费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalExecutedCount"
            label="累计已执行方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalAmountZeroAmountCount"
            label="累计无费用方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalNonZeroAmountCount"
            label="累计含费用方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="TotalPrescriptionCount"
            label="累计方案总数"
            show-overflow-tooltip
            align="center"
          />
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import Report_Api from "@/api/report";
import {
  ExportTaskRedashDTO,
  PrescriptionSummaryInputDTO,
  PrescriptionSummaryItem,
} from "@/api/report/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
const userStore = useUserStore();
import dayjs from "dayjs";
import { Filters } from "element-plus/es/components/table/src/table-column/defaults";
import { ExportEnum } from "@/enums/Other";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "TreatmentRegimenDataStatistics",
  inheritAttrs: false,
});

const queryParams = ref<PrescriptionSummaryInputDTO>({
  DisplayEmptyData: "0",
  EndTimeDt: dayjs().format("YYYY-MM-DD 23:59:59"),
  StartTimeDt: dayjs().format("YYYY-MM-DD 00:00:00"),
  LoginUserId: "",
  PageIndex: 1,
  PageSize: 10,
  orgIds: null,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD"),
]);
const filterName = ref<Filters>([]);
const filterCityName = ref<Filters>([]);
const exportLoading = ref<boolean>(false);
const queryResultId = ref<number>(0);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<PrescriptionSummaryItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  queryParams.value.LoginUserId = userStore.userInfo.Id;
  tableLoading.value = true;
  const redashParams = convertToRedashParams(queryParams.value, "Report_PrescriptionSummary");
  redashParams.pageSize = 99999;
  const res = await Report_Api.getRedashList<PrescriptionSummaryItem>(redashParams);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
    queryResultId.value = res.Data.QueryResultId;
    handleGetFilterData();
  }
  tableLoading.value = false;
};

const handleGetFilterData = () => {
  // 对筛选出来的数据进行去重
  const newDataName = pageData.value.map((v) => ({
    text: v.Name,
    value: v.Name,
  }));
  filterName.value = [...new Map(newDataName.map((item) => [item.text, item])).values()];
  const newDataCityName = pageData.value.map((v) => ({
    text: v.CityName,
    value: v.CityName,
  }));
  filterCityName.value = [...new Map(newDataCityName.map((item) => [item.text, item])).values()];
};

const filterMethod = (value: string, row: PrescriptionSummaryItem, column: any) => {
  const property = column["property"];
  return row[property as keyof PrescriptionSummaryItem] === value;
};

const handleExportExcel = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  const exportParams = convertToRedashParams<PrescriptionSummaryInputDTO>(
    copyData,
    "Report_PrescriptionSummary"
  );
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `治疗方案数据统计-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId.value,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.value.PageIndex,
    PageSize: queryParams.value.PageSize,
    QueryName: "Report_PrescriptionSummary",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.StartTimeDt = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTimeDt = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});

const headerCellStyle = ({ column }: { column: any }) => {
  const colorsMap = {
    "#65A33E": [
      "DailyConsultCount",
      "DailyAdvisoryCount",
      "DailyDoctorQuickConsultCount",
      "DailyDoctorPositiveCount",
      "DailyThePositiveCount",
      "DailyPaitentConsultCount",
      "DailyPaitentAdvisoryCount",
      "DailyYunDongCount",
      "DailyGeWuCount",
      "DailyMaiZhenCount",
      "DailyCiReCount",
      "DailyKangFuPingDingCount",
      "DailyZeroAmountCount",
      "DailyNonZeroAmountCount",
      "DailyExecutedContainAmountCount",
      "DailyExecutedAmount",
      "DailyExecutedCount",
      "DailyPrescriptionCount",
    ],
  };

  // 查找当前列使用的背景色
  for (const [bgColor, columns] of Object.entries(colorsMap)) {
    if (columns.includes(column.property)) {
      return {
        backgroundColor: bgColor,
        color: "#303133",
        fontWeight: "bold",
      };
    }
  }

  // 默认样式
  return {
    backgroundColor: "#f5f7fa",
    color: "#606266",
  };
};

const getSummaries = (param: { columns: any[]; data: any[] }) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }
    if (index === 1) {
      sums[index] = "0";
      return;
    }

    const values = data.map((item) => Number(item[column.property]) || 0);
    const sum = values.reduce((prev, curr) => {
      return prev + curr;
    }, 0);

    sums[index] = `${sum}`;
  });

  return sums;
};
</script>

<style lang="scss"></style>
