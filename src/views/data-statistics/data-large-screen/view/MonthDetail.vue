<template>
  <div>
    <Title title="月收入详情">
      <div ref="chartRef" class="w-100% h-100%" />
    </Title>
  </div>
</template>
<script setup lang="ts">
import Report_Api from "@/api/report";
import Title from "./Title.vue";
import * as echarts from "echarts";

const chartRef = useTemplateRef<HTMLElement>("chartRef");
const chart = shallowRef<echarts.ECharts | null>(null);
const chartData = ref({
  xData: [] as string[],
  yData: [] as number[],
});

onMounted(() => {
  chart.value = echarts.init(chartRef.value);
});

const loadData = async () => {
  const res = await Report_Api.getRedashList<{ Date: string; Amount: number }>({
    queryName: "Report_MonthlyIncomeTrend",
    parameters: {},
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 10,
  });
  if (res.Type === 200) {
    const xData = res.Data.Data.map((v) => v.Date)
      .map((date) => date.split("-")[1])
      .map((s) => s + "月")
      .reverse();
    console.log("xData", xData);
    chartData.value.xData = xData;
    chartData.value.yData = res.Data.Data.map((v) => Number(v.Amount)).reverse();
    renderChart();
  }
};
const renderChart = () => {
  chart.value?.setOption({
    color: ["#3196FA"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    grid: {
      top: "10%",
      left: "0%",
      right: "5%",
      bottom: "5%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {
          show: false,
        },
        dataView: {
          show: false,
        },
      },
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: chartData.value.xData,
        axisLabel: {
          interval: 0,
          color: "white", // 设置 y 轴文字颜色为蓝色
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: (value: number) => {
            return (value / 1000).toFixed(1) + "k";
          },
          color: "white", // 设置 y 轴文字颜色为蓝色
        },
        splitLine: {
          lineStyle: {
            // 设置背景横线
            color: "rgba(255,255,255,0.1)",
            type: "dashed",
          },
        },
      },
    ],
    series: [
      {
        type: "line",
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#3196FA" },
            { offset: 0.5, color: "#1871c9" },
            { offset: 1, color: "rgba(182,229,187,0.1)" },
          ]),
        },
        lineStyle: {
          color: "#3196FA",
          width: 3,
        },
        emphasis: {
          focus: "series",
        },
        smooth: true,
        data: chartData.value.yData,
      },
    ],
  });
};

onMounted(() => {
  loadData();
});

defineExpose({
  loadData,
});
</script>
