<template>
  <div>
    <Title title="开方数详情">
      <div ref="chartRef" class="w-100% h-100%" />
    </Title>
  </div>
</template>
<script setup lang="ts">
import Report_Api from "@/api/report";
import Title from "./Title.vue";
import * as echarts from "echarts";

const chartRef = useTemplateRef<HTMLDivElement>("chartRef");
const chart = ref<echarts.ECharts>();

onMounted(() => {
  chart.value = echarts.init(chartRef.value);
});

const loadData = async () => {
  const params = {
    queryName: "Report_PrescriptionCountTrend",
    parameters: {},
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 10,
  };

  const res = await Report_Api.getRedashList<{
    Date: string;
    PrescriptionCount: number;
    TotalExecutedCount: number;
  }>(params);
  if (res.Type === 200) {
    const xData = res.Data.Data.map((v) => v.Date)
      .map((date) => date.split("-")[1])
      .map((s) => s + "月")
      .reverse();
    const chartData = {
      xData,
      KFCount: res.Data.Data.map((v) => Number(v.PrescriptionCount)).reverse(),
      ZXCount: res.Data.Data.map((v) => Number(v.TotalExecutedCount)).reverse(),
    };
    changeCharts(chartData);
  }
};

const changeCharts = (chartData: { xData: string[]; KFCount: number[]; ZXCount: number[] }) => {
  chart.value?.setOption({
    color: ["#3196FA", "#13C179"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    legend: {
      textStyle: {
        fontSize: 16,
        color: "white", // 设置图例文字颜色为红色
      },
      data: ["开方数", "执行数"],
      bottom: "0",
    },
    grid: {
      top: "10%",
      left: "0%",
      right: "5%",
      bottom: "10%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {
          show: false,
        },
        dataView: {
          show: false,
        },
      },
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: chartData.xData,
        axisLabel: {
          interval: 0,
          color: "white", // 设置 y 轴文字颜色为蓝色
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: "{value}个",
          color: "white", // 设置 y 轴文字颜色为蓝色
        },
        splitLine: {
          lineStyle: {
            // 设置背景横线
            color: "rgba(255,255,255,0.1)",
            type: "dashed",
          },
        },
      },
    ],
    series: [
      {
        name: "开方数",
        type: "line",
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#3196FA" },
            { offset: 0.5, color: "#1871c9" },
            { offset: 1, color: "rgba(182,229,187,0.1)" },
          ]),
        },
        lineStyle: {
          color: "#3196FA",
          width: 3,
        },
        emphasis: {
          focus: "series",
        },
        smooth: true,
        data: chartData.KFCount,
      },
      {
        name: "执行数",
        type: "line",
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#13C179" },
            { offset: 0.5, color: "#10a366" },
            { offset: 1, color: "rgba(182,229,187,0.1)" },
          ]),
        },
        lineStyle: {
          color: "#13C179",
          width: 3,
        },
        emphasis: {
          focus: "series",
        },
        smooth: true,
        data: chartData.ZXCount,
      },
    ],
  });
};
onMounted(() => {
  loadData();
});

defineExpose({
  loadData,
});
</script>
