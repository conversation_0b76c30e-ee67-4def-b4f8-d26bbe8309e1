<template>
  <div class="title">
    <div class="title-bgImg">
      <div class="title-bgImg-bg">
        <span class="title-bgImg-bg-title">{{ title }}</span>
      </div>
    </div>
    <slot />
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: "",
  },
});
</script>

<style scoped lang="scss">
.title {
  width: 100%;
  height: 100%;
  padding: 4px;
  &-bgImg {
    width: 100%;
    height: 40px;
    background-image: url("../assets/bgimg.png");
    background-size: 100% 100%;
    position: relative;
    &-bg {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 85%;
      position: absolute;
      right: 0;
      top: 0;
      bottom: 30%;
      &-title {
        background: linear-gradient(180deg, #ffffff 0%, #54b8fe 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 20px;
        font-family: YouSheBiaoTiHei;
        color: #1ad7ff;
        font-family: "YouSheBiaoTiHei";
      }
    }
  }
}
</style>
