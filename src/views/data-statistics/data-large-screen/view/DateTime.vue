<template>
  <div class="date-time">
    <span class="date-time-time">{{ time }}</span>
    <div class="date-time-date">
      <div>{{ week }}</div>
      <div class="mt-1">{{ date }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
const now = useNow();
const weekMap = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
const week = computed(() => weekMap[now.value.getDay()]);
const date = computed(() => now.value.toLocaleDateString());
const time = computed(() => now.value.toLocaleTimeString());
</script>
<style scoped lang="scss">
.date-time {
  display: flex;
  align-items: center;
  &-time {
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
  }
  &-date {
    font-weight: 400;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-left: 12px;
  }
}
</style>
