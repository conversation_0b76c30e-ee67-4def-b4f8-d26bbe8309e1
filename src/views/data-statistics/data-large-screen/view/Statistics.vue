<template>
  <div class="statistics c-white">
    <div class="statistics-box">
      <span class="statistics-box-count">{{ info.OrganizationCount ?? "--" }}</span>
      <br />
      <span class="statistics-box-name">累计注册医院</span>
    </div>
    <div class="statistics-box">
      <span class="statistics-box-count">{{ info.AuthDoctorCount ?? "--" }}</span>
      <br />
      <span class="statistics-box-name">累计注册医生</span>
    </div>
    <div class="statistics-box">
      <span class="statistics-box-count">{{ info.AuthTherapistCount ?? "--" }}</span>
      <br />
      <span class="statistics-box-name">累计注册治疗师</span>
    </div>
    <div class="statistics-box">
      <span class="statistics-box-count">{{ info.NormalUserCount ?? "--" }}</span>
      <br />
      <span class="statistics-box-name">累计注册患者</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import Report_Api from "@/api/report";
interface InfoEntity {
  OrganizationCount: number | null;
  AuthDoctorCount: number | null;
  AuthTherapistCount: number | null;
  NormalUserCount: number | null;
}

const info = ref<InfoEntity>({
  OrganizationCount: null,
  AuthDoctorCount: null,
  AuthTherapistCount: null,
  NormalUserCount: null,
});
const loadData = async () => {
  const res = await Report_Api.getRedashList<InfoEntity>({
    queryName: "Report_PlatformHomePageSummary",
    parameters: {},
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 10,
  });
  if (res.Type === 200) {
    info.value = res.Data.Data[0];
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.statistics {
  min-width: 570px;
  height: 70px;
  margin: 0 auto;
  background-image: url("../assets/topbg.png");
  background-size: 100% 100%;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  padding: 0 40px;
  gap: 10px;
  &-box {
    text-align: center;
    &-count {
      font-size: 30px;
      font-family: DINAlternate, DINAlternate;
      font-weight: bold;
      color: #ffffff;
      background: linear-gradient(180deg, #ffffff 0%, #ffa72d 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
</style>
