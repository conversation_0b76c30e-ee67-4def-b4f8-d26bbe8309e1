<template>
  <div ref="mapRef" class="map" />
</template>
<script lang="ts">
import * as echarts from "echarts";
import sichuanMap from "../assets/sichuan.json";
import { debounce } from "lodash-es";
echarts.registerMap("sichuan", sichuanMap as any);

interface MapData {
  LatLon: string;
  Name: string;
  PrescriptionCnt: number;
  Amount: number;
}
type MapSeriesOption = echarts.SeriesOption & {
  name: string;
  value: [number, number, string, number, number];
  label: { show: boolean };
};

const _IMAGE =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAATCAYAAAB2pebxAAAAAXNSR0IArs4c6QAAAnxJREFUOE+VlE1IVUEUx8+5V319UIugTRBZmwhaSZ+vSArMXNSi4iVR1iOiFkVk4cqQByXPRUgQCFGUQgmCkPTxAgsKI0MxihZlaWaCBNqX4p2ZO2fmxL1P7Rnvid7lnJnf/Z//mf8gZPlMd7LeGmrN31L9Mlv9/zXMCulJNoNW+9ioE240vwUxYWeD5YDUNbMW5UjCgPGrncUTV3HDDZ0LlFMJa1kOJABIApBodIf80xi/I7OBZlXyD+IBGNnlDrvF2UDIg+0VuKqkKfMPpifZnFYSqgAgD0ALACuGvD8/ipYefj6auR/twJNPbEWH0/T2JCbSBuqu2mLH6gdMYskMiBEARvy25JcVHHj6egqE9muqn0kUOkamQOftx/UxPyj6nTVFrpWtrGXhtBIjgEkAkifAqDNurPM2IjDyQKqcSd5kIxYxed3u2FgZRit/BiB+cXE1WXUXjdwatmM84LBFL2jTgFF1efl+IjSW+1r2WKL7QF4ESH5xfG8bbqr6HtZenV9GE+P3kGQpTCqZhAQgZpKNyIOPD7KWF5jEZiDhA4lrzq/RGtyZkPz5bMR8s0fB+rVAYjkHnkwZHbRl5AhodS7tiRZr0Mh+RBXHtfGOQIHqrF7nGn0dSOwKD2oBmRDHyPek/EORijcf0A6kesGqd1jgH8OVMREAqOvKcbS6gUksSI95csQm8EJaMF6bOzp+BE/1eMF+5OH27biiZEbQct4TIxRaWensfdQQTGV6xNkDmM5O5mVDkiMOejuwtPXjnFM8IztGPnMXUgyjt8LRzwsSpliry04E6zBaH/o1T4jaDSTjbnvBw6k4zA/SnaxyQLfhxku9c3nZ/gLY+8Z4K+EmvgAAAABJRU5ErkJggg==";

const geoOption = {
  map: "sichuan",
  center: [102.920786, 30.31266],
  scaleLimit: {
    // 最小最大缩放倍数
    min: 1,
    max: 999,
  },
  silent: true,
  roam: true,
  tooltip: { show: false },
  label: {
    color: "#fff",
    show: true,
  },
  itemStyle: {
    areaColor: {
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#073684", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "#061E3D", // 100% 处的颜色
        },
      ],
    },
    borderColor: new echarts.graphic.LinearGradient(
      0,
      0,
      0,
      1,
      [
        {
          offset: 0,
          color: "#00F6FF",
        },
        {
          offset: 1,
          color: "#87ADCB",
        },
      ],
      false
    ),
    shadowColor: "rgba(10,76,139,1)",
    shadowOffsetY: 0,
    shadowBlur: 60,
    borderWidth: 1,
  },
  // emphasis: {
  //   label: {
  //     color: '#fff',
  //     show: true,
  //   },
  //   itemStyle: {
  //     areaColor: {
  //       x: 0,
  //       y: 0,
  //       x2: 0,
  //       y2: 1,
  //       colorStops: [
  //         {
  //           offset: 0,
  //           color: '#073684', // 0% 处的颜色
  //         },
  //         {
  //           offset: 1,
  //           color: '#2B91B7', // 100% 处的颜色
  //         },
  //       ],
  //     },
  //   },
  // },
  // regions: [
  //   {
  //     name: '南海诸岛',
  //     itemStyle: {
  //       // 隐藏地图
  //       opacity: 0, // 为 0 时不绘制该图形
  //     },
  //     label: {
  //       show: false, // 隐藏文字
  //     },
  //   },
  // ],
};
const toolTipOption = {
  show: true,
  trigger: "item",
  position: "top",
  backgroundColor: "#093172",
  borderWidth: 0,
  padding: 12,
  hideDelay: 0,
  transitionDuration: 0,
  extraCssText: "opacity: 0.8; ",
  formatter: (params: { data: MapSeriesOption }) => {
    // console.log('formatter', params);
    return `
<div>
  <image src="${_IMAGE}" width="12px" height="12px" />
  <span style="color: #d8a04c; font-size: 20px;  font-family: YouSheBiaoTiHei;">${params.data.value[2]}</span>
<br />
<span style="color: #CCCCCC; font-size: 14px; line-height: 22px;">开方数量: <span style="color: #FFFFFF; font-size: 14px; line-height: 22px;">${params.data.value[3]}次</span></span>
<br />
<span style="color: #CCCCCC; font-size: 14px; line-height: 22px;">开方收入: <span style="color: #FFFFFF; font-size: 14px; line-height: 22px;">${params.data.value[4]}元</span></span>
</div>`;
  },
};

const seriesOption = {
  id: "first",
  name: "Top 3",
  type: "scatter",
  coordinateSystem: "geo",
  animationDuration: 0,
  tooltip: toolTipOption,
  label: {
    show: true,
    color: "#fff",
    position: "top",
    backgroundColor: "#093172",
    padding: 12,
    fontSize: 16,
    overflow: "break",
    formatter: [
      `{icon|}{title| {@[2]} }`,
      `{key|开方数量：}{value| {@[3]}次}`,
      `{key|开方收入：}{value| {@[4]}元}`,
    ].join("\n"),
    rich: {
      icon: {
        backgroundColor: {
          image: _IMAGE,
        },
      },
      title: {
        color: "#d8a04c",
        fontSize: 20,
        background: "linear-gradient(180deg, #FFFFFF 0%, #54B8FE 100%)",
        "-webkitBackgroundClip": "text",
        "-webkit-text-fill-color": "transparent",

        fontFamily: "YouSheBiaoTiHei",
      },
      key: {
        color: "#CCCCCC",
        fontSize: 14,
        lineHeight: 22,
      },
      value: {
        color: "#FFFFFF",
        fontSize: 14,
        lineHeight: 22,
      },
    },
  },
  itemStyle: {
    opacity: 0.8,
  },
  symbolSize: (value: any, params: { dataIndex: number }) => (params.dataIndex < 3 ? 30 : 20),
  symbol: (value: any, params: { dataIndex: number }) => {
    return params.dataIndex < 3
      ? // 定位图标样式

        "image://data:image/png;base64,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"
      : "image://data:image/png;base64,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";
  },
};
const secondSeriesOption = {
  id: "second",
  name: "other",
  type: "scatter",
  coordinateSystem: "geo",
  animationDuration: 0,
  tooltip: toolTipOption,
  itemStyle: {
    opacity: 0.8,
  },
  symbolSize: 20,

  symbol:
    "image://data:image/png;base64,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",
};
const initOption = {
  geo: geoOption,
  tooltip: {
    trigger: "item",
  },
  series: [seriesOption, secondSeriesOption],
};

import type { OrgEntity } from "../index.vue";

export default defineComponent({
  props: {
    /**机构列表 */
    list: {
      type: Array as PropType<OrgEntity[]>,
      default: () => [],
    },
  },
  setup() {
    const mapRef = useTemplateRef<HTMLElement>("mapRef");
    let chart = null as null | echarts.ECharts;
    let moving = false;
    let lazyEvent = null as null | any;
    let lazyList = null as null | MapData[];
    let chartData = [] as MapSeriesOption[];

    const resizeChart = () => {
      chart?.resize();
    };

    // const listenEvent = (eventName: string) => {
    //   chart.value?.on(eventName, (event) => {
    //     console.log(eventName, event);
    //   });
    // };
    const hideLabel = (event: any) => {
      if (moving) {
        lazyEvent = event;
        return;
      }
      lazyEvent = null;

      const { seriesIndex, dataIndex } = event;
      if (seriesIndex === 0 && dataIndex < 3 && chartData[dataIndex].label.show) {
        console.log("hideLabel", event);
        // return;
        const data = chartData;
        data[dataIndex].label.show = false;
        chart?.setOption({
          series: [{ id: "first", data: data.slice(0, 3) }],
        });
      }
    };

    const onStopMove = debounce(() => {
      moving = false;
      // console.log('move stop');
      lazyEvent && hideLabel(lazyEvent);
      lazyList && setChartData(lazyList);
    }, 1000);

    const setChartData = (list: MapData[]) => {
      // console.log('setChartData');
      if (moving) {
        lazyList = list;
        return;
      }
      lazyList = null;

      const showOrg = [] as MapSeriesOption[];
      list
        .filter((v) => v.LatLon)
        .forEach((v, index) => {
          showOrg.push({
            name: v.Name,
            value: [
              Number(v.LatLon.split(",")[0]),
              Number(v.LatLon.split(",")[1]),
              `NO.${index + 1} ${v.Name}`,

              v.PrescriptionCnt,
              v.Amount,
            ],
            label: {
              show: index < 3,
              // show: false,
            },
            // tooltip: {
            //   show: index >= 3,
            // },
          });
        });
      chartData = showOrg;

      const newSeries = [];
      if (showOrg.length < 3) {
        newSeries.push({
          id: "first",
          data: chartData,
        });
      } else {
        newSeries.push({
          id: "first",
          data: showOrg.slice(0, 3),
        });
        newSeries.push({
          id: "second",
          data: showOrg.slice(3),
        });
      }
      chart?.setOption({
        series: newSeries,
      });
      chart?.hideLoading();
    };

    onMounted(() => {
      chart = echarts.init(mapRef.value);
      chart.showLoading("default", {
        text: "加载中...",
        // color: '#c23531',
        textColor: "white",
        maskColor: "rgba(0, 0, 0, 0.1)",
      });
      chart.setOption(initOption);
      chart.hideLoading();
      // pad 上点击事件会同时触发 mouseover
      chart.on("mouseover", "series", (event) => {
        // console.log('mouseover', event);
        hideLabel(event);
      });

      chart.on("georoam", () => {
        moving = true;
        onStopMove();
      });
    });

    onUnmounted(() => {
      chart?.dispose();
    });

    return {
      resizeChart,
      setChartData,
    };
  },
  watch: {
    list: {
      handler(newVal) {
        this.setChartData(newVal);
      },
    },
  },
  mounted() {
    window.addEventListener("resize", this.resizeChart);
  },

  destroyed() {
    window.removeEventListener("resize", this.resizeChart);
  },
});
</script>
<style scoped lang="scss">
.map {
  width: 100% !important;
  height: 90% !important;
}
</style>
