<template>
  <div ref="fullscreenDiv" class="page-root">
    <VScaleScreen>
      <div class="screen-root">
        <!-- 头部 -->
        <div class="screen-root-top">
          <!-- 全屏按钮 -->
          <div
            :class="`i-svg:${isFullscreen ? 'fullscreen1-exit' : 'fullscreen1'} c-white absolute top-10px right-10px`"
            @click="toggle"
          />
          <!-- 时间 -->
          <DateTime class="absolute top-30px right-30px" />
        </div>
        <!-- 中间 -->
        <div class="screen-root-mid c-white">
          <div class="min-w-350px w-25%">
            <!-- 医院收入排名 -->
            <Left :list="orgList" @changeTime="changeTime" />
          </div>
          <div class="flex-1 flex flex-col items-center">
            <!-- 地图上方的统计 -->
            <Statistics />
            <!-- 地图 -->
            <MapChart class="flex-1 self-stretch" :list="orgList" />
          </div>
          <div class="min-w-350px w-25% flex flex-col items-stretch">
            <!-- 累计收入 -->
            <AccumulatedIncome ref="chartRightTop" class="h-30%" />
            <!-- 月收入 -->
            <MonthDetail ref="chartRightMid" class="flex-1" />
            <!-- 开方数 -->
            <QuantityDetail ref="chartRightBottom" class="flex-1" />
          </div>
        </div>
        <!-- 底部 -->
        <div class="screen-root-footer" />
      </div>
    </VScaleScreen>
  </div>
</template>
<script setup lang="ts">
import Report_Api from "@/api/report";
import dayjs from "dayjs";
import Left from "./view/Left.vue";
import Statistics from "./view/Statistics.vue";
import MonthDetail from "./view/MonthDetail.vue";
import QuantityDetail from "./view/QuantityDetail.vue";
import AccumulatedIncome from "./view/AccumulatedIncome.vue";
import MapChart from "./view/MapChart.vue";
import VScaleScreen from "@/components/VScaleScreen.vue";
import DateTime from "./view/DateTime.vue";

export interface OrgEntity {
  Name: string;
  Amount?: string | number;
  Percentage?: string | number;
}

interface SubChart extends ComponentPublicInstance {
  loadData: () => void;
}

const chartRightTop = useTemplateRef<SubChart>("chartRightTop");
const chartRightMid = useTemplateRef<SubChart>("chartRightMid");
const chartRightBottom = useTemplateRef<SubChart>("chartRightBottom");
const { isFullscreen, toggle } = useFullscreen(useTemplateRef<HTMLDivElement>("fullscreenDiv"));

let beginTime = dayjs().format("YYYY-MM-01 00:00:00");
let endTime = dayjs().format("YYYY-MM-DD 23:59:59");

const changeTime = ({ BeginTimeDt, EndTimeDt }: { BeginTimeDt: string; EndTimeDt: string }) => {
  beginTime = BeginTimeDt;
  endTime = EndTimeDt;
  loadData();
};

const orgList = shallowRef<OrgEntity[]>([]);

async function loadData() {
  const res = await Report_Api.getRedashList<OrgEntity>({
    queryName: "Report_HospitalFinanceRanking",
    parameters: {
      BeginTimeDt: beginTime,
      EndTimeDt: endTime,
      IsTest: "0",
    },
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 99999,
  });

  if (res.Type === 200) {
    const maxValue = Number(res.Data.Data[0].Amount);
    res.Data.Data.forEach((v) => {
      v.Amount = Number(v.Amount);
      v.Percentage = (v.Amount / maxValue) * 100;
    });
    orgList.value = res.Data.Data;
  }
}

// 每分钟刷新一次数据
const { resume } = useIntervalFn(
  () => {
    loadData();
    chartRightTop.value?.loadData();
    chartRightMid.value?.loadData();
    chartRightBottom.value?.loadData();
  },
  60000,
  { immediate: false, immediateCallback: true }
);

onMounted(() => {
  resume();
});
</script>
<style scoped lang="scss">
.page-root {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.screen-root {
  width: 1920px;
  height: 1080px;
  background-image: url("./assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: stretch;

  &-top {
    height: 80px;
    background-image: url("./assets/topLogo.png");
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;

    &-date-time {
      position: absolute;
      top: 30px;
      right: 10px;
      display: flex;
      align-items: center;
      .time {
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
      }
      .date {
        font-weight: 400;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
        margin-left: 12px;
      }
    }
  }
  &-mid {
    flex: 1;
    display: flex;
    align-items: stretch;
    &::before {
      content: "";
      width: 50px;
      background-image: url("./assets/left.png");
      background-size: 100% 100%;
    }
    &::after {
      content: "";
      width: 50px;
      background-image: url("./assets/right.png");
      background-size: 100% 100%;
    }
  }
  &-footer {
    height: 50px;
    background-image: url("./assets/footer.png");
    background-size: 100% 100%;
  }
}
</style>
