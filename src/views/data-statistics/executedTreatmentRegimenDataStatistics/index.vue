<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="是否展示无数据医院">
                <el-select
                  v-model="queryParams.DisplayEmptyData"
                  placeholder="请选择"
                  clearable
                  style="width: 100px"
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              :disabled="pageData.length <= 0"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column
            prop="Name"
            label="医院"
            show-overflow-tooltip
            fixed
            :filters="filterName"
            :filter-method="filterMethod"
            width="100"
            align="center"
          />
          <el-table-column
            prop="CityName"
            label="地区"
            fixed
            :filters="filterCityName"
            :filter-method="filterMethod"
            show-overflow-tooltip
            width="60"
            align="center"
          />
          <el-table-column
            prop="NewDoctorConsultCount"
            label="问诊数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewTherapistConsultCount"
            label="康复咨询数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewNurseConsultCount"
            label="护理咨询数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewDoctorDirectConsultCount"
            label="医生主动下达方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewTherapistDirectConsultCount"
            label="治疗师主动下达方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewNurseDirectConsultCount"
            label="护士主动下达方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewDoctorQuickConsult"
            label="医生快速下方数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewTherapistQuickConsult"
            label="治疗师快速下方数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewNurseQuickConsult"
            label="护士快速下方数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewPaitentConsultCount"
            label="患者问诊下达方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewPaitentAdvisoryCount"
            label="康复咨询下达方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewPaitentNurseCount"
            label="护理咨询下达方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreeGeWuCount"
            label="含免费隔物灸法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewGeWuCount"
            label="含付费隔物灸法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewGeWuAmount"
            label="隔物灸法费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreeMaiZhenCount"
            label="含免费埋针治疗方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewMaiZhenCount"
            label="含付费埋针治疗方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewMaiZhenAmount"
            label="埋针治疗费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreeCiReCount"
            label="含免费磁热疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewCiReCount"
            label="含付费磁热疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewCiReAmount"
            label="磁热疗法费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreeHuXiCount"
            label="含免费呼吸训练方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewHuXiCount"
            label="含付费呼吸训练方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewHuXiAmount"
            label="含付费呼吸训练费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewChaoShenCount"
            label="含超声治疗方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewChaoShenAmount"
            label="含超声治疗费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreeYunDongCount"
            label="含免费运动疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewYunDongCount"
            label="含付费运动疗法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewYunDongAmount"
            label="运动疗法费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreeKangFuZiXunCount"
            label="含免费康复咨询方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewKangFuZiXunCount"
            label="含付费康复咨询方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewKangFuZiXunAmount"
            label="康复咨询费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreeKangFuPingDingCount"
            label="含免费康复评定方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewKangFuPingDingCount"
            label="含付费康复评定方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewKangFuPingDingAmount"
            label="康复评定费用"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewFreePrescriptionCount"
            label="免费方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewPrescriptionCount"
            label="付费方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewContinuePrescriptionCount"
            label="续方数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="NewPrescriptionAmount"
            label="治疗费"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="SendPrescriptionCount"
            label="下达方案数"
            show-overflow-tooltip
            align="center"
          />
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import Report_Api from "@/api/report";
import {
  ExportTaskRedashDTO,
  NewPrescriptionSummaryInputDTO,
  PrescriptionSummaryInputDTO,
  NewPrescriptionSummaryItem,
} from "@/api/report/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
const userStore = useUserStore();
import dayjs from "dayjs";
import { Filters } from "element-plus/es/components/table/src/table-column/defaults";
import { ExportEnum } from "@/enums/Other";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "ExecutedTreatmentRegimenDataStatistics",
  inheritAttrs: false,
});

const queryParams = ref<NewPrescriptionSummaryInputDTO>({
  DisplayEmptyData: "0",
  EndTimeDt: dayjs().format("YYYY-MM-DD 23:59:59"),
  BeginTimeDt: dayjs().format("YYYY-MM-DD 00:00:00"),
  LoginUserId: "",
  PageIndex: 1,
  PageSize: 9999,
  OrgIds: null,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD"),
]);
const filterName = ref<Filters>([]);
const filterCityName = ref<Filters>([]);
const exportLoading = ref<boolean>(false);
const queryResultId = ref<number>(0);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<NewPrescriptionSummaryItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  queryParams.value.LoginUserId = userStore.userInfo.Id;
  tableLoading.value = true;
  const redashParams = convertToRedashParams(queryParams.value, "Report_NewPrescriptionSummary");
  const res = await Report_Api.getRedashList<NewPrescriptionSummaryItem>(redashParams);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
    queryResultId.value = res.Data.QueryResultId;
    handleGetFilterData();
  }
  tableLoading.value = false;
};

const handleGetFilterData = () => {
  // 对筛选出来的数据进行去重
  const newDataName = pageData.value.map((v) => ({
    text: v.Name,
    value: v.Name,
  }));
  filterName.value = [...new Map(newDataName.map((item) => [item.text, item])).values()];
  const newDataCityName = pageData.value.map((v) => ({
    text: v.CityName,
    value: v.CityName,
  }));
  filterCityName.value = [...new Map(newDataCityName.map((item) => [item.text, item])).values()];
};

const filterMethod = (value: string, row: NewPrescriptionSummaryItem, column: any) => {
  const property = column["property"];
  return row[property as keyof NewPrescriptionSummaryItem] === value;
};

const handleExportExcel = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  const exportParams = convertToRedashParams<PrescriptionSummaryInputDTO>(
    copyData,
    "Report_NewPrescriptionSummary"
  );
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `已执行方案数据统计-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId.value,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.value.PageIndex,
    PageSize: queryParams.value.PageSize,
    QueryName: "Report_NewPrescriptionSummary",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.BeginTimeDt = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTimeDt = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});

const getSummaries = (param: { columns: any[]; data: any[] }) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }
    if (index === 1) {
      sums[index] = "0";
      return;
    }

    const values = data.map((item) => Number(item[column.property]) || 0);
    const sum = values.reduce((prev, curr) => {
      return prev + curr;
    }, 0);

    sums[index] = `${sum}`;
  });

  return sums;
};
</script>

<style lang="scss"></style>
