<template>
  <div ref="fullscreenDiv" class="root" style="overflow: auto">
    <VScaleScreen>
      <div class="screen-root w-1920px">
        <Top :show-index="showContentIndex" @changeData="handleContentChange">
          <div class="flex items-center">
            <DateTime />
            <div
              :class="`i-svg:${isFullscreen ? 'fullscreen1-exit' : 'fullscreen1'} color-white ml-10px`"
              @click="toggle"
            />
          </div>
        </Top>

        <div class="mid">
          <div class="mid-left">
            <!-- 是否自动播放 -->
            <div
              style="width: 166px"
              class="mid-left-top flex-center-center mid-left-style"
              @click="autoPlay = !autoPlay"
            >
              <div class="flex-center-center">
                <span>{{ autoPlay ? "停止播放" : "自动播放" }}</span>
                <i v-if="autoPlay" class="el-icon-video-pause" style="margin-left: 10px" />
                <i v-else class="el-icon-video-play" style="margin-left: 10px" />
              </div>
            </div>
            <!-- 选项 -->
            <div style="margin-top: 55px; display: flex; align-items: center; gap: 12px">
              <!-- 机构 -->
              <el-select
                v-if="baseOrganizationList.length"
                v-model="showSelect.orgSelectId"
                filterable
                clearable
                placeholder="请选择"
                :teleported="false"
                style="width: 240px"
                @change="handleOrgChange"
              >
                <el-option
                  v-for="item in baseOrganizationList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
              <!-- 科室 -->
              <el-select
                v-model="showSelect.deptSelectIds"
                filterable
                multiple
                collapse-tags
                clearable
                :teleported="false"
                placeholder="请选择"
                style="width: 240px"
                @change="handleDeptChange"
              >
                <el-option
                  v-for="item in baseDepartmentList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id!"
                />
              </el-select>
              <!-- 角色 -->
              <div
                v-if="showContentIndex === 1"
                class="mid-left-top flex-center-center mid-left-style"
                @click="showSelect.role = !showSelect.role"
              >
                <div class="flex-center-center">
                  <span v-if="showSelect.roleSelectIndex.length === 1">
                    {{ baseRoleList[showSelect.roleSelectIndex[0]]?.Name }}
                  </span>
                  <span v-if="showSelect.roleSelectIndex.length > 1">
                    {{ baseRoleList[showSelect.roleSelectIndex[0]]?.Name }} +
                    {{ showSelect.roleSelectIndex.length - 1 }}
                  </span>
                  <i class="el-icon-caret-bottom" style="margin-left: 10px; color: white" />
                </div>
                <div v-if="baseRoleList.length && showSelect.role" class="mid-left-top-list">
                  <div
                    v-for="(item, index) in baseRoleList"
                    :key="index"
                    class="mid-left-top-list-item"
                    :class="{
                      currentStyle: showSelect.roleSelectIndex.some((s) => s === index),
                    }"
                    @click.stop="handleRoleItemClick(index)"
                  >
                    {{ item.Name }}
                  </div>
                </div>
              </div>
              <!-- 时间 -->
              <div
                v-if="showContentIndex === 2"
                class="mid-left-top flex-center-center mid-left-style"
              >
                <div class="flex gap-10px ml-10px mr-10px">
                  <span>视图模式</span>
                  <el-radio-group v-model="viewMode">
                    <el-radio value="day">日</el-radio>
                    <el-radio value="month">月</el-radio>
                  </el-radio-group>
                </div>
              </div>
              <!-- 日期 -->
              <div
                v-if="showContentIndex === 2 || showContentIndex === 3"
                class="mid-left-top flex-center-center mid-left-style"
              >
                <el-date-picker
                  v-model="selectDayRange"
                  type="daterange"
                  :teleported="false"
                  :editable="false"
                  :shortcuts="datePickerShortcuts"
                  start-placeholder="开始日期"
                  range-separator="至"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="background-color: transparent; box-shadow: none"
                />
              </div>
              <!-- 查询 -->
              <div class="search-btn" @click="handleSearchBtnClick">查询</div>
            </div>
          </div>
          <!-- 内容 -->
          <div class="mid-content flex-center-center">
            <template v-if="!showContentIndex">
              <span>累计服务患者：</span>
              <span class="mid-content-text">
                {{ showContentList[showContentIndex].data }}
              </span>
              <span>人</span>
            </template>
            <template v-if="showContentIndex === 1">
              <span>医务人员管理患者Top10</span>
            </template>
            <template v-if="showContentIndex === 2">
              <span>累计下达方案：</span>
              <span class="mid-content-text">
                {{ showContentList[showContentIndex].data }}
              </span>
              <span>人次 执行率：</span>
              <span class="mid-content-text">
                {{ showContentList[showContentIndex].percentage }}%
              </span>
            </template>
            <template v-if="showContentIndex === 3">
              <div style="position: relative">
                <div>
                  <span>累计患者随访：</span>
                  <span class="mid-content-text">
                    {{ showContentList[showContentIndex].data }}
                  </span>
                  <span>人次，</span>
                  <span>回院复诊：</span>
                  <span class="mid-content-text">
                    {{ showContentList[showContentIndex].data1 }}
                  </span>
                  <span>人</span>
                </div>
                <div style="position: absolute; font-size: 17px; right: 0; bottom: -23px">
                  回院复诊人数从25年3月开始统计
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 图表相关 -->
        <div
          v-loading="pageLoading"
          class="charts"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <el-carousel
            ref="carousel"
            :autoplay="autoPlay"
            :initial-index="carouselIndex"
            trigger="click"
            height="800px"
            :interval="5000"
            @change="handlerCarouselChange"
          >
            <el-carousel-item>
              <P1 ref="p1" :org-id="orgId" :dept-id="deptIds" @getTitleContent="setTitleContent" />
            </el-carousel-item>
            <el-carousel-item>
              <P2 ref="p2" :org-id="orgId" :dept-id="deptIds" :role-type="roleIds" />
            </el-carousel-item>
            <el-carousel-item>
              <P3
                ref="p3"
                :org-id="orgId"
                :dept-id="deptIds"
                :time-begin-dt="selectDayRange[0] + ' 00:00:00'"
                :time-end-dt="selectDayRange[1] + ' 23:59:59'"
                :query-type="viewMode"
                @getTitleContent="setTitleContent"
              />
            </el-carousel-item>
            <el-carousel-item>
              <P4
                ref="p4"
                :org-id="orgId"
                :org-name="orgName"
                :dept-id="deptIds"
                :time-begin-dt="selectDayRange[0] + ' 00:00:00'"
                :time-end-dt="selectDayRange[1] + ' 23:59:59'"
                @getTitleContent="setTitleContent"
              />
            </el-carousel-item>
          </el-carousel>
        </div>
        <!-- 底部 -->
        <div class="footer" />
      </div>
    </VScaleScreen>
  </div>
</template>
<script lang="ts">
import useOrgList from "@/hooks/useOrgList";

import dayjs from "dayjs";
import useDepartmentList from "@/hooks/useDepartmentList";
import type { ComponentPublicInstance } from "vue";
import largeScreenContext from "./large-screen-context";
import Top from "./views/Top.vue";
import P1 from "./views/P1.vue";
import P2 from "./views/P2.vue";
import P3 from "./views/P3.vue";
import P4 from "./views/P4.vue";
import DateTime from "./views/DateTime.vue";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

interface ChartComponent extends ComponentPublicInstance {
  loadChartsData: () => void;
}

interface CarouselComponent extends ComponentPublicInstance {
  setActiveItem: (index: number) => void;
}

export default defineComponent({
  components: {
    Top,
    P1,
    P2,
    P3,
    P4,
    DateTime,
  },
  provide: { largeScreenContext },
  setup() {
    const fullscreenDiv = useTemplateRef<HTMLDivElement>("fullscreenDiv");
    const p1 = useTemplateRef<ChartComponent>("p1");
    const p2 = useTemplateRef<ChartComponent>("p2");
    const p3 = useTemplateRef<ChartComponent>("p3");
    const p4 = useTemplateRef<ChartComponent>("p4");
    const carousel = useTemplateRef<CarouselComponent>("carousel");
    const { baseOrganizationList, loadOrgList, resetOrgList } = useOrgList({ hasAll: true });
    const { baseDepartmentList, loadDepartmentList, resetDepartmentList } = useDepartmentList({
      hasAll: true,
    });

    const { isFullscreen, toggle } = useFullscreen(fullscreenDiv);

    const { datePickerShortcuts } = useDateRangePicker();

    return {
      p1,
      p2,
      p3,
      p4,
      carousel,
      baseOrganizationList,
      loadOrgList,
      resetOrgList,
      baseDepartmentList,
      loadDepartmentList,
      resetDepartmentList,
      isFullscreen,
      toggle,
      datePickerShortcuts,
    };
  },
  data() {
    return {
      pageLoading: false,
      autoPlay: false,
      showContentList: [
        {
          data: "",
          percentage: "",
          data1: "",
        },
        {
          data: "",
          percentage: "",
          data1: "",
        },
        {
          data: "",
          percentage: "",
          data1: "",
        },
        {
          data: "",
          percentage: "",
          data1: "",
        },
      ],
      showContentIndex: 0,
      timer: null as null | NodeJS.Timeout,
      carouselIndex: 0,
      baseRoleList: [
        { Name: "全部角色", Id: "*" },
        { Name: "医生", Id: "doctor" },
        { Name: "护士", Id: "nurse" },
        { Name: "治疗师", Id: "therapist" },
      ],
      showSelect: {
        org: false,
        dept: false,
        role: false,
        orgSelectId: "*",
        deptSelectIds: ["*"],
        roleSelectIndex: [0],
      },
      selectOrgId: null,

      viewMode: "month",
      selectDayRange: [
        dayjs(new Date()).subtract(6, "month").format("YYYY-MM-DD"),
        dayjs(new Date()).format("YYYY-MM-DD"),
      ],
    };
  },
  computed: {
    roleIds() {
      let arr: string[] = [];
      this.showSelect.roleSelectIndex.forEach((element) => {
        arr.push(this.baseRoleList[element].Id);
      });
      if (arr.length === 1 && arr[0] === "*") {
        return "*";
      }
      return arr.join(",");
    },
    deptIds() {
      if (this.baseDepartmentList.length === 1 && this.baseDepartmentList[0].Id === "*") return "*";
      return this.showSelect.deptSelectIds.join(",");
    },
    orgId() {
      return this.showSelect.orgSelectId;
    },
    orgName() {
      const orgList = this.baseOrganizationList.filter((s) => s.Id === this.showSelect.orgSelectId);
      if (orgList.length) {
        return orgList[0].Name;
      }
      return "全部机构";
    },
  },

  mounted() {
    this.loadOrgList({ PageSize: 9999, DtoTypeName: "QueryOrgListOutputDto3" });
    // 60分钟调用一次handleSearch刷新数据
    this.timer = setInterval(() => {
      this.handleSearch(false);
    }, 600000 * 6);
  },
  beforeUnmount() {
    this.timer && clearInterval(this.timer);
  },
  methods: {
    handleOrgChange(orgId: string) {
      console.log("org", orgId);
      if (!orgId) {
        // 如果点击清楚按钮
        this.showSelect.orgSelectId = "*";
        this.resetSelectedDepartment();
        largeScreenContext.org = null;
        return;
      }
      if (orgId === "*") {
        this.resetSelectedDepartment();
        largeScreenContext.org = null;
      } else {
        largeScreenContext.org = this.baseOrganizationList.find((s) => s.Id === orgId) ?? null;
        this.resetDepartmentList();
        this.loadDepartmentList(orgId);
        this.showSelect.deptSelectIds = ["*"];
      }
    },
    handleDeptChange(ids: string[]) {
      if (!ids.length) this.showSelect.deptSelectIds = ["*"];
      if (ids[ids.length - 1] === "*") {
        this.showSelect.deptSelectIds = ["*"];
      } else {
        const allIndex = ids.indexOf("*");
        // 删除 *
        if (allIndex > -1) {
          this.showSelect.deptSelectIds.splice(allIndex, 1);
        }
      }
    },
    resetSelectedDepartment() {
      this.showSelect.deptSelectIds = ["*"];
      this.resetDepartmentList();
    },
    handleRoleItemClick(index: number) {
      this.showSelect.role = false;
      this.showSelect.roleSelectIndex = [index];
    },
    handleSearchBtnClick() {
      this.handleSearch(true);
    },
    handleSearch(showLoading = true) {
      if (showLoading) {
        this.pageLoading = true;
      }
      this.p1?.loadChartsData();
      this.p2?.loadChartsData();
      this.p3?.loadChartsData();
      this.p4?.loadChartsData();
      this.showSelect.org = false;
      this.showSelect.dept = false;
      this.showSelect.role = false;
      setTimeout(() => {
        this.pageLoading = false;
      }, 1000);
    },
    setTitleContent({
      data,
      index,
      percentage,
      data1,
    }: {
      data: string;
      index: number;
      percentage: string;
      data1: string;
    }) {
      this.showContentList[index].data = data;
      this.showContentList[index].percentage = percentage;
      this.showContentList[index].data1 = data1;
    },

    handleContentChange(index: number) {
      this.showContentIndex = index;
      this.carouselIndex = index;
      this.carousel?.setActiveItem(index);
    },
    handlerCarouselChange(index: number) {
      this.showContentIndex = index;
    },
  },
});
</script>
<style scoped lang="scss">
.root {
  overflow: hidden;
  height: 100%;
}
.screen-root {
  position: relative;
  background-image: url("./images/bg.png");
  background-size: 100% 100%;
  .fullscreen {
    margin-left: 20px;
  }
  .mid {
    position: relative;
    padding: 0 79px;
    transform: translateY(-20px);
    z-index: 3;
    &-left {
      &-top {
        min-width: 166px;
        height: 45px;
        background: linear-gradient(
          90deg,
          rgba(51, 204, 204, 0.2) 0%,
          rgba(51, 204, 204, 0) 53%,
          rgba(51, 204, 204, 0.2) 100%
        );
        border-radius: 3px;
        border: 1px solid rgba(51, 204, 204, 0.4);
        cursor: pointer;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
        line-height: 25px;
        position: relative;
        z-index: 3;
        &-list {
          position: absolute;
          top: 54.67px;
          left: 0;
          width: 352px;
          height: 196px;
          overflow-y: auto;
          background: #000000;
          box-shadow:
            inset -4px -4px 10px 0px rgba(51, 204, 204, 0.5),
            inset 4px 4px 10px 0px rgba(51, 204, 204, 0.5);
          border-radius: 1px;
          border: 1px solid #33cccc;
          &-item {
            cursor: pointer;
            height: 49px;
            line-height: 49px;
            box-sizing: border-box;
            font-family: YouSheBiaoTiHei;
            font-weight: normal;
            font-size: 18px;
            color: #ffffff;
            text-align: center;
            background: linear-gradient(
              90deg,
              rgba(51, 204, 204, 0.1) 0%,
              rgba(51, 204, 204, 0.1) 100%
            );
          }
        }
      }
      &-style {
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
        line-height: 25px;
      }
    }
    &-content {
      position: absolute;
      left: 50%;
      top: 36%;
      transform: translate(-50%, -50%);
      background-image: url("./images/content-icon.png");
      background-size: 100% 100%;
      padding: 0 90px;
      min-width: 612px;
      height: 74px;
      font-family: YouSheBiaoTiHei;
      font-weight: normal;
      font-size: 30px;
      color: #ecf6ff;
      line-height: 34px;
      text-align: center;
      &-text {
        text-shadow: 0px 0px 13px rgba(0, 44, 78, 1);
        background-image: linear-gradient(
          180deg,
          rgba(255, 255, 255, 1) 0,
          rgba(255, 167, 45, 1) 100%
        );
        height: 36px;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 44px;
        font-family: DINAlternate-Bold;
        font-weight: 700;
        text-align: right;
        white-space: nowrap;
        line-height: 36px;
        margin-left: 9px;
        -webkit-background-clip: text;
        background-clip: text; /* 添加标准属性 */
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .charts {
    margin-top: -20px;
    position: relative;
    padding: 27px 79px 0 79px;
  }
}
.search-btn {
  width: 96px;
  height: 43px;
  background: linear-gradient(180deg, rgba(51, 204, 204, 0.4) 0%, rgba(51, 204, 204, 0) 100%);
  box-shadow: inset 0px 0px 7px 4px rgba(51, 204, 204, 0.6);
  border-radius: 3px;
  border: 1px solid;
  border-image: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 0)
    )
    1 1;
  text-align: center;
  font-weight: 600;
  font-size: 19px;
  color: #7aa3cc;
  line-height: 43px;
  cursor: pointer;
}
.flex-center-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-start-center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.footer {
  margin-top: -5px;
  width: 100%;
  height: 52px;
  background-image: url("./images/footer.png");
  background-size: 100% 100%;
}
.currentStyle {
  background: linear-gradient(
    90deg,
    rgba(51, 204, 204, 0.4) 0%,
    rgba(51, 204, 204, 0.4) 100%
  ) !important;
}
.el-radio {
  margin-right: 10px !important;
}
.el-range-editor--mini.el-input__inner {
  height: 45px !important;
  background: none !important;
  border: none !important;
  font-size: 19px !important;
}
:deep(.el-range-separator) {
  color: #ffffff !important;
  line-height: 40px;
}
:deep(.el-range-input) {
  color: #ffffff;
  background: none !important;
}
// .el-select,
:deep(.el-select__placeholder) {
  color: #ffffff !important;
}
:deep(.el-select__wrapper) {
  box-shadow: none !important;
  color: #ffffff !important;
  background: linear-gradient(
    90deg,
    rgba(51, 204, 204, 0.2) 0%,
    rgba(51, 204, 204, 0) 53%,
    rgba(51, 204, 204, 0.2) 100%
  ) !important;
  border-radius: 3px !important;
  border: 1px solid rgba(51, 204, 204, 0.4) !important;
  height: 43px !important;
  font-size: 18px;
}
:deep(.el-tag) {
  background: none !important;
  border: none !important;
  color: white !important;
}
:deep(.el-select__tags) {
  max-width: 90% !important;
}
:deep(.el-select__tags-text) {
  font-size: 16px !important;
}
</style>
