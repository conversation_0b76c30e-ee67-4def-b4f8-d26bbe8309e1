<template>
  <div class="box">
    <div class="box-left">
      <div class="box-left-top">
        <Box width="563px" height="318px" label="满意度" style="margin-right: 31px">
          <PieCharts v-if="satisfaction.length" :data="satisfaction" width="563px" height="276px" />
        </Box>
        <Box width="563px" height="318px" label="康复效果">
          <PieCharts
            v-if="therapeuticEffect.length"
            :data="therapeuticEffect"
            width="563px"
            height="276px"
          />
        </Box>
      </div>
      <div class="box-left-bom">
        <Box
          width="1155px"
          height="365.33px"
          :label="orgId === '*' ? '医院分布' : '科室分布'"
          style="margin-top: 27px"
        >
          <LineCharts v-if="deptData.length" width="1155px" height="365.33px" :data="deptData" />
        </Box>
      </div>
    </div>
    <div class="box-right">
      <template v-if="evaluation.length">
        <Box
          v-for="(item, index) in evaluation"
          :key="index"
          width="576px"
          style="margin-bottom: 20px"
        >
          <div class="box-right-item">
            <div class="box-right-item-top">
              <span class="box-right-item-top-name">{{ item.PatName }}</span>
              <div style="display: flex">
                <div style="margin-right: 21px" class="flex-start-center">
                  <span class="box-right-item-top-label">满意度</span>
                  <el-rate v-model="item.Myd" disabled text-color="#ff9900" />
                </div>
                <div class="flex-start-center">
                  <span class="box-right-item-top-label">康复效果</span>
                  <el-rate v-model="item.Zlxg" disabled text-color="#ff9900" />
                </div>
              </div>
            </div>
            <div class="box-right-item-bom">{{ item.ProEndObj.Content }}</div>
          </div>
        </Box>
        <Box width="576px" height="50px" class="box-right-bom">
          <div class="box-right-bom-text" @click="handleSeeMoreClick">查看更多</div>
        </Box>
      </template>
      <el-empty v-else description="暂无数据" />
    </div>
  </div>
</template>
<script lang="ts">
import dayjs from "dayjs";
import Report_Api from "@/api/report";
import Consult_Api from "@/api/consult";
import Box from "./Box.vue";
import PieCharts, { PieChartData } from "./PieChart.vue";
import LineCharts from "./LineChart.vue";

/**
 * 评价数据
 */
interface EvaluationItem {
  PatName: string;
  Myd: number;
  Zlxg: number;
  ProEndObj: any;
  ProEnd: string;
}

/**
 * 评分数据
 */
interface RateItem {
  Name: string;
  Point: number;
  Count: number;
}

export default defineComponent({
  components: {
    Box,
    PieCharts,
    LineCharts,
  },
  props: {
    orgId: {
      type: String,
      default: "*",
    },
    orgName: {
      type: String,
      default: "",
    },
    deptId: {
      type: String,
      default: "*",
    },
    timeBeginDt: {
      type: String,
      default: () => dayjs(new Date()).subtract(6, "month").format("YYYY-MM-01") + " 00:00:00",
    },
    timeEndDt: {
      type: String,
      default: () => dayjs(new Date()).format("YYYY-MM-DD") + " 23:59:59",
    },
  },
  setup() {
    const deptData = ref<{ name: string; value: number }[]>([]);
    const evaluation = ref<EvaluationItem[]>([]);
    const query = {
      queryName: "Report_HospitalDataAnalysisReturn",
    };
    return {
      deptData,
      evaluation,
      query,
    };
  },
  data() {
    return {
      therapeuticEffect: [] as PieChartData[],
      satisfaction: [] as PieChartData[],
    };
  },
  mounted() {
    this.loadChartsData();
  },
  methods: {
    loadChartsData() {
      this.loadRedashData();
      this.loadEvaluationData();
    },
    async loadEvaluationData() {
      const res = await Consult_Api.getVisitRecordStatistics<{
        Record: EvaluationItem[];
        Count: RateItem[];
      }>({
        OrgIds: this.orgId === "*" ? null : [this.orgId],
        DeptIds: this.deptId === "*" ? null : [this.deptId],
        OperaTimeStart: this.timeBeginDt,
        OperaTimeEnd: this.timeEndDt,
        PageIndex: 1,
        PageSize: 6,
      });
      if (res.Type === 200) {
        // 处理评价数据
        this.processEvaluationData(res.Data.Record);
        // 处理评分数据
        this.processRateData(res.Data.Count);
      }
    },
    /**
     * 处理评分数据
     * @param list - 评分数据列表
     */
    processRateData(list: RateItem[]) {
      // 按照Name分组数据
      const groupedData = this.groupedByKey(list);
      const groupNames = Object.keys(groupedData);

      // 如果没有数据则清空状态
      if (!groupNames.length) {
        this.therapeuticEffect = [];
        this.satisfaction = [];
        return;
      }

      // 处理每个分组的评分数据
      const processGroupData = (groupName: string) => {
        const currentGroup = groupedData[groupName];
        const existingPoints = new Set(currentGroup.map((item) => item.Point));

        // 补充缺失的评分点(1-5星)
        const missingPoints = Array.from({ length: 5 }, (_, i) => ({
          Name: groupName,
          Point: i + 1,
          Count: 0,
        })).filter((item) => !existingPoints.has(item.Point));

        // 合并现有数据和缺失数据
        return [...currentGroup, ...missingPoints]
          .map((item) => ({
            name: item.Point ? `${item.Point}星` : "未采集",
            value: item.Count,
          }))
          .sort((a, b) => Number(a.name[0]) - Number(b.name[0]));
      };

      // 更新状态
      const stateMap = {
        治疗效果: "therapeuticEffect",
        满意度: "satisfaction",
      } as const;

      // 遍历分组并更新对应状态
      Object.entries(stateMap).forEach(([groupName, stateProp]) => {
        if (groupedData[groupName]) {
          this[stateProp] = processGroupData(groupName);
        }
      });
    },
    processEvaluationData(data: EvaluationItem[]) {
      data.forEach((s) => {
        if (s.ProEnd) {
          s.ProEndObj = JSON.parse(s.ProEnd);
        }
      });

      this.evaluation = data.sort((a, b) => b.Myd - a.Myd);
    },
    async loadRedashData() {
      const res = await Report_Api.getRedashList<{
        GroupName: string;
        ReturnCount: number;
        ReturnVisitCount: number;
      }>({
        queryName: "Report_HospitalDataAnalysisReturn",
        parameters: {
          OrgIds: this.orgId,
          DeptIds: this.deptId,
          TimeBeginDt: this.timeBeginDt,
          TimeEndDt: this.timeEndDt,
        },
        maxAge: 0,
        JobWaitingMs: 30000,
        pageIndex: 1,
        pageSize: 99999,
      });
      if (res.Type === 200) {
        const result = res.Data.Data.map((element) => {
          return {
            name: element.GroupName,
            value: element.ReturnCount * 1, // 累计服务患者
            value2: element.ReturnVisitCount * 1, // 回院复诊
          };
        }).slice(0, 10);
        this.deptData = result;
        this.$emit("getTitleContent", {
          index: 3,
          data: result.reduce((a, b) => a + b.value * 1, 0),
          data1: result.reduce((a, b) => a + b.value2 * 1, 0),
        });
      }
    },
    handleSeeMoreClick() {
      if (document.fullscreenElement) {
        // 如果处于全屏状态，先退出全屏
        document
          .exitFullscreen()
          .then(() => {
            // 退出全屏后调用openUrl
            this.$router.push(
              `/medical-procedure/homeRehabilitationVisitNew?DeptIds=${this.deptId}&OrgIds=${this.orgId}&QueryStartDate=${this.timeBeginDt}&QueryEndDate=${this.timeEndDt}&OrgName=${this.orgName}`
            );
          })
          .catch((err) => {
            console.error("退出全屏失败:", err);
            // 即使退出全屏失败，也尝试打开URL
            this.$router.push(
              `/medical-procedure/homeRehabilitationVisitNew?DeptIds=${this.deptId}&OrgIds=${this.orgId}&QueryStartDate=${this.timeBeginDt}&QueryEndDate=${this.timeEndDt}&OrgName=${this.orgName}`
            );
          });
      } else {
        // 不是全屏状态，直接调用openUrl
        this.$router.push(
          `/medical-procedure/homeRehabilitationVisitNew?DeptIds=${this.deptId}&OrgIds=${this.orgId}&QueryStartDate=${this.timeBeginDt}&QueryEndDate=${this.timeEndDt}&OrgName=${this.orgName}`
        );
      }
    },
    groupedByKey<T extends { Name: string; Point: number }>(list: T[]): Record<string, T[]> {
      const groupedByName = list.reduce(
        (result, item) => {
          const key = item.Name;
          if (!result[key]) {
            result[key] = [] as T[];
          }
          // 过滤未采集数据
          if (item.Point !== 0) {
            result[key].push(item);
          }
          return result;
        },
        {} as Record<string, T[]>
      );
      return groupedByName;
    },
  },
});
</script>
<style scoped lang="scss">
.box {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  &-left {
    &-top {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
  &-right {
    margin-left: 29px;
    height: 755px;
    width: 576px;
    position: relative;
    overflow: auto;
    &-item {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      flex: 1;
      padding: 16px;
      &-top {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        &-name {
          font-weight: 600;
          font-size: 18px;
          color: #33cccc;
          line-height: 25px;
          margin-right: 30px;
          display: -webkit-box;
          -webkit-line-clamp: 1; /* 显示的最大行数 */
          line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden; /* 隐藏超出部分 */
          text-overflow: ellipsis; /* 显示省略号 */
          flex: 1;
        }
        &-label {
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 22px;
          margin-right: 17px;
        }
      }
      &-bom {
        margin-top: 12px;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        line-height: 22px;
      }
    }
    &-bom {
      &-text {
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        line-height: 50px;
        text-align: center;
        flex: 1;
        cursor: pointer;
      }
    }
  }
}
.flex-start-center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>
