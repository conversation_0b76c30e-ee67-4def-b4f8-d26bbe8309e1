export const geoConfig = {
  // 使用 registerMap 注册的地图名称。
  // map: name,
  // 是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
  roam: false,
  zoom: 1.4,
  // 图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。
  itemStyle: {
    // areaColor: 'transparent',
    borderWidth: 1.5,
    // borderColor: '#33cccc',
    borderColor: "#8CF5F4",
    // shadowColor: '#33cccc',
    shadowColor: "rgba(50, 128, 138, 0.23)",
    shadowBlur: 1,
    shadowOffsetY: 10,
    // shadowOffsetX: 3,
  },
  label: {
    show: false,
  },
  // 设置高亮状态下的多边形和标签样式
  emphasis: {
    disable: true,
  },
};

const cache: Record<string, any> = {};
/** 获取原始地图json数据 */
export async function getMapJSON(adCode: string) {
  if (cache[adCode]) return JSON.parse(JSON.stringify(cache[adCode]));
  // const url = `https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${adcode}_full`;

  const url = `/geoJson/${adCode}_full.json`;
  // https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=469022
  // if (adCode != '100000') {
  //   url = `https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${adCode}`;
  // }
  try {
    const r = await fetch(url);
    const data = await r.json();
    console.log(adCode, data);
    cache[adCode] = data;
    return data;
  } catch (error: any) {
    console.error(error);
    return null;
  }
}

export type AdcodeType = 1 | 2 | 3 | 4;

/**
 * 获取行政区划代码类型
 * @param {string|number} adcode 行政区划代码
 * @returns {number} 返回行政区划类型：1 省级, 2 市级, 3 区级， 4 直辖市
 * @throws {Error} 如果adcode无效或长度不正确，则抛出错误
 */
export function getAdcodeType(adcode: string | number): AdcodeType {
  if (!adcode || (typeof adcode !== "string" && typeof adcode !== "number")) {
    throw new Error("adcode is not a string or number");
  }

  const code = adcode.toString();
  if (code.length !== 6) {
    throw new Error("adcode length must be 6");
  }

  const typeMap = {
    "0000": 1 as AdcodeType, // 省级
    "00": 2 as AdcodeType, // 市级
  };
  // 直辖市
  const specialCities = ["5000"];
  if (specialCities.includes(code.slice(0, 4))) {
    return 4; // 直辖市为市级
  }

  return typeMap[code.slice(-4) as "0000" | "00"] || typeMap[code.slice(-2) as "00" | "0000"] || 3; // 默认区级
}
export const geoToolTipOption = {
  backgroundColor: "transparent",
  borderWidth: 0,
  borderRadius: 0,
  borderColor: "transparent",
  fontSize: 16,
  padding: 0,
  hideDelay: 0,
  transitionDuration: 0,
  // extraCssText: 'opacity: 0.8; ',
  formatter: (params: any) => {
    // console.log('formatter', params);
    return `
      <div style="box-shadow: #33cccc 0px 0px 20px 0px;
                  background: rgba(0, 0, 0, 0.6);
                  color: white;
                  font-size: 20px;
                  padding: 10px;
                  border-radius: 10px;">
        <span style="color: white;">${params.data.name}</span>
        <br />
        <span style="color: #33CCCC;">${params.data.count}</span>
      </div>`;
  },
};
export const toolTipOption = {
  show: true,
  trigger: "item",
  position: "top",
  backgroundColor: "#093172",
  borderWidth: 0,
  padding: 12,
  hideDelay: 0,
  transitionDuration: 0,
  extraCssText: "opacity: 0.8; ",
  formatter: (params: any) => {
    // console.log('formatter', params);
    return `
      <div>
        <span style="color: #d8a04c; font-size: 20px;  font-family: YouSheBiaoTiHei;">${params.data.value[2]}</span>
      <br />
      <span style="color: #CCCCCC; font-size: 14px; line-height: 22px;">患者数量: <span style="color: #FFFFFF; font-size: 14px; line-height: 22px;">${params.data.value[3]}</span></span>
      <br />
     `;
  },
};
export const top3SeriesOption = {
  id: "first",
  name: "Top 3",
  // type: 'effectScatter',
  // rippleEffect: {
  //   period: 5,
  //   scale: 2,
  //   number: 2,
  //   brushType: 'stroke',
  // },
  type: "scatter",
  coordinateSystem: "geo",
  animationDuration: 0,
  tooltip: toolTipOption,
  label: {
    show: true,
    color: "#fff",
    position: "top",
    backgroundColor: "transparent",
    borderWidth: 1,
    borderType: "solid",
    borderColor: {
      type: "linear",
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#f8b500", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "rgba(248, 181, 0, 0.3)", // 100% 处的颜色
        },
      ],
      global: false, // 缺省为 false
    },
    padding: 3,
    overflow: "break",
    formatter: `{title|{@[2]} }`,
    rich: {
      title: {
        fontFamily: "YouSheBiaoTiHei",
        fontSize: 20,
        padding: 8,
        backgroundColor: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(0,0,0,0.6)", // 0% 处的颜色
            },
            {
              offset: 1,
              color: "rgba(0,0,0,0.4)", // 100% 处的颜色
            },
          ],
          global: false, // 缺省为 false
        },
      },
      key: {
        color: "#CCCCCC",
        fontSize: 14,
        lineHeight: 22,
      },
      value: {
        color: "#FFFFFF",
        fontSize: 14,
        lineHeight: 22,
      },
    },
  },
  itemStyle: {
    // opacity: 0.8,
  },
  symbolSize: (value: any, params: any) => (params.dataIndex < 3 ? 30 : 20),
  symbol: (value: any, params: any) => {
    return params.dataIndex < 3
      ? // 定位图标样式
        "image://data:image/png;base64,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"
      : "image://data:image/png;base64,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";
  },
};

export const otherSeriesOption = {
  id: "second",
  name: "other",
  type: "scatter",
  coordinateSystem: "geo",
  animationDuration: 0,
  tooltip: toolTipOption,
  itemStyle: {
    opacity: 0.8,
  },
  symbolSize: 20,
  symbol:
    "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB8AAAAhCAMAAAAWAIDWAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAMAUExURUdwTAAcrAAAjwAvwAAAjXh4/wAntgAJmgAAkgATof///wAAqP///wAcqwA4zf///////wAFlgAAlQBp8wAhsAJK4P///////wBW+AQAvwJE2RtL/////////////////////wAAsP///////wBn+AFA1QApuRRM/wA6ziF7/xdh/wRN5AcAsDBD/////zFE/////zA6/////wABkv///w0X1QdP6w4e6wM90gVa8P///xBj/AE+0hde9QAtvQAVogIAvP///yBP/wVW7xVe/xhV/xFf9CBW/wAAsAdZ7xBw/Akn3AAQnwMLsw1r/////yJs/wtj9yNO9hpu+A4pxf///wsdxwgFxwEAtv///wAApAEAtg5AzgAApSZL/////w4y2////yRT/wIAvP///////////wgIrhUWvf///+/w+Pb2+/39/////////wAmuwAOowAaqwI4ywAmvQAAjAE0yARS6gAPogATpAAbqwZR5wEzwwVL4QAOoQARpgAerQQ8zQIpvwIqwQEovAAzxQUOyQAUpgUx1hlS9g0AwAIrwgEsvBUA0xIAxw0O4Qcl1ws+2wwq0wkk6xQA1BEA0QIAtS2K/wxL3yhH+A4AvB9//wh3/xNg/xxO+hJz/SlO/xUA0xFE2gAzxBxT/xVg/xZZ/xl3/RBp9xtV/xxR8iRW/xdb/xx8/wU/zhdy/xtv/yR5/x5d/yB8/xt4/B96/xRp/xIAwyxO/xNY6Qdc8hhL3f///w8AvP///////////yJJ//39/xVm9gAAov///zBP+Tc9/y98+RI+3gIAvAgQwiAdxSA71SVt7vL4/2pS/1FL/ySX/1iT/zNU/ydj60tv3iBa9ltN/wAAqUtK/xYUu2Be/yBU4zIvuj9r/xRC4kdX/////////////////////////xkZnBgZnKur2xcnpbGx3cTE5VpauB4enrW134WFyoaGyhO56E1NshJa0B1y5TNG0X6z62Rx9x901oar6DR76hc4qU5Os8/P6srK6HlvEJwAAADndFJOUwDm5ubmAebm5uYQ5qjm5gK35uYC5uYbmga55uZ7CDZk4NSKWgzm5ubm5rnmursK1NvVQuTJKlUT5BnOLeZ95ubG3OPm5uZh3+Dm5g7m5hOj5ua55uakNLi+UubTxubkBOlT5tUMRlXky0v07dfaTk+24bFC5qU+qpLXS5kznsDlu1x42ua/09paq+PlO4q9zNjMtll13mXlvp4gHkKi3MMj5ubljODl5LqN0ay81sHD0Mnb3dC7UtPm5+Zmho8FjubFPObEyNCB6t/K4XTe1OHdKtvf7tsfyNTL3t7s4srg4zAisZU6gWSac7AAAAJYSURBVDjLY2BABd7CDLgBq2lFZXUNLhXCYbWCQhyChaVlEthki8uNOYSEhDiYBBmLqtBVSOQXGLMwsTAxMYFIwfoGMxUkWZXGOiEmFigoAariYBeJcfODylo2tQlyoANBH5G1trNYgdJmzT7s2ICPiNS8aUB580ldvPac6MCeV01q+ZrZIPNN+9q7RRh5eRnBgBfMEFHT8Ny8zhrigMnh/RZ2amwIoGanoR28ZasbK9TrvZKtEyxamGFgkYZo4Kb1WkvggWDeKSk5cboNHwTYiHou26CltdoSEQA9kg4evgu1pVRVVaVEnb00XZZuXDEHOfymTOXm5g7ZoS0q6hyoGaCu7upuixJNEjOjgQp8dwXu9NJ0VZdevGquCmoELNBx8DgcFRsSHCAtLe0+P8gaPf626Ugmxx7mTkg6ELddd0YHK0ba2KMTkZwOlI/T19VdiSWNeIcrKkafPHHZ0FA/yA9b+gk7pXhD8b6+4bGjh7AnsDMZt9LvPLhw8zgrplx8prhBztW7p6/cPn/pnAKPsh6KLP9uA55ILoZrMRlnr9+7yCCbKq4gI4uQThHwF2NgMOLJ/vXt94/vWQJp/AxcSlZicPlQGQaGfTxv3r578eXrzxfPP78+yMXAEGnCD5XmcgQ6SP7lcyB49ekViPqwFygsIIcp/+IPiHz+cT+yPIM/0Px4kPlgyed/Xx8Bmp8IN59BTyAU4r5/71++/g90Hyuq+xj45Q14ErkYWMXkjJ4+4mfQQ/MfSEWmeG6ugLjSM6Unj3OA4SOLJXxTHuYpyyvnyXEhiQEAyZbA+r41wqsAAAAASUVORK5CYII=",
};

// 第一版
// 0-5% 黄色 ， 5%-10% 绿色 ， 10%-20% 蓝色 ， 20%-30% 淡蓝色 ， 30%以上 紫色
// rgba(228, 248, 0, 1), rgba(0, 248, 187, 1), rgba(0, 228, 248, 1), rgba(0, 179, 248, 1), rgba(248, 181, 0, 1)

// 第二版
// 默认 rgba(15, 69, 91, 1)， rgba(20, 107, 135, 1)， rgba(15, 136, 155, 1)， rgba(53, 172, 195, 1)， rgba(53, 190, 195, 1)
// 高亮 rgba(7, 86, 117, 1)， rgba(8, 122, 161, 1)， rgba(0, 157, 181, 1)， rgba(37, 189, 219, 1)， rgba(37, 213, 219, 1)

// eslint-disable-next-line complexity
export function configItemStyle(values: any) {
  let labelColor, labelColorHover, areaColor, borderColor, areaColorHover;
  // if (values.percent > 0 && values.percent <= 0.05) {
  //   color = 'white';
  //   areaColor = 'rgba(15, 69, 91, 1)';
  //   borderColor = areaColorHover = 'rgba(7, 86, 117, 1)';
  // } else if (values.percent > 0.05 && values.percent <= 0.1) {
  //   color = 'white';
  //   areaColor = 'rgba(20, 107, 135, 1)';
  //   borderColor = areaColorHover = 'rgba(8, 122, 161, 1)';
  // } else if (values.percent > 0.1 && values.percent <= 0.2) {
  //   color = 'white';
  //   areaColor = 'rgba(15, 136, 155, 1)';
  //   borderColor = areaColorHover = 'rgba(0, 157, 181, 1)';
  // } else if (values.percent > 0.2 && values.percent <= 0.3) {
  //   color = 'white';
  //   areaColor = 'rgba(53, 172, 195, 1)';
  //   borderColor = areaColorHover = 'rgba(37, 189, 219, 1)';
  // } else if (values.percent > 0.3) {
  //   color = 'white';
  //   areaColor = 'rgba(53, 190, 195, 1)';
  //   borderColor = areaColorHover = 'rgba(37, 213, 219, 1)';
  // }
  if (values.percent > 0 && values.percent <= 0.05) {
    // // color = 'rgba(228, 248, 0, 1)';
    // // labelColor = 'white';
    // labelColorHover = 'white';
    // // areaColor = 'rgba(228, 248, 0, 0.25)';
    // borderColor = '#33cccc';
    // // areaColorHover = 'rgba(228, 248, 0, 0.75)';
    // areaColorHover = 'rgba(51, 204, 204, 0.5)';
  } else if (values.percent > 0.05 && values.percent <= 0.1) {
    labelColor = "rgba(0, 248, 187, 1)";
    labelColorHover = "white";
    areaColor = "rgba(0, 248, 187, 0.25)";
    borderColor = labelColor;
    areaColorHover = "rgba(0, 248, 187, 0.75)";
  } else if (values.percent > 0.1 && values.percent <= 0.2) {
    labelColor = "rgba(0, 228, 248, 1)";
    labelColorHover = "white";
    areaColor = "rgba(0, 228, 248, 0.25)";
    borderColor = labelColor;
    areaColorHover = "rgba(0, 228, 248, 0.75)";
  } else if (values.percent > 0.2 && values.percent <= 0.3) {
    labelColor = "rgba(0, 179, 248, 1)";
    labelColorHover = "white";
    areaColor = "rgba(0, 179, 248, 0.25)";
    borderColor = labelColor;
    areaColorHover = "rgba(0, 179, 248, 0.75)";
  } else if (values.percent > 0.3) {
    labelColor = "rgba(248, 181, 0, 1)";
    labelColorHover = "white";
    areaColor = "rgba(248, 181, 0, 0.25)";
    borderColor = labelColor;
    areaColorHover = "rgba(248, 181, 0, 0.75)";
  }

  if (labelColor) {
    values.label = {
      color: labelColor,
    };
  }
  if (labelColorHover) {
    values.emphasis ??= {};
    values.emphasis.label = {
      color: labelColorHover,
    };
  }

  if (areaColor) {
    values.itemStyle ??= {};
    values.itemStyle.areaColor = areaColor;
  }
  if (areaColorHover) {
    values.emphasis ??= {};
    values.emphasis.itemStyle = { areaColor: areaColorHover };
  }
  if (borderColor) {
    const itemStyle = {
      borderWidth: 2,
      borderColor,
      // shadowColor: 'white',
      // shadowBlur: 2,
      // shadowOffsetY: 1,
      // shadowOffsetX: 1,
    };
    values.itemStyle = Object.assign({}, values.itemStyle, itemStyle);
  }
}
