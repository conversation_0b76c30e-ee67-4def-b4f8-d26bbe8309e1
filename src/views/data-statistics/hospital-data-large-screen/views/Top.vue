<template>
  <div class="top">
    <div class="top-left">
      <slot />
    </div>
    <div class="top-title">
      <div class="flex-start-center" style="cursor: pointer" @click="isShow = !isShow">
        <span>{{ titleList[showIndex] }}</span>
        <img class="top-title-icon" src="../images/down-icon.png" />
      </div>
      <div v-if="isShow" class="top-title-list">
        <div
          v-for="(item, index) in titleList"
          :key="index"
          class="top-title-list-item"
          :class="{ currentStyle: showIndex === index }"
          @click.stop="handleItemClick(index)"
        >
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  showIndex: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["changeData"]);

const titleList = [
  "院外康复患者管理情况",
  "医务人员患者管理情况",
  "院外康复方案执行情况",
  "院外康复患者服务情况",
];

const isShow = ref(false);
const handleItemClick = (index: number) => {
  emit("changeData", index);
  isShow.value = false;
};
</script>
<style scoped lang="scss">
.top {
  width: 100%;
  height: 100px;
  background-image: url("../images/top-img.png");
  background-size: 100% 100%;
  position: relative;
  z-index: 4;
  &-left {
    position: absolute;
    top: 20%;
    left: 79px;
  }
  &-title {
    position: absolute;
    font-family: YouSheBiaoTiHei;
    font-weight: normal;
    font-size: 37px;
    color: #ecf6ff;
    line-height: 42px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 561px;
    display: flex;
    justify-content: center;
    position: relative;
    &-icon {
      width: 51px;
      height: 44px;
      margin-left: 17px;
      cursor: pointer;
    }
    &-list {
      position: absolute;
      top: 50px;
      width: 561px;
      height: 196px;
      background: #000000;
      box-shadow:
        inset -4px -4px 10px 0px rgba(51, 204, 204, 0.5),
        inset 4px 4px 10px 0px rgba(51, 204, 204, 0.5);
      border-radius: 1px;
      border: 1px solid #33cccc;
      &-item {
        cursor: pointer;
        height: 49px;
        line-height: 49px;
        box-sizing: border-box;
        font-family: YouSheBiaoTiHei;
        font-weight: normal;
        font-size: 20px;
        color: #ecf6ff;
        text-align: center;
        background: linear-gradient(
          90deg,
          rgba(51, 204, 204, 0.1) 0%,
          rgba(51, 204, 204, 0.1) 100%
        );
      }
    }
  }
}
.flex-start-center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.currentStyle {
  background: linear-gradient(
    90deg,
    rgba(51, 204, 204, 0.4) 0%,
    rgba(51, 204, 204, 0.4) 100%
  ) !important;
}
</style>
