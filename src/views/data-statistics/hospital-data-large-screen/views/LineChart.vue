<template>
  <div ref="chartRef" :style="{ width: width, height: height }" />
</template>
<script lang="ts">
import * as echarts from "echarts";
const offsetX = 10; //bar宽
const offsetY = 5; //倾斜角度
// 绘制左侧面
const CubeLeft = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    const xAxisPoint = shape.xAxisPoint;
    const c0 = [shape.x, shape.y]; // 左侧面 右上点
    const c1 = [shape.x - offsetX, shape.y - offsetY + 5]; // 左侧面  左上点
    const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY + 5]; // 左侧面 左下点
    const c3 = [xAxisPoint[0], xAxisPoint[1] + 5]; // 左侧面 右下点
    ctx.moveTo(c0[0], c0[1]);
    ctx.lineTo(c1[0], c1[1]);
    ctx.lineTo(c2[0], c2[1]);
    ctx.lineTo(c3[0], c3[1]);
  },
});
// 绘制右侧面
const CubeRight = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    const xAxisPoint = shape.xAxisPoint;
    const c1 = [shape.x, shape.y]; //右侧面左上点
    const c2 = [xAxisPoint[0], xAxisPoint[1] + 5]; //右侧面 左下点
    const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY + 5]; //右侧面  右下点
    const c4 = [shape.x + offsetX, shape.y - offsetY + 5]; //右侧面 右上点
    ctx.moveTo(c1[0], c1[1]);
    ctx.lineTo(c2[0], c2[1]);
    ctx.lineTo(c3[0], c3[1]);
    ctx.lineTo(c4[0], c4[1]);
  },
});
// 绘制顶面
const CubeTop = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    const c1 = [shape.x, shape.y + 5]; //顶部菱形下点
    const c2 = [shape.x + offsetX, shape.y - offsetY + 5]; //顶部菱形右点
    const c3 = [shape.x, shape.y - offsetX + 5]; //顶部菱形上点
    const c4 = [shape.x - offsetX, shape.y - offsetY + 5]; //顶部菱形左点
    ctx.moveTo(c1[0], c1[1]);
    ctx.lineTo(c2[0], c2[1]);
    ctx.lineTo(c3[0], c3[1]);
    ctx.lineTo(c4[0], c4[1]);
  },
});
// 注册三个面图形
echarts.graphic.registerShape("CubeLeft", CubeLeft);
echarts.graphic.registerShape("CubeRight", CubeRight);
echarts.graphic.registerShape("CubeTop", CubeTop);

export default {};
</script>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as PropType<{ name: string; value: number }[]>,
    default: () => [],
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "100%",
  },
});

const chartRef = useTemplateRef<HTMLElement | null>("chartRef");
let chart = null as null | echarts.ECharts;

watch(
  () => props.data,
  () => {
    renderChart();
  }
);

onMounted(() => {
  chart = echarts.init(chartRef.value);
  renderChart();
});

onUnmounted(() => {
  chart?.dispose();
});

const renderChart = () => {
  const options = buildChartOptions(props.data);
  chart?.setOption(options);
};

const buildChartOptions = (data: { name: string; value: number }[]) => {
  if (!data || !data.length) {
    return {};
  }

  let xAxisData = data.map((s) => s.name);
  let seriesData = data.map((s) => s.value);
  let colorArr = [
    ["#71ddff"],
    ["rgba(12, 149, 198, 1)", "rgba(13,8,16,0)"],
    ["rgba(34, 188, 255, 1)", "rgba(14,156,185,0)"],
  ];
  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params: any) {
        const item = params[1];
        return item.name + " : " + item.value;
      },
    },
    grid: {
      left: "5%",
      right: "10%",
      top: "15%",
      bottom: "5%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: "rgba(255, 255, 255, 0.60)",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 14,
        interval: 0,
        // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
        formatter: function (value: string) {
          const length = value.length;
          if (length > 3) {
            const start = Math.floor(length / 2);
            const str = value.slice(0, start) + "\n" + value.slice(start, length);
            return str;
          }
          return value;
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      // y轴（竖直线）
      axisLine: {
        show: false,
      },
      // y轴横向 标线
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(255,255,255,0.16)",
        },
      },
      // y轴刻度线
      axisTick: {
        show: false,
      },
      // y轴文字
      axisLabel: {
        fontSize: 14,
        color: "rgba(255, 255, 255, 0.60)",
      },
    },
    series: [
      {
        type: "custom",
        renderItem: (params: any, api: any) => {
          const location = api.coord([api.value(0), api.value(1)]);
          return {
            type: "group",
            children: [
              // 左侧面
              {
                type: "CubeLeft",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: colorArr[1][0],
                    },
                    {
                      offset: 1,
                      color: colorArr[1][1],
                    },
                  ]),
                },
              },
              // 右侧面
              {
                type: "CubeRight",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: colorArr[2][0],
                    },
                    {
                      offset: 1,
                      color: colorArr[2][1],
                    },
                  ]),
                },
              },
              // 顶部
              {
                type: "CubeTop",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: colorArr[0][0],
                    },
                    {
                      offset: 1,
                      color: colorArr[0][0],
                    },
                  ]),
                },
              },
            ],
          };
        },
        data: seriesData,
      },
      {
        type: "bar",
        label: {
          show: true,
          position: "top",
          formatter: (e: { value: number }) => {
            return e.value;
          },
          fontSize: 16,
          color: "#43C4F1",
          offset: [0, -5],
        },
        itemStyle: {
          color: "transparent",
        },
        tooltip: {},
        data: seriesData,
      },
    ],
  };
};
</script>
