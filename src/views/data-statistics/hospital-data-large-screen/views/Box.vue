<template>
  <div class="box-root" :style="{ width: width, height: height }">
    <div v-if="label" class="box-root-title">
      <div class="box-root-title-left" />
      <div class="box-root-title-icon" />
      <span class="box-root-title-label">{{ label }}</span>
      <div class="box-root-title-right">
        <div class="box-root-title-right-left" />
        <div class="box-root-title-right-right" />
      </div>
    </div>
    <div class="content"><slot /></div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  width: {
    type: String,
    default: "auto",
  },
  height: {
    type: String,
    default: "auto",
  },
  label: {
    type: String,
    default: "",
  },
});
</script>
<style scoped lang="scss">
.box-root {
  background: rgba(0, 0, 0, 0.3);
  box-shadow:
    inset -4px -4px 10px 0px rgba(51, 204, 204, 0.5),
    inset 4px 4px 10px 0px rgba(51, 204, 204, 0.5);
  border-radius: 1px;
  border: 1px solid #33cccc;
  display: flex;
  flex-direction: column;
  &-title {
    height: 43px;
    width: 100%;
    background: linear-gradient(90deg, rgba(51, 204, 204, 0.4) 0%, rgba(51, 204, 204, 0) 100%);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    &-left {
      width: 4px;
      height: 43px;
      background: #33cccc;
    }
    &-icon {
      margin-left: 14px;
      width: 8px;
      height: 12px;
      background: linear-gradient(180deg, #ffffff 0%, #95e4e4 100%);
      box-shadow: 0px 0px 11px 0px #33cccc;
      clip-path: polygon(0 0, 100% 50%, 0 100%);
    }
    &-label {
      flex: 1;
      margin-left: 10px;
      font-weight: 500;
      font-size: 21px;
      color: #ffffff;
      line-height: 29px;
      text-shadow: 0px 0px 11px #33cccc;
    }
    &-right {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      &-left {
        width: 6px;
        height: 14px;
        background: #33cccc;
        transform: skewX(40deg);
        margin-right: 10px;
      }
      &-right {
        width: 6px;
        height: 14px;
        background: #33cccc;
        opacity: 0.2;
        transform: skewX(40deg);
        margin-right: 17px;
      }
    }
  }
  .content {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
