<template>
  <Box width="1762px" label="医务人员管理患者Top10">
    <LineCharts v-if="deptData.length" :data="deptData" width="1762px" height="710px" />
  </Box>
</template>
<script setup lang="ts">
import Report_Api from "@/api/report";
import Box from "./Box.vue";
import LineCharts from "./LineChart.vue";

const props = defineProps({
  orgId: {
    type: String,
    default: "*",
  },
  deptId: {
    type: String,
    default: "*",
  },
  roleType: {
    type: String,
    default: "*",
  },
});

const deptData = ref<{ name: string; value: number }[]>([]);

const loadChartsData = async () => {
  const res = await Report_Api.getRedashList<{
    DoctorName: string;
    PatientCount: number;
  }>({
    queryName: "Report_HospitalDataAnalysisDoctorPatient",
    parameters: { OrgIds: props.orgId, DeptIds: props.deptId, RoleType: props.roleType },
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 99999,
  });
  if (res.Type === 200) {
    if (res.Data.Data.length) {
      deptData.value = res.Data.Data.map((item) => ({
        name: item.DoctorName,
        value: item.PatientCount * 1,
      }));
    } else {
      deptData.value = [];
    }
  } else {
    deptData.value = [];
  }
};

onMounted(() => {
  loadChartsData();
});

defineExpose({
  loadChartsData,
});
</script>
