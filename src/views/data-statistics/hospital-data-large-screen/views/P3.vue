<template>
  <Box width="1760px" label="院外康复方案执行情况">
    <div ref="chartRef" style="width: 1760px; height: 710px" />
  </Box>
</template>
<script lang="ts">
import * as echarts from "echarts";
import Report_Api from "@/api/report";
import dayjs from "dayjs";
import Box from "./Box.vue";

interface ChartData {
  Date: string;
  ExecutePrescriptionCount: number;
  PrescriptionCount: number;
}

export default defineComponent({
  components: {
    Box,
  },
  props: {
    orgId: {
      type: String,
      default: "*",
    },
    deptId: {
      type: String,
      default: "*",
    },
    timeBeginDt: {
      type: String,
      default: () => dayjs(new Date()).subtract(6, "month").format("YYYY-MM-01") + " 00:00:00",
    },
    timeEndDt: {
      type: String,
      default: () => dayjs(new Date()).format("YYYY-MM-DD") + " 23:59:59",
    },
    queryType: {
      type: String,
      default: "month",
    },
  },
  setup() {
    const chartRef = useTemplateRef<HTMLElement>("chartRef");
    const chart = shallowRef<echarts.ECharts | null>(null);
    onMounted(() => {
      chart.value = echarts.init(chartRef.value);
    });

    onUnmounted(() => {
      chart.value?.dispose();
    });

    return {
      chart,
    };
  },

  mounted() {
    this.loadChartsData();
  },

  methods: {
    async loadChartsData() {
      const res = await Report_Api.getRedashList<ChartData>({
        queryName: "Report_HospitalDataAnalysisPrescriptionChart",
        parameters: {
          OrgIds: this.orgId,
          DeptIds: this.deptId,
          TimeBeginDt: this.timeBeginDt,
          TimeEndDt: this.timeEndDt,
        },
        maxAge: 0,
        JobWaitingMs: 30000,
        pageIndex: 1,
        pageSize: 99999,
      });
      if (res.Type === 200) {
        const chartsData = this.loadChartsDataFinishing(res.Data.Data);
        const option = this.renderChart(chartsData);
        this.chart?.setOption(option);
      }
    },
    loadChartsDataFinishing(info: ChartData[]): { x: string[]; y: number[] } {
      if (!info.length) return { x: [], y: [] };
      const allExecutePrescriptionCount = info.reduce(
        (a, b) => a + b.ExecutePrescriptionCount * 1,
        0
      );
      const allPrescriptionCount = info.reduce((a, b) => a + b.PrescriptionCount * 1, 0);
      const allPercentage = (
        ((allExecutePrescriptionCount || 0) / (allPrescriptionCount || 0)) *
        100
      ).toFixed(1);

      this.$emit("getTitleContent", {
        index: 2,
        data: allPrescriptionCount,
        percentage: allPercentage,
      });

      switch (this.queryType) {
        case "month": {
          const groupedByList = this.groupedByKey(info);
          return {
            x: Object.keys(groupedByList),
            y: Object.values(groupedByList).map((s) =>
              s.reduce((a, b) => a + b.ExecutePrescriptionCount * 1, 0)
            ),
          };
        }
        case "day": {
          return {
            x: info.map((s) => s.Date),
            y: info.map((s) => s.ExecutePrescriptionCount * 1),
          };
        }
        default:
          return {
            x: [],
            y: [],
          };
      }
    },
    groupedByKey(list: ChartData[]): Record<string, ChartData[]> {
      const groupedByMonth = list.reduce(
        (result, item) => {
          // 提取日期中的年份和月份部分 (YYYY-MM)
          const month = item.Date.slice(0, 7);

          // 如果没有当前月份的组，先创建一个空数组
          if (!result[month]) {
            result[month] = [];
          }

          // 将当前数据项添加到相应的月份组中
          result[month].push(item);

          return result;
        },
        {} as Record<string, ChartData[]>
      );
      return groupedByMonth;
    },
    renderChart(chartsData: { x: string[]; y: number[] }) {
      return {
        tooltip: {
          axisPointer: {
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(0, 255, 233,0)",
                  },
                  {
                    offset: 0.5,
                    color: "rgba(255, 255, 255,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 255, 233,0)",
                  },
                ],
                global: false,
              },
            },
          },
        },
        grid: {
          top: "15%",
          left: "5%",
          right: "5%",
          bottom: "15%",
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: true,
            axisLine: {
              show: true,
            },
            splitArea: {
              color: "#f00",
              lineStyle: {
                color: "#f00",
              },
            },
            axisLabel: {
              color: "#fff",
              formatter: (value: string, index: number) => {
                if (index === 0 || index === chartsData.x.length - 1) {
                  return value;
                }
                return "";
              },
            },
            splitLine: {
              show: false,
            },
            data: chartsData.x,
          },
        ],

        yAxis: [
          {
            type: "value",
            minInterval: 1,
            min: 0,
            splitNumber: 4,
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(255,255,255,0.1)",
              },
            },
            axisLine: {
              show: true,
            },
            axisLabel: {
              show: true,
              margin: 20,
              color: "#d1e6eb",
            },
            axisTick: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: "执行方案数",
            type: "line",
            showAllSymbol: true,
            symbol: "circle",
            symbolSize: 10,
            lineStyle: {
              color: "#28adae",
              shadowColor: "rgba(0, 0, 0, .3)",
              shadowBlur: 0,
              shadowOffsetY: 5,
              shadowOffsetX: 5,
            },
            label: {
              show: false,
              position: "top",
              color: "#ffdc00",
            },
            itemStyle: {
              color: "#ffdc00",
              borderColor: "#fff",
              borderWidth: 3,
              shadowBlur: 0,
              shadowOffsetY: 2,
              shadowOffsetX: 2,
            },
            tooltip: {
              show: true,
            },
            data: chartsData.y,
          },
        ],
        dataZoom: [
          {
            type: "slider", // 选择 slider 类型，实现滑动条的缩放
            show: true, // 显示 dataZoom 组件
            xAxisIndex: 0, // 控制第一个 x 轴
            start: 0, // 默认显示的数据起始位置（0%）
            end: 100, // 默认显示的数据结束位置（100%）
          },
          {
            type: "inside", // 使用 inside 类型，实现鼠标滚轮缩放
            start: 0,
            end: 100,
          },
        ],
      };
    },
  },
});
</script>
<style scoped lang="scss"></style>
