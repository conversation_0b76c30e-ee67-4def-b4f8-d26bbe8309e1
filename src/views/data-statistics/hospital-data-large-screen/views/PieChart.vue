<template>
  <div ref="chartRef" :style="{ width: width, height: height }" />
</template>
<script setup lang="ts">
import * as echarts from "echarts";

export interface PieChartData {
  name: string;
  value: number;
}

const props = defineProps({
  data: {
    type: Array as PropType<PieChartData[]>,
    default: (): PieChartData[] => [],
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "100%",
  },
});

const chartRef = useTemplateRef<HTMLElement | null>("chartRef");
let chart = null as null | echarts.ECharts;

onMounted(() => {
  chart = echarts.init(chartRef.value);
  renderChart();
});

onUnmounted(() => {
  chart?.dispose();
});

watch(
  () => props.data,
  () => {
    renderChart();
  }
);

const renderChart = () => {
  const options = buildChartOption(props.data);
  chart?.setOption(options);
};
const buildChartOption = (pieData: PieChartData[]) => {
  var sum = pieData.reduce((per, cur) => per + cur.value, 0);
  var option = {
    color: ["#d4d4d4", "#1B4EC4", "#1D7DC8", "#1DB3CA", "#1CC18C", "#2CAB5C"],
    tooltip: {
      trigger: "item",
      right: "center",
    },
    legend: {
      orient: "vertical",
      left: "70%",
      align: "left",
      top: "center",
      itemHeight: 15,
      itemWidth: 15,
      itemGap: 20,
      height: 300,
      textStyle: {
        color: "#FFFFFF",
        rich: {
          a: {
            width: 60,
            fontSize: 18,
            fontWeight: 400,
            color: "#FFFFFF",
            lineHeight: 20,
            padding: [0, 30, 0, 0],
          },
        },
      },
      formatter: function (name: string) {
        let index = 0;
        let value = 0;
        let rate = 0;
        for (let i = 0; i < pieData.length; i++) {
          if (pieData[i].name == name) {
            value = pieData[i].value ? pieData[i].value : 0;
            rate = Number(((value / sum) * 100).toFixed(0));
            index = i;
          }
        }
        return `{a|${name}}{${index}|${rate + "%"}}`;
      },
    },
    series: [
      {
        type: "pie",
        radius: ["30%", "60%"],
        center: ["30%", "50%"],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
  return option;
};
</script>
