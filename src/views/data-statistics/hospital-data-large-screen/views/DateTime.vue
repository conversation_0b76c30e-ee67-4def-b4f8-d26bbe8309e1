<template>
  <div class="date-time">
    <div class="date-time-date">
      <div>{{ week }}</div>
      <div class="mt-1">{{ date }}</div>
    </div>
    <span class="date-time-time">{{ time }}</span>
  </div>
</template>

<script setup lang="ts">
const now = useNow();
const weekMap = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
const week = computed(() => weekMap[now.value.getDay()]);
const date = computed(() => now.value.toLocaleDateString());
const time = computed(() => now.value.toLocaleTimeString());
</script>
<style scoped lang="scss">
.date-time {
  display: flex;
  align-items: center;
  &-time {
    margin-left: 20px;
    font-weight: 500;
    font-size: 20px;
    color: #ffffff;
    line-height: 28px;
    text-shadow: 0px 0px 11px #33cccc;
  }
  &-date {
    font-weight: 400;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 12px;
  }
}
</style>
