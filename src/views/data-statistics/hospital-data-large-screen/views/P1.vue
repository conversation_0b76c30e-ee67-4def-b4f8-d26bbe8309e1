<template>
  <div class="box">
    <div class="box-left">
      <Box :label="avgAge">
        <div ref="ageChartRef" style="width: var(--left-width); height: 190px" />
      </Box>
      <Box :label="isAllOrg ? '医院分布' : '科室分布'" style="margin-top: 25px">
        <div ref="hospitalChartRef" style="width: var(--left-width); height: 500px" />
      </Box>
    </div>
    <div v-resize="resizeChart" class="box-center">
      <div ref="mapChartRef" style="width: 100%; height: 753px" />
      <div v-show="showBack" class="back-btn" @click="goBack">返回</div>
    </div>
    <div class="box-right">
      <Box label="疾病分布">
        <div ref="diseaseChartRef" style="width: var(--right-width); height: 753px" />
      </Box>
    </div>
  </div>
</template>

<script lang="ts">
import * as echarts from "echarts";
import {
  configItemStyle,
  geoConfig,
  geoToolTipOption,
  getAdcodeType,
  getMapJSON,
  otherSeriesOption,
  top3SeriesOption,
} from "./map-config";
import mapBG from "../images/map-bg.jpg";
import Report_Api from "@/api/report";
import Box from "./Box.vue";
import { AdcodeType } from "./map-config";

type Field = "Province" | "City" | "Country";

import type { LargeScreenContext } from "../large-screen-context";

export default defineComponent({
  components: {
    Box,
  },
  inject: ["largeScreenContext"],
  props: {
    orgId: {
      type: String,
      default: "*",
    },
    deptId: {
      type: String,
      default: "*",
    },
  },
  setup() {
    const ageChartRef = useTemplateRef<HTMLDivElement>("ageChartRef");
    const hospitalChartRef = useTemplateRef<HTMLDivElement>("hospitalChartRef");
    const mapChartRef = useTemplateRef<HTMLDivElement>("mapChartRef");
    const diseaseChartRef = useTemplateRef<HTMLDivElement>("diseaseChartRef");

    const ageChart = shallowRef<echarts.ECharts | null>(null);
    const hospitalChart = shallowRef<echarts.ECharts | null>(null);
    const mapChart = shallowRef<echarts.ECharts | null>(null);
    const diseaseChart = shallowRef<echarts.ECharts | null>(null);

    onMounted(() => {
      ageChart.value = echarts.init(ageChartRef.value);
      hospitalChart.value = echarts.init(hospitalChartRef.value);
      mapChart.value = echarts.init(mapChartRef.value);
      diseaseChart.value = echarts.init(diseaseChartRef.value);
    });

    onUnmounted(() => {
      ageChart.value?.dispose();
      hospitalChart.value?.dispose();
      mapChart.value?.dispose();
      diseaseChart.value?.dispose();
    });

    return {
      ageChart,
      hospitalChart,
      mapChart,
      diseaseChart,
    };
  },
  data() {
    return {
      avgAge: "年龄分布（平均年龄~岁）",
      deptSetInterval: null as null | NodeJS.Timeout,
      diseaseSetInterval: null as null | NodeJS.Timeout,
      query: {
        queryName: "Report_PatientStatistics",
        parameters: { OrgIds: this.orgId, DeptIds: this.deptId, Keyword: "*" },
        maxAge: 0,
        JobWaitingMs: 30000,
        pageIndex: 1,
        pageSize: 99999,
      },
      patientCount: 0,
      mapList: [] as string[],
      mapBG: null as HTMLImageElement | null,
    };
  },
  computed: {
    isAllOrg() {
      return this.query.parameters.OrgIds === "*";
    },
    showBack() {
      return this.mapList.length > 1;
    },
  },
  created() {
    const image = document.createElement("img");
    image.src = mapBG;
    this.mapBG = image;
  },
  mounted() {
    // 设置地图点击事件
    // @ts-ignore
    this.mapChart?.on("dblclick", "series.map", async (params: any) => {
      // console.log(params);
      if (params.data) {
        const { adcode, level } = params.data;
        if (level === "district") {
          ElMessage("无此区域地图显示！");
          return;
        }
        this.renderMapChart(adcode);
      }
    });
    this.loadChartsData();
  },
  beforeUnmount() {
    this.deptSetInterval && clearInterval(this.deptSetInterval);
    this.diseaseSetInterval && clearInterval(this.diseaseSetInterval);
  },
  destroyed() {
    this.deptSetInterval && clearInterval(this.deptSetInterval);
    this.diseaseSetInterval && clearInterval(this.diseaseSetInterval);
  },
  methods: {
    async loadChartsData() {
      this.query.parameters.DeptIds = this.deptId;
      this.query.parameters.OrgIds = this.orgId;
      this.diseaseSetInterval && clearInterval(this.diseaseSetInterval);
      this.diseaseSetInterval = null;
      this.deptSetInterval && clearInterval(this.deptSetInterval);
      this.deptSetInterval = null;
      // 获取年龄分布
      this.getAgeData();
      // 获取患者疾病
      this.getDiseaseData();
      // 获取科室分布
      this.getHospitalOrDepartmentData();
      // 获取患者分布
      this.initMap();
    },
    async getAgeData() {
      let params = null;
      if (this.isAllOrg) {
        params = {
          queryName: "Report_UserStatistics",
          parameters: {},
          maxAge: 0,
          JobWaitingMs: 30000,
          pageIndex: 1,
          pageSize: 99999,
        };
      } else {
        params = this.query;
      }
      const res = await Report_Api.getRedashList<{ PatientCount: number; TotalCount: number }>(
        params
      );
      if (res.Type === 200) {
        // 获取年龄分布
        const option = this.buildOptionsForAge(res.Data.Data[0]);
        this.patientCount = res.Data.Data[0].PatientCount || res.Data.Data[0].TotalCount || 0;
        this.$emit("getTitleContent", {
          index: 0,
          data: this.patientCount,
        });
        this.ageChart?.setOption(option);
      }
    },
    async getDiseaseData() {
      const copyData = JSON.parse(JSON.stringify(this.query));
      copyData.queryName = "Report_PatientDiseaseTagStatistics";
      const res = await Report_Api.getRedashList<{ Tag: string; Count: number }>(copyData);
      if (res.Type === 200) {
        const option = this.buildOptionsForDisease(res.Data.Data);
        this.diseaseChart?.setOption(option);
        this.autoScrollDiseaseChart(option);
      }
    },
    async getHospitalOrDepartmentData() {
      const copyData = JSON.parse(JSON.stringify(this.query));
      copyData.queryName = "Report_HospitalDataAnalysisPatientsProfile";
      delete copyData.parameters.Keyword;
      const res = await Report_Api.getRedashList<{ Name: string; PatientCount: number }>(copyData);
      if (res.Type === 200) {
        //获取科室分布
        const options1 = this.buildOptionsForDepartment(res.Data.Data.reverse());
        this.hospitalChart?.setOption(options1);
        this.autoScrollDepartmentChart(options1);
      }
    },
    autoScrollDiseaseChart(option: any) {
      // 需要在这里再次清除定时器，否则当网络不好时，连续调用这个方法，会出现定时器一直执行的问题
      this.diseaseSetInterval && clearInterval(this.diseaseSetInterval);
      this.diseaseSetInterval = setInterval(() => {
        if (option.dataZoom[0].endValue >= option.yAxis[1].data.length + 1) {
          option.dataZoom[0].endValue = option.yAxis[1].data.length + 1;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.diseaseChart?.setOption(option);
      }, 4000);
    },
    autoScrollDepartmentChart(option: any) {
      // 需要在这里再次清除定时器，否则当网络不好时，连续调用这个方法，会出现定时器一直执行的问题
      this.deptSetInterval && clearInterval(this.deptSetInterval);
      this.deptSetInterval = setInterval(() => {
        if (option.dataZoom[0].endValue >= 11) {
          option.dataZoom[0].endValue = 5;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.hospitalChart?.setOption(option);
      }, 4000);
    },
    async getPatientData(adcode: string) {
      // 根据 adcode 长度判断层级
      // 省级: 2位数字 + 0000，如 510000
      // 市级: 4位数字 + 00，如 510100
      // 区级: 6位数字，如 510104
      let queryType = "1";
      let params = {
        provinces: "*",
        cities: "*",
        countries: "*",
        orgId: this.orgId || "*",
      };

      const type = getAdcodeType(adcode);
      //

      const fieldMap: Record<AdcodeType, Field> = {
        1: "City",
        2: "Country",
        3: "Country",
        4: "Country",
      };
      const filterMap = {
        1: (item: { Province: string; City: string }) =>
          item.Province.slice(0, 2) === item.City.slice(0, 2),
        2: (item: { Province: string; City: string; Country: string }) =>
          item.Province.slice(0, 2) === item.City.slice(0, 2) &&
          item.City.slice(0, 4) === item.Country.slice(0, 4),
        3: (item: { Province: string; City: string; Country: string }) =>
          item.Province.slice(0, 2) === item.City.slice(0, 2) &&
          item.City.slice(0, 4) === item.Country.slice(0, 4),
        4: (item: { Province: string; Country: string }) =>
          item.Province.slice(0, 2) === item.Country.slice(0, 2),
      };
      const field = fieldMap[type];
      const filter = filterMap[type];

      switch (type) {
        case 1:
          queryType = "2";
          params.provinces = `${adcode}` === "100000" ? "500000|510000" : `${adcode}`;
          break;
        case 2:
          queryType = "3";
          params.cities = `${adcode}`;
          break;
        case 3:
          queryType = "3";
          params.countries = `${adcode}`;
          break;
        case 4:
          queryType = "3";
          params.provinces = `${adcode}`;
          break;
      }

      const res = await Report_Api.getRedashList<{
        Province: string;
        City: string;
        Country: string;
        Count: number;
      }>({
        queryName: "Report_PatientRegionalStatistics",
        parameters: {
          queryType,
          ...params,
        },
        maxAge: 0,
        JobWaitingMs: 30000,
        pageIndex: 1,
        pageSize: 9999,
      });
      if (res.Type === 200) {
        // 处理数据，并设置到图表上
        // 聚合一下重庆的数据
        let cq: { adcode: number; count: number } | null = null;
        const data = res.Data.Data.filter(filter).map((item) => {
          const adcode = Number(item[field]);
          const count = Number(item.Count);
          if (adcode == 500000) {
            if (cq) {
              cq.count += count;
            } else {
              cq = {
                adcode: 500000,
                count: count,
              };
            }
          }
          return { adcode, count };
        });

        if (cq) {
          return [...data.filter((item) => item.adcode !== 500000), cq];
        }

        return data;
      } else {
        return [];
      }
    },
    async getHospitalData() {
      const res = await Report_Api.getRedashList<{
        Id: string;
        Name: string;
        LatLon: string;
        PatientCount: number;
      }>({
        queryName: "Report_HospitalDataAnalysisPatientsProfile",
        parameters: { OrgIds: "*", DeptIds: "*" },
        maxAge: 0,
        JobWaitingMs: 30000,
        pageIndex: 1,
        pageSize: 999,
      });
      if (res.Type === 200) {
        if (this.isAllOrg) {
          return res.Data.Data.reverse()
            .filter((item) => item.LatLon)
            .map((item) => {
              return {
                ...item,
              };
            });
        } else {
          const hospital = res.Data.Data.filter((item) => item.LatLon).find(
            (item) => item.Id === this.orgId
          );
          if (hospital) {
            return [hospital];
          } else {
            return [];
          }
        }
      } else {
        return [];
      }
    },
    buildOptionsForDisease(responseData: { Tag: string; Count: number }[]) {
      let className = responseData.map((s) => s.Tag);
      let data = responseData.map((s) => s.Count);
      let dataBG = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
      let maxData = Math.max(...data);
      const myColor = [
        "#4A817A",
        "#1edcaa",
        "#756A92",
        "#00ADB1",
        "#FFE4E2",
        "#C97B9D",
        "#6C6BB1",
        "#FFAA7D",
        "#B3A7B7",
        "#1ea5f5",
        "#577DD4",
        "#92A24D",
      ];
      let option = {
        grid: {
          left: 20,
          top: 15,
          bottom: 0,
          right: 80,
        },
        xAxis: [
          {
            type: "value",
            max: maxData,
            show: false,
          },
          {
            type: "value",
            show: false,
          },
        ],
        yAxis: [
          {
            type: "category",
            inverse: true,
            axisLabel: {
              show: true,
              margin: 2,
              color: "#FFFFFF",
              fontSize: 19,
              // 调整左侧文字的3个属性，缺一不可
              verticalAlign: "bottom",
              align: "top",
              //调整文字上右下左
              padding: [15, 0, 15, 0],
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            data: className,
          },
          {
            type: "category",
            inverse: true,
            axisTick: "none",
            axisLine: "none",
            show: true,
            axisLabel: {
              color: "#FFFFFF",
              fontSize: 19,
              formatter: (value: number) => {
                if (this.patientCount === 0) return "--";
                const total = this.patientCount;
                const percentage = ((value / total) * 100).toFixed(2);
                return percentage + "%";
              },
            },
            data: data,
          },
        ],
        series: [
          {
            name: "进度部分",
            type: "bar",
            zlevel: 1,
            itemStyle: {
              borderRadius: 3,
              color: (params: any) => {
                return {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: myColor[params.dataIndex],
                    },
                    {
                      offset: 0.5,
                      color: myColor[params.dataIndex],
                    },
                    {
                      offset: 1,
                      color: myColor[params.dataIndex],
                    },
                  ],
                };
              },
            },
            barWidth: 5,
            barGap: "0%",

            data: data,
          },
          {
            name: "背景部分",
            type: "bar",
            xAxisIndex: 1,
            barWidth: 5,
            barGap: "-100%",
            data: dataBG,
            itemStyle: {
              color: "#D0DEEE",
              opacity: 0.1,
            },
          },
          // 进度条的小圆圈
          {
            name: "小圈圈",
            type: "scatter",
            emphasis: {
              scale: false,
            },
            symbol:
              "image://data:image/png;base64,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",
            symbolSize: [26, 26],
            itemStyle: {
              color: "#FFF",
              shadowColor: "rgba(0, 255, 254, 0.53)",
              shadowBlur: 5,
              borderWidth: 1,
              opacity: 1,
            },
            z: 2,
            zlevel: 10,
            data: data,
            animationDelay: 500,
          },
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1], // 这里是从X轴的0刻度开始
            show: false, // 是否显示滑动条，不影响使用
            type: "slider", // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: data.length + 1, // 展示到第几个。
          },
        ],
      };
      return option;
    },
    buildOptionsForDepartment(info: { Name: string; PatientCount: number }[]) {
      let className = info.map((s) => s.Name);
      let data = info.map((s) => s.PatientCount * 1);
      let dataBG = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
      let maxData = Math.max(...info.map((s) => s.PatientCount * 1));
      const myColor = [
        "#4A817A",
        "#1edcaa",
        "#756A92",
        "#00ADB1",
        "#FFE4E2",
        "#C97B9D",
        "#6C6BB1",
        "#FFAA7D",
        "#B3A7B7",
        "#1ea5f5",
        "#577DD4",
        "#92A24D",
      ];
      let option = {
        grid: {
          left: 20,
          top: 15,
          bottom: 0,
          right: 70,
        },
        xAxis: [
          {
            type: "value",
            max: maxData,
            show: false,
          },
          {
            type: "value",
            show: false,
          },
        ],
        yAxis: [
          {
            type: "category",
            inverse: true,
            axisLabel: {
              show: true,
              margin: 2,
              color: "#FFFFFF",
              fontSize: 19,
              // 调整左侧文字的3个属性，缺一不可
              verticalAlign: "bottom",
              align: "top",
              //调整文字上右下左
              padding: [15, 0, 15, 0],
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            data: className,
          },
          {
            type: "category",
            inverse: true,
            axisTick: "none",
            axisLine: "none",
            show: true,
            axisLabel: {
              color: "#FFFFFF",
              fontSize: 16,
              formatter: (value: string) => {
                return value + "人";
              },
            },
            data: data,
          },
        ],
        series: [
          {
            name: "进度部分",
            type: "bar",
            zlevel: 1,
            itemStyle: {
              borderRadius: 3,
              color: (params: { dataIndex: number }) => {
                return {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: myColor[params.dataIndex],
                    },
                    {
                      offset: 0.5,
                      color: myColor[params.dataIndex],
                    },
                    {
                      offset: 1,
                      color: myColor[params.dataIndex],
                    },
                  ],
                };
              },
            },
            barWidth: 5,
            barGap: "0%",

            data: data,
          },
          {
            name: "背景部分",
            type: "bar",
            xAxisIndex: 1,
            barWidth: 5,
            barGap: "-100%",
            data: dataBG,
            itemStyle: {
              color: "#D0DEEE",
              opacity: 0.1,
            },
          },
          // 进度条的小圆圈
          {
            name: "小圈圈",
            type: "scatter",
            emphasis: {
              scale: false,
            },
            symbol:
              "image://data:image/png;base64,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",
            symbolSize: [26, 26],
            itemStyle: {
              color: "#FFF",
              shadowColor: "rgba(0, 255, 254, 0.53)",
              shadowBlur: 5,
              borderWidth: 1,
              opacity: 1,
            },
            z: 2,
            zlevel: 10,
            data: data,
            animationDelay: 500,
          },
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1], // 这里是从X轴的0刻度开始
            show: false, // 是否显示滑动条，不影响使用
            type: "slider", // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 5, // 展示到第几个。
          },
        ],
      };
      return option;
    },
    buildOptionsForAge(responseData: any) {
      var chartName = ["60岁以上", "40-60岁", "20-40岁", "20岁以下", "其他"];
      var chartData = [
        responseData.AgeGreaterThan60 || 0,
        responseData.AgeBetween40To60 || 0,
        responseData.AgeBetween20To40 || 0,
        responseData.AgeLessThan20 || 0,
        responseData.UnkownAge || 0,
      ];
      this.avgAge = `年龄分布（平均年龄${responseData.AverageAge || "~"}岁）`;
      var data = [];
      var legendName = [];
      const total = chartData.reduce((item, current) => item + current * 1, 0);
      for (var i = 0; i < chartData.length; i++) {
        var c = {
          value: chartData[i],
          name: chartName[i],
        };
        data[i] = c;
        legendName[i] = chartName[i];
      }
      return {
        tooltip: {
          trigger: "item",

          formatter: "{b} : {d}% <br/> {c}",
        },
        legend: {
          background: "#33CCCC",
          orient: "vertical",
          x: "40%",
          y: "center",
          itemWidth: 20,
          itemHeight: 20,
          align: "left",
          padding: 20,
          itemStyle: {
            borderWidth: 0,
          },
          textStyle: {
            rich: {
              name: {
                color: "#ffffff", // 名称颜色
                fontSize: 14,
                fontWeight: "bold",
              },
              padding: {
                fontSize: 14, // 空格的字体大小
                padding: [0, 20, 0, 0], // 控制 name 和 value 之间的间距
              },
              value: {
                color: "#ffffff", // 名称颜色
                fontSize: 14,
                fontWeight: "bold",
              },
            },
          },
          formatter: (name: string) => {
            const nameIndex = chartName.findIndex((s) => s === name);
            const value = chartData[nameIndex];
            const percentage = total
              ? ((value / total) * 100).toFixed(2)
              : (100 / chartName.length).toFixed(1);
            return `{name|${name}}{padding|}{value|${percentage}%}`;
          },
          data: legendName,
        },
        series: [
          {
            type: "pie",
            radius: ["30%", "60%"],
            center: ["20%", "50%"],
            color: ["#269595", "#2dc5c5", "#59d3d3", "#8ae0e0", "#d4d4d4"],
            data: data,
            labelLine: {
              show: false,
              length: 20,
              length2: 20,
              lineStyle: {
                color: "#12EABE",
                width: 2,
              },
            },
            label: {
              show: false,
              formatter: "{c|{c}}\n{hr|}\n{d|{d}%}",
              rich: {
                b: {
                  fontSize: 20,
                  color: "#12EABE",
                  align: "left",
                  padding: 4,
                },
                hr: {
                  borderColor: "#12EABE",
                  width: "100%",
                  borderWidth: 2,
                  height: 0,
                },
                d: {
                  fontSize: 20,
                  color: "#fff",
                  align: "left",
                  padding: 4,
                },
                c: {
                  fontSize: 20,
                  color: "#fff",
                  align: "left",
                  padding: 4,
                },
              },
            },
          },
        ],
      };
    },
    resizeChart() {
      this.mapChart?.resize();
    },
    // 点击返回，地图返回上一级
    async goBack() {
      // console.log(this.mapList);

      if (this.mapList.length >= 2) {
        const adcode = this.mapList[this.mapList.length - 2];
        this.mapList.splice(-2, 2);
        // console.log(this.mapList);
        this.renderMapChart(adcode);
      } else {
        await this.renderMapChart("100000");
      }
    },
    async initMap() {
      this.mapList = [];
      if (this.isAllOrg) {
        await this.renderMapChart("100000");
      } else {
        console.debug("largeScreenContext", this.largeScreenContext);
        const adcodeList = (this.largeScreenContext as LargeScreenContext).getAdcodeList();
        // console.debug("adcodeList", adcodeList);

        if (adcodeList.length >= 2) {
          this.mapList.push("100000");
          this.mapList.push(adcodeList[0]);
          await this.renderMapChart(adcodeList[1]);
        } else {
          await this.renderMapChart("100000");
        }
      }
    },
    // 渲染地图
    async renderMapChart(adcode: string) {
      // console.log("renderChart", adcode, typeof adcode);
      let mapName = `${adcode}`;
      this.mapList.push(adcode);

      let [mapData, patientData, hospitalData] = await Promise.all([
        getMapJSON(adcode),
        this.getPatientData(adcode),
        this.getHospitalData(),
      ]);

      let max = 0;
      patientData.forEach((item) => {
        if (item.count > max) {
          max = item.count;
        }
      });

      if (mapData === null) {
        ElMessage.warning("无此区域地图显示！");
        this.mapList = ["100000"];
        mapName = "100000";
        mapData = await getMapJSON("100000");
        // 配置地图
        this.setOptionGeo(mapName, max);
      }
      echarts.registerMap(mapName, mapData);
      // 配置地图
      this.setOptionGeo(mapName, max);

      // 根据 json 数据拼装 mapData， 用于地图点击下钻时传递数据，主要有adcode、name
      const total = patientData.reduce((item, current) => item + current.count, 0);

      // console.log("总数", total, patientData);
      const data = mapData.features.map((item: { properties: { adcode: number } }) => {
        const values = {
          count: 0,
          value: 0,
          percent: 0,
        };
        if (total !== 0) {
          patientData.forEach((p) => {
            if (p.adcode === item.properties.adcode) {
              values.count = p.count;
              values.percent = p.count / total;
              values.value = p.count;
              configItemStyle(values);
            }
          });
        }
        return { ...item.properties, ...values };
      });

      // this.chart3.ma
      const hospitals = hospitalData.map((item, index) => {
        const [longitude, latitude] = item.LatLon.split(",").map(Number);
        let name = item.Name;
        if (index < 3) {
          name = `NO.${index + 1} ${item.Name}`;
        }
        return {
          name,
          value: [
            longitude, // 经度
            latitude, // 纬度
            name, // 医院名称
            item.PatientCount, // 开方数量 之后 改为患者数量
          ],
        };
      });

      // 配置option
      this.setDataForMap(data, hospitals, mapName);
    },

    setOptionGeo(name: string, max: number) {
      const mapConfig = {
        // tooltip 提示配置项
        tooltip: geoToolTipOption,
        geo: {
          ...geoConfig,
          map: name,
        },
        series: [],
      };
      // @ts-ignore
      mapConfig.geo.itemStyle.areaColor = {
        image: this.mapBG, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
        repeat: "no-repeat", // 是否平铺，可以是 'repeat-x', 'repeat-y', 'no-repeat'
      };
      // console.log(mapConfig);

      this.mapChart?.setOption(mapConfig, true);
    },
    setDataForMap(data: any, hospitals: any, mapName: string) {
      // console.log(data);

      const option = {
        series: [
          {
            ...otherSeriesOption,
            id: "other",
            data: hospitals.slice(3),
          },
          {
            ...top3SeriesOption,
            data: hospitals.slice(0, 3),
          },
          {
            id: "map",
            type: "map",
            // geoIndex: 0,
            map: mapName,
            zoom: 1.4,
            roam: false,
            itemStyle: {
              areaColor: "transparent",
              // borderColor: '#8CF5F4', //'rgba(69,203,197)', // '#33cccc',
              // borderWidth: 1,
              // shadowColor: 'white',
              // shadowBlur: 1,
              // shadowOffsetY: 1,
              // shadowOffsetX: 1,
            },
            label: {
              show: true,
              color: "white",
              fontSize: 16,
              fontWeight: "500",
              shadowColor: "black",
              formatter: `{b}\n{c}`,
            },
            // 设置高亮状态下的多边形和标签样式
            emphasis: {
              itemStyle: {
                areaColor: "rgba(51, 204, 204, 0.5)",
              },
              // 设置字体
              label: {
                color: "white",
                fontSize: 18,
              },
            },
            data,
          },
        ],
      };
      this.mapChart?.setOption(option);
    },
  },
});
</script>
<style scoped lang="scss">
.box {
  --left-width: 350px;
  --right-width: 350px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-left {
    width: var(--left-width);
    display: flex;
    flex-direction: column;
  }
  &-center {
    flex: 1;
    position: relative;
    margin: 0 10px;

    .back-btn {
      position: absolute;
      top: 0;
      width: 96px;
      height: 43px;
      background: linear-gradient(180deg, rgba(51, 204, 204, 0.4) 0%, rgba(51, 204, 204, 0) 100%);
      box-shadow: inset 0px 0px 7px 4px rgba(51, 204, 204, 0.6);
      border-radius: 3px;
      border: 1px solid;
      border-image: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0),
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0)
        )
        1 1;
      text-align: center;
      font-weight: 600;
      font-size: 19px;
      color: #7aa3cc;
      line-height: 43px;
      cursor: pointer;
    }
  }
  &-right {
    width: var(--right-width);
  }
}
</style>
