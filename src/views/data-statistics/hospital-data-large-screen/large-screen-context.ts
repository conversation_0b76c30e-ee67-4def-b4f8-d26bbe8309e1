export type LargeScreenContext = {
  org: { Id: string; Name: string; City?: string } | null;
  getAdcodeList: () => string[];
};

const largeScreenContext: LargeScreenContext = {
  org: null,
  getAdcodeList() {
    if (this.org && this.org.City) {
      const adcode: string[] = JSON.parse(this.org.City);
      if (Array.isArray(adcode) && adcode.length > 2) {
        return adcode;
      } else {
        return ["100000"];
      }
    } else {
      return ["100000"];
    }
  },
};

export default largeScreenContext;
