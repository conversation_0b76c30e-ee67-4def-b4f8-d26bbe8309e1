<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto max-h-600px">
    <el-form
      :ref="kFormRef"
      :model="formData"
      label-width="80px"
      :inline="true"
      :disabled="props.disabled"
    >
      <div
        v-for="(express, index) in formData"
        :key="index"
        class="flex flex-col items-stretch justify-start mb-20px"
        :class="{ 'border border-solid border-#dcdfe6 p-20px': formData.length > 1 }"
      >
        <el-form-item label="快递单号" :prop="`${index}.ExpressNum`">
          <el-input v-model="express.ExpressNum" placeholder="请输入快递单号" clearable />
        </el-form-item>
        <el-form-item label="发货备注" :prop="`${index}.Remark`">
          <el-input
            v-model="express.Remark"
            placeholder="请输入发货备注"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 8 }"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormInstance } from "element-plus";
import Order_Api from "@/api/order";
import { ExpressCompany, SendExpressInputDTO } from "@/api/order/types";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";
const props = withDefaults(
  defineProps<{
    // 订单编号号
    orderNo: string;
    // 物流信息
    data?: OrderExpress[];
    // 是否禁用
    disabled?: boolean;
  }>(),
  {
    disabled: false,
  }
);

const emit = defineEmits(["cancel", "submit"]);

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.data));
  if (data && Array.isArray(data)) {
    formData.push(...data);
  }
  if (formData.length === 0) {
    formData.push({
      ExpressNum: "",
      Remark: "",
    });
  }
});

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = reactive<OrderExpress[]>([]);

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      // 是否存在快递单号和备注同时为空
      const hasEmptyExpress = formData.some((item) => !item.ExpressNum && !item.Remark);
      if (hasEmptyExpress) {
        ElMessage.warning("请填写快递单号或备注");
        return;
      }

      // 获取快递单号
      const expressNums = formData
        .filter((item) => item.ExpressNum)
        .map((item) => item.ExpressNum!);
      kEnableDebug && console.debug("快递单号", expressNums);
      if (expressNums.length === 0) {
        // 仅填写了备注
        const uploadExpressData = formData.map((item) => {
          return {
            OrderNo: props.orderNo,
            Remark: item.Remark,
          };
        });
        requestSendExpress(uploadExpressData);
        return;
      }

      // 是否有相同快递单号
      const expressNumSet = new Set(expressNums);
      if (expressNumSet.size !== expressNums.length) {
        ElMessage.warning("快递单号重复");
        return;
      }

      // 获取快递公司信息
      const r = await requestExpressCompany(expressNums);
      if (r.Type !== 200) {
        ElMessage.warning(r.Content);
        return;
      }

      const uploadExpressData = expressNums.map((num) => {
        return {
          OrderNo: props.orderNo,
          ExpressNum: num,
          ExpressComName: r.Data![num].Name,
          ExpressComCode: r.Data![num].ComCode,
          Remark: formData.find((item) => item.ExpressNum === num)?.Remark,
        };
      });
      requestSendExpress(uploadExpressData);
    } else {
      kEnableDebug && console.debug("提交失败", fields, formData);
    }
  });
}

/** 请求发货 */
async function requestSendExpress(data: OrderExpress[]) {
  formLoading.value = true;
  const params: SendExpressInputDTO = {
    OrderExpresses: data,
  };
  const r = await Order_Api.sendExpress(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}

// 获取快递公司的数据
async function requestExpressCompany(
  expressNums: string[]
): Promise<ServerResult<ExpressCompany | null>> {
  const r = await Order_Api.getExpressCompany({
    nums: expressNums.join(","),
  });
  if (r.Type !== 200) {
    return r;
  }

  // 筛选出根据快递单号，未查询到快递信息
  const emptyExpress = Object.entries(r.Data).reduce((pre, [key, value]) => {
    if (!expressNums.includes(key) || Object.keys(value).length === 0) {
      pre.push(key);
    }
    return pre;
  }, [] as string[]);
  if (emptyExpress.length > 0) {
    return {
      Type: 500,
      Content: `未存在${emptyExpress.join("、")}的快递单号`,
      Data: null,
    };
  }

  return r;
}
</script>

<style lang="scss" scoped></style>
