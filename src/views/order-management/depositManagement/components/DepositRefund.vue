<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
    <el-form-item label="支付方式">
      {{ payTypeText }}
    </el-form-item>
    <el-form-item label="退款类型">
      <el-select v-model="depositRefundType" class="100px" placeholder="退款方式">
        <el-option label="全额退款" :value="0" />
        <el-option label="部分退款" :value="1" />
      </el-select>
    </el-form-item>
    <el-form-item label="退款金额" prop="Price">
      <el-input-number
        v-model="form.Price"
        placeholder="请输入退款金额"
        :min="0"
        :max="payPrice"
        :step="0.1"
        :precision="2"
        class="w-full"
      />
    </el-form-item>
    <el-form-item v-if="depositRefundType === 1" label="退款原因" prop="Reason">
      <el-input v-model="form.Reason" type="textarea" placeholder="请输入退款原因" class="w-full" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { DeviceManageItem } from "@/api/consult/types";
import { RefundDepositInputDTO } from "@/api/order/types";
import { FormInstance, FormRules } from "element-plus";

const payPrice = ref<number>(0);
const payTypeText = ref<string>("");
const depositRefundType = ref<number>(0);
const formRef = ref<FormInstance>();

const validatePrice = (rule: any, value: any, callback: any) => {
  if (+value === 0) {
    callback(new Error("请输入退款金额"));
  }
  if (+value > payPrice.value) {
    callback(new Error(`退款金额不可超过支付金额${payPrice.value}`));
  } else {
    callback();
  }
};
const rules = reactive<FormRules<RefundDepositInputDTO>>({
  Price: [
    {
      type: "number",
      whitespace: true,
      required: true,
      message: "请输入金额",
      trigger: "blur",
    },
    {
      validator: validatePrice,
      trigger: "blur",
    },
  ],
  Reason: [
    {
      type: "string",
      whitespace: true,
      required: true,
      message: "请输入部分退款原因",
      trigger: "blur",
    },
  ],
});

const form = ref<RefundDepositInputDTO>({
  OrderNo: "",
  OrderAftersaleId: "",
  Price: 0,
  Reason: "",
});

const handleSubmit = async (): Promise<RefundDepositInputDTO | null> => {
  if (!formRef.value) return null;
  try {
    await formRef.value.validate();
    return form.value;
  } catch (error) {
    console.error("表单验证失败:", error);
    return null;
  }
};

const handleProcessingData = (info: DeviceManageItem) => {
  form.value.OrderNo = info.OrderNo;
  const devices = info.DeviceOrderInfo.OrderDetails.filter((x) => x.RelationId === info.DeviceId);
  if (devices.length) {
    const firstDevice = devices[0];
    payPrice.value = firstDevice.Price;
    const OrderAftersaleList = devices[0].OrderAftersales.filter((s) => s.Status === 2);
    if (OrderAftersaleList && OrderAftersaleList.length > 0) {
      form.value.OrderAftersaleId = OrderAftersaleList[0].Id;
    } else {
      form.value.OrderAftersaleId = "";
    }
  }
  const PAYMENT_TYPE_LABELS = ["微信", "支付宝", "免费", "其他"];
  payTypeText.value = PAYMENT_TYPE_LABELS[info.DeviceOrderInfo.PayType];
};

interface Props {
  detailInfo: DeviceManageItem | null;
}
const props = defineProps<Props>();

watch(
  () => props.detailInfo,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
