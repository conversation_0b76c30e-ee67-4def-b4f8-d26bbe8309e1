<template>
  <div class="detail-content-wrapper p-4 dark:bg-gray-800 dark:text-gray-200">
    <el-descriptions
      v-if="props.detailInfo"
      :column="1"
      border
      size="small"
      class="custom-descriptions"
    >
      <template #title>
        <div class="flex items-center text-lg font-semibold">
          <img :src="deviceIcon" alt="设备图标" class="w-6 h-6 mr-3" />
          <span>{{ props.detailInfo?.DeviceName }}</span>
        </div>
      </template>

      <el-descriptions-item label="押金金额">
        {{ getDepositAmountText(props.detailInfo) }}
      </el-descriptions-item>
      <el-descriptions-item label="支付方式">
        {{ getPaymentTypeText(props.detailInfo) }}
      </el-descriptions-item>

      <template
        v-if="
          props.detailInfo?.DeviceBizState === 8 ||
          props.detailInfo?.DeviceBizState === 7 ||
          props.detailInfo?.DeviceBizState === 6
        "
      >
        <el-descriptions-item v-if="props.detailInfo?.ReturnTime" label="退还时间">
          {{ handleFormatDate(props.detailInfo.ReturnTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="退还快递单号">
          {{ getExpressInfoField(props.detailInfo, "ExpressNum") }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ getExpressInfoField(props.detailInfo, "Remark") }}
        </el-descriptions-item>
      </template>

      <template
        v-if="
          (props.detailInfo?.DeviceBizState === 8 || props.detailInfo?.DeviceBizState === 7) &&
          props.detailInfo?.StorageTime
        "
      >
        <el-descriptions-item label="入库时间">
          {{ handleFormatDate(props.detailInfo.StorageTime) }}
        </el-descriptions-item>
      </template>

      <template v-if="props.detailInfo?.DeviceBizState === 8 && refundInfo">
        <el-descriptions-item label="退款方式">{{ refundInfo?.typeText }}</el-descriptions-item>
        <el-descriptions-item
          v-if="refundInfo?.typeText === '部分退款' && getRefundReasonsText(props.detailInfo)"
          label="部分退款原因"
        >
          {{ getRefundReasonsText(props.detailInfo) }}
        </el-descriptions-item>
        <el-descriptions-item label="退款金额">¥{{ refundInfo?.amount }}</el-descriptions-item>
        <el-descriptions-item v-if="refundInfo?.rawTime" label="退款时间">
          {{ refundInfo?.time }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <div v-else class="text-center text-gray-500 py-10">暂无详情信息</div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { computed } from "vue";
import { DeviceManageItem, DeviceManageOrderInfoDetail } from "@/api/consult/types";
import deviceIcon from "@/assets/images/deviceIcon.png";
function getCurrentDeviceOrderDetail(
  detailInfo: DeviceManageItem | null
): DeviceManageOrderInfoDetail | undefined {
  if (!detailInfo?.DeviceOrderInfo?.OrderDetails?.length) return undefined;
  return detailInfo.DeviceOrderInfo.OrderDetails.find((s) => s.RelationId === detailInfo.DeviceId);
}

function getDepositAmountText(detailInfo: DeviceManageItem | null): string {
  if (!detailInfo) return "¥0";
  const currentDeviceItem = getCurrentDeviceOrderDetail(detailInfo);
  if (currentDeviceItem) {
    return `¥${currentDeviceItem.Price}`;
  }
  return "¥0";
}

const PAYMENT_TYPE_LABELS = ["微信", "支付宝", "免费", "其他"];
function getPaymentTypeText(detailInfo: DeviceManageItem | null): string {
  if (!detailInfo?.DeviceOrderInfo) return "";
  return PAYMENT_TYPE_LABELS[detailInfo.DeviceOrderInfo.PayType] ?? "未知";
}

function handleFormatDate(date: string): string {
  if (!date) return "";
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
}

function getExpressInfoField(
  detailInfo: DeviceManageItem | null,
  fieldName: keyof OrderExpress
): string {
  const orderExpresses = detailInfo?.DeviceOrderInfo?.OrderExpresses;
  if (!orderExpresses || !orderExpresses.length) return "";
  const returnShipmentExpress = orderExpresses.find((s) => s.Type === 1);
  if (returnShipmentExpress) {
    const value = returnShipmentExpress[fieldName];
    return String(value || "");
  }
  return "";
}

function getRefundReasonsText(detailInfo: DeviceManageItem | null): string {
  const currentDeviceOrderDetail = getCurrentDeviceOrderDetail(detailInfo);
  if (!currentDeviceOrderDetail?.OrderAftersales?.length) {
    return "";
  }
  return currentDeviceOrderDetail.OrderAftersales.map((as) => as.Reason)
    .filter(Boolean)
    .join("、");
}

function determineRefundStatusText(totalRefundAmount: number, orderTotalPrice?: number): string {
  if (typeof orderTotalPrice !== "number" || orderTotalPrice < 0) {
    return "订单总价信息不足";
  }
  if (totalRefundAmount < orderTotalPrice) {
    return "部分退款";
  }
  if (totalRefundAmount === orderTotalPrice) {
    return "全部退款";
  }
  return "退款金额异常"; // Implies totalRefundAmount > orderTotalPrice
}

// New computed property for refund information
const refundInfo = computed(() => {
  if (!props.detailInfo) return null;
  const currentDeviceOrderDetail = getCurrentDeviceOrderDetail(props.detailInfo);

  const defaultRefundState = { typeText: "未查询到退款方式", amount: 0, time: "", rawTime: "" };

  if (!currentDeviceOrderDetail?.OrderAftersales?.length) {
    return defaultRefundState;
  }

  const completedRefunds = currentDeviceOrderDetail.OrderAftersales.filter((s) => s.Status === 3);
  if (!completedRefunds.length) {
    return { typeText: "未查询到已完成的退款", amount: 0, time: "", rawTime: "" };
  }

  const totalRefundAmount = completedRefunds.reduce((sum, current) => sum + current.Price, 0);
  // Ensure safe access to RefundTime, though completedRefunds is checked for length > 0
  const firstRefundTime = completedRefunds[0]?.RefundTime;

  // Call the helper function to determine refund type text
  const typeText = determineRefundStatusText(
    totalRefundAmount,
    currentDeviceOrderDetail.TotalPrice
  );

  return {
    typeText,
    amount: totalRefundAmount,
    time: firstRefundTime ? handleFormatDate(firstRefundTime) : "",
    rawTime: firstRefundTime || "",
  };
});

interface Props {
  detailInfo: DeviceManageItem | null;
}
const props = defineProps<Props>();
</script>

<style lang="scss" scoped></style>
