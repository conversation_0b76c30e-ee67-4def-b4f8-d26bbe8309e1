<template>
  <div class="p-4 bg-white dark:bg-dark-700">
    <div class="patUser mb-4">
      <div class="img flex items-center">
        <img :src="patUserInfo.HeadImg" alt="User Avatar" class="w-12 h-12 rounded-full mr-3" />
        <div class="userInfo">
          <span class="name text-lg font-semibold block">{{ patUserInfo.Name }}</span>
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ patUserInfo.PhoneNumber }}
          </span>
        </div>
      </div>
    </div>
    <div class="device mb-6">
      <div class="flex items-center">
        <img :src="deviceIcon" alt="Device Icon" class="w-10 h-10 mr-3" />
        <span class="text-base text-gray-700 dark:text-gray-300">
          {{ props.detailInfo?.DeviceName }}
        </span>
      </div>
    </div>
    <el-form
      ref="deviceReturnFormRef"
      :model="backDeviceQuery"
      :rules="formRules"
      label-position="left"
      label-width="100px"
    >
      <el-form-item label="快递单号:" prop="ExpressNumber">
        <el-input
          v-model="backDeviceQuery.ExpressNumber"
          type="text"
          placeholder="请输入快递单号"
        />
      </el-form-item>
      <el-form-item label="备注:" prop="ApplyReason">
        <el-input
          v-model="backDeviceQuery.ApplyReason"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="3"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import Bff_Api from "@/api/bff";
import { DeviceManageItem, ReturnOrderDetailInputDTO } from "@/api/consult/types";
import deviceIcon from "@/assets/images/deviceIcon.png";
import Order_Api from "@/api/order";

let patUserInfo = reactive<BaseUserProfile>({} as BaseUserProfile);
let orderDetailId = ref<string>("");
let backUserInfo = ref<GetCodeItem>({} as GetCodeItem);

const deviceReturnFormRef = ref<FormInstance>();

const backDeviceQuery = ref<ReturnOrderDetailInputDTO>({
  ExpressName: "",
  ExpressNumber: "",
  ExpressCode: "",
  Phone: "",
  ApplyReason: "",
  OrderDetailId: "",
});

const validateRequiredFields = (rule: any, value: any, callback: any) => {
  if (!backDeviceQuery.value.ExpressNumber && !backDeviceQuery.value.ApplyReason) {
    callback(new Error("快递单号和备注至少填写一项"));
  } else {
    if (backDeviceQuery.value.ExpressNumber) {
      deviceReturnFormRef.value?.clearValidate("ApplyReason");
    }
    if (backDeviceQuery.value.ApplyReason) {
      deviceReturnFormRef.value?.clearValidate("ExpressNumber");
    }
    callback();
  }
};

const formRules = reactive<FormRules<ReturnOrderDetailInputDTO>>({
  ExpressNumber: [{ validator: validateRequiredFields, trigger: "blur" }],
  ApplyReason: [{ validator: validateRequiredFields, trigger: "blur" }],
});

const handleSubmit = async (): Promise<ReturnOrderDetailInputDTO | null> => {
  if (!deviceReturnFormRef.value) return null;
  try {
    const valid = await deviceReturnFormRef.value.validate();
    if (valid) {
      if (backDeviceQuery.value.ExpressNumber) {
        const isSuccess = await handleCheckExpressNumber();
        if (!isSuccess) return null;
      }
      return backDeviceQuery.value;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
};

const handleCheckExpressNumber = async (): Promise<boolean> => {
  const res = await Order_Api.getExpressCompany({
    nums: backDeviceQuery.value.ExpressNumber,
  });
  if (res.Type === 200) {
    if (!res.Data[backDeviceQuery.value.ExpressNumber]) {
      ElMessage.warning(`未存在${backDeviceQuery.value.ExpressNumber}的快递单号`);
      return false;
    }
    const obj = res.Data[backDeviceQuery.value.ExpressNumber];
    backDeviceQuery.value.ExpressName = obj.Name;
    backDeviceQuery.value.ExpressCode = obj.ComCode;
    return true;
  } else {
    return false;
  }
};

const handleProcessingData = (info: DeviceManageItem) => {
  patUserInfo = Object.assign(patUserInfo, info.PatUser);
  const orderDetails = info.DeviceOrderInfo.OrderDetails.filter((x) => {
    return x.RelationId === info.DeviceId;
  });
  orderDetailId.value = orderDetails.length > 0 ? orderDetails[0].Id : "";
  backDeviceQuery.value.OrderDetailId = orderDetailId.value;
  handleGetReturnDevices();
};

const handleGetReturnDevices = async () => {
  const res = await Bff_Api.getByCode({
    code: "DeviceReturningInfo",
  });
  if (res.Type === 200) {
    const obj = res.Data.filter((v) => v.Code === "DeviceReturningInfo")[0];
    backUserInfo.value = obj;
    backDeviceQuery.value.Phone = obj.Payload[1].Value;
  }
};

interface Props {
  detailInfo: DeviceManageItem | null;
}
const props = defineProps<Props>();

watch(
  () => props.detailInfo,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-input .el-input__inner),
:deep(.el-textarea .el-textarea__inner) {
  width: 100%;
}
</style>
