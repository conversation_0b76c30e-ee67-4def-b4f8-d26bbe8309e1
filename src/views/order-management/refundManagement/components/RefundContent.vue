<template>
  <div
    class="p-10px bg-gray-100 dark:bg-dark-900 text-neutral-800 dark:text-neutral-200 space-y-3 overflow-y-auto max-h-600px overflow-y-auto"
  >
    <div class="flex items-center gap-x-4 p-4 bg-white dark:bg-dark-800 rounded-lg shadow-md">
      <FormItem label="订单编号" required style="margin-bottom: 0">
        <el-input v-model="params.orderNo" placeholder="请输入订单编号" />
      </FormItem>
      <el-button type="primary" :loading="queryLoading" @click="handleQuery">查询</el-button>
    </div>
    <div v-if="!pageError.isError && isLoadPage" v-loading="pageLoading">
      <div class="space-y-3">
        <div class="p-3 bg-white dark:bg-dark-800 rounded-lg shadow-md">
          <FormItem label="订单编号:">
            {{ info.OrderNo }}
          </FormItem>
          <FormItem label="订单金额:">
            <span>￥{{ info.Price }}</span>
            <span v-if="onGetLayering()">(含押金￥{{ onGetLayering() }})</span>
          </FormItem>
          <FormItem label="支付方式:">
            {{ payList[info.PayType] || "未查询到数据" }}
          </FormItem>
          <FormItem label="患者姓名:">
            {{ info.UserName || "未查询到数据" }}
          </FormItem>
          <template v-if="info.OrderExtend">
            <FormItem label="医院:">
              {{ info.OrderExtend.OrgName || "未查询到数据" }}
            </FormItem>
            <br />
            <FormItem label="下达人:">
              {{ info.OrderExtend.CreatorName || "未查询到数据" }}
            </FormItem>
          </template>
          <FormItem label="订单状态:">
            {{ ["待支付", "已支付/待发货", "已发货/待收货", "已完成", "已取消"][info.Status] }}
          </FormItem>
          <FormItem label="订单创建时间:">
            {{
              info.CreatedTime
                ? dayjs(info.CreatedTime).format("YYYY-MM-DD HH:mm:ss")
                : "未查询到数据"
            }}
          </FormItem>
        </div>
        <div class="p-3 bg-white dark:bg-dark-800 rounded-lg shadow-md">
          <FormItem label="退款原因:" class="flex" required style="align-items: flex-start">
            <div class="flex-1">
              <el-checkbox-group v-model="info.RemarkTags">
                <el-checkbox v-for="o in remarkTags" :key="o.Id" :label="o.Key">
                  {{ o.Key }}
                </el-checkbox>
              </el-checkbox-group>
              <el-input
                v-model="info.Remark"
                type="textarea"
                :rows="2"
                placeholder="请输入备注"
                class="w-full sm:w-4/5 mt-2.5"
              />
            </div>
          </FormItem>
        </div>
        <div class="p-3 bg-white dark:bg-dark-800 rounded-lg shadow-md">
          <template v-if="treatmentList.length || communityList.length">
            <span class="block text-lg font-semibold text-neutral-900 dark:text-neutral-100">
              退款项目
            </span>
            <template v-if="treatmentList.length">
              <div
                v-for="o in treatmentList"
                :key="o.Id"
                class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 last:border-b-0"
              >
                <div>
                  <p>{{ o.Title }}(￥{{ o.TotalPrice }})</p>
                  <p>剩余可退金额：{{ o.TreatmentCostAmount }}</p>
                </div>
                <div>
                  <span>退款金额：</span>
                  <el-input-number
                    v-model="o.RefundAmount"
                    :disabled="o.TreatmentCostAmount == 0"
                    :min="0"
                    :max="o.TreatmentCostAmount"
                    :precision="2"
                    :step="0.01"
                    class="w-120px"
                  />
                </div>
              </div>
            </template>
            <template v-if="communityList.length">
              <div
                v-for="o in communityList"
                :key="o.BaseMoItemId"
                class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 last:border-b-0"
              >
                <div>
                  <p>{{ o.BaseMoItemName }}(￥{{ o.Price * o.TotalCount }})</p>
                  <p>
                    共{{ o.TotalCount }}次，已执行{{ o.ExecuteCount }}次, 已退款{{
                      o.RefundCount
                    }}次，剩余{{ onGetHaveRefundCount(o) }}次
                  </p>
                  <p>剩余可退金额：{{ onGetCommunityCanRefundAmount(o) }}</p>
                </div>
                <div>
                  <span>退款次数：</span>
                  <el-input-number
                    v-model="o.Count"
                    :min="0"
                    :disabled="onGetHaveRefundCount(o) == 0"
                    :precision="0"
                    :step="1"
                    :max="onGetHaveRefundCount(o)"
                    class="w-120px"
                  />
                  <br />
                  <span>退款金额：{{ onGetCommunityRefundAmount(o) }}</span>
                </div>
              </div>
            </template>
          </template>
        </div>
      </div>
    </div>
    <div v-else-if="pageError.isError" class="text-red-500">{{ pageError.errorMsg }}</div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useUserStore } from "@/store";
const userStore = useUserStore();
import Order_Api from "@/api/order";
import { OrderDetailItem, OrderItem, RefundOrderInputDTO } from "@/api/order/types";
import { PageError, PageOrderDetailItem } from "./types";
import { getDictListData } from "@/utils/dict";
import Decimal from "decimal.js";
import Community_Api from "@/api/community";
import { CommunityMoItem, RefundExecInputDTO } from "@/api/community/types";

const info = ref<OrderItem>({
  RemarkTags: [],
} as unknown as OrderItem);

const params = ref<{
  orderNo: string;
}>({
  orderNo: "",
});

const queryLoading = ref<boolean>(false);
const pageLoading = ref<boolean>(false);
const isLoadPage = ref<boolean>(false);
const pageError = ref<PageError>({ isError: false, errorMsg: "" });
const payList = ["微信", "支付宝", "免费", "其他"];
const remarkTags = ref<ReadDict[]>([]);
const treatmentList = ref<PageOrderDetailItem[]>([]);
const communityList = ref<CommunityMoItem[]>([]);

const handleQuery = async () => {
  queryLoading.value = true;
  try {
    // 获取订单数据
    await onGetOrderData();
  } catch (error) {
    pageError.value.isError = true;
    pageError.value.errorMsg = "获取订单数据失败";
  } finally {
    pageLoading.value = false;
    queryLoading.value = false;
    isLoadPage.value = true;
  }
};

const onGetOrderData = async () => {
  pageLoading.value = true;
  const res = await Order_Api.getOrdersByOrderNo(params.value);
  if (res.Type === 200) {
    if (!res.Data) {
      pageError.value.isError = true;
      pageError.value.errorMsg = "未找到相关订单信息";
      return;
    }
    // 处理业务数据并且获取其他的借口数据
    onProcessOrderData(res.Data);
  }
};

const onProcessOrderData = (data: OrderItem) => {
  if (data.Status > 3 || data.Status < 1) {
    pageError.value.isError = true;
    pageError.value.errorMsg = "订单状态不支持退款";
    return;
  }
  info.value = { ...info.value, ...data };
  // 计算可退金额
  onCalculationAmount();
  // 获取线下数据
  onGetCommunityList();
};

const onCalculationAmount = () => {
  const treatmentFilterList = info.value.OrderDetails.filter((v) => v.GoodsType === "Treatment");
  const newTreatmentList = treatmentFilterList.map((v): PageOrderDetailItem => {
    return {
      ...v,
      TreatmentCostAmount: onGetRefundableAmount(v),
      RefundAmount: 0,
    };
  });
  treatmentList.value = newTreatmentList;
};

const onGetRefundableAmount = (v: OrderDetailItem): number => {
  // 计算已经退款的金额
  let sunRefund = 0;

  if (v.OrderAftersales && v.OrderAftersales.length > 0) {
    v.OrderAftersales.forEach((element) => {
      if (element.Status === 3) {
        sunRefund += element.Price * 1;
      }
    });
  }
  // 使用Decimal高精度计算
  const refundableAmount = new Decimal(v.TotalPrice).minus(sunRefund).toNumber();
  return refundableAmount;
};
const onGetLayering = () => {
  if (info.value.OrderDetails) {
    const LayeringList = info.value.OrderDetails.filter((s) => s.GoodsType === "DeviceBond");
    const Layering = LayeringList.reduce((total, obj) => total + obj.TotalPrice, 0);
    return Layering;
  }
};
const onGetCommunityList = async () => {
  const res = await Community_Api.getMoItemByPrescriptionId({
    orderNo: info.value.OrderNo,
  });
  if (res.Type === 200 && res.Data && res.Data.length) {
    const ids = res.Data.map((v) => v.BaseMoItemId);
    const result = treatmentList.value.filter((item) => !ids.includes(item.RelationId));
    treatmentList.value = result;
    res.Data.forEach((v) => {
      v.Count = 0;
    });
    communityList.value = res.Data;
  }
};

const onGetHaveRefundCount = (v: CommunityMoItem): number => {
  // 使用Decimal高精度计算
  const refundableAmount = new Decimal(v.TotalCount)
    .minus(v.ExecuteCount)
    .minus(v.RefundCount)
    .toNumber();
  return refundableAmount;
};

const onGetCommunityCanRefundAmount = (v: CommunityMoItem): number => {
  // 使用Decimal高精度计算
  return new Decimal(v.TotalCount)
    .minus(v.ExecuteCount)
    .minus(v.RefundCount)
    .times(v.Price)
    .toNumber();
};

const onGetCommunityRefundAmount = (v: CommunityMoItem): number => {
  return new Decimal(v.Price).times(v.Count).toNumber();
};
const handleSubmit = (): {
  refundData: RefundOrderInputDTO[];
  communityData: RefundExecInputDTO[];
} | null => {
  // 检查是否含有错误数据（检查是否可以点击提交）
  if (onCheckIsError()) {
    return null;
  }
  let refundData: RefundOrderInputDTO[] = [];
  let communityData: RefundExecInputDTO[] = [];
  treatmentList.value.length &&
    treatmentList.value
      .filter((s) => s.RefundAmount! > 0)
      .forEach((m) => {
        refundData.push({
          OrderDetailId: m.Id,
          Price: m.RefundAmount!,
          Reason: JSON.stringify({
            Tags: info.value.RemarkTags,
            Remark: info.value.Remark || "",
          }),
        });
      });
  communityList.value.length &&
    communityList.value
      .filter((v) => v.Count > 0)
      .forEach((s) => {
        const index = info.value.OrderDetails.findIndex((v) => v.RelationId === s.BaseMoItemId);
        const obj = {
          RefundCount: s.Count, //退款次数
          Id: s.StrId, //接口GetMoItemByPrescriptionId的返回数据ID
          UserId: userStore.userInfo.Id, //操作人
          OrderDetailId: info.value.OrderDetails[index].Id,
          Reason: JSON.stringify({
            Tags: info.value.RemarkTags,
            Remark: info.value.Remark || "",
          }),
        };
        communityData.push(obj);
      });
  return {
    refundData,
    communityData,
  };
};

const onCheckIsError = (): boolean => {
  // 治疗退款和社区退款至少填写一个
  const refundData = treatmentList.value.filter((v) => v.RefundAmount! > 0);
  const communityData = communityList.value.filter((v) => v.Count > 0);
  if (!refundData.length && !communityData.length) {
    ElMessage.warning("请输入退款金额或者退款次数！");
    return true;
  }
  const wainData = treatmentList.value.filter((v) => v.RefundAmount! > v.TreatmentCostAmount!);
  if (wainData.length) {
    ElMessage.warning("退款金额不能大于可退金额！");
    return true;
  }
  if (!info.value.Remark && !info.value.RemarkTags!.length) {
    ElMessage.warning("请输入退款原因！或者选择退款原因标签");
    return true;
  }
  return false;
};

const onGetRefundReasonList = async () => {
  const list = await getDictListData("RefundReason", {
    PageSize: 9999,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
    SortConditions: [{ SortField: "OrderNumber", ListSortDirection: 0 }],
  });
  remarkTags.value = list;
};

onBeforeMount(() => {
  // 获取退款原因列表数据
  onGetRefundReasonList();
});
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
p {
  margin: 10px 0 !important;
}
</style>
