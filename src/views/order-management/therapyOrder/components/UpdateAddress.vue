<template>
  <div style="padding: 20px">
    <el-form ref="formRef" :model="formData" :inline="true" label-width="120px">
      <el-form-item
        label="收货人姓名:"
        prop="Name"
        required
        :rules="[{ required: true, message: '请输入收货人姓名', trigger: 'blur' }]"
      >
        <el-input v-model="formData.Name" type="text" />
      </el-form-item>
      <el-form-item
        label="收货人手机号:"
        prop="Tel"
        :rules="[
          { required: true, message: '请输入收货人手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
        ]"
      >
        <el-input v-model="formData.Tel" type="text" />
      </el-form-item>
      <el-form-item
        label="地区:"
        prop="cityStr"
        :rules="[{ required: true, message: '请选择地区', trigger: 'blur' }]"
      >
        <city-picker
          v-model="formData.cityStr"
          :is-only-city="false"
          style="width: 250px"
          @getSelectCityInfo="handleCityChange"
        />
      </el-form-item>
      <br />
      <el-form-item
        label="详细地址:"
        prop="Address"
        :rules="[{ required: true, message: '请输入详细地址', trigger: 'blur' }]"
      >
        <el-input
          v-model="formData.Address"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 5 }"
          style="width: 200px"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { OrderAddressInputDTO } from "@/api/order/types";
import { FormInstance } from "element-plus";
interface PageOrderAddress extends OrderAddress {
  cityStr: string;
}
const formRef = ref<FormInstance>();
const formData = ref<PageOrderAddress>({
  Name: "",
  Tel: "",
  ProvinceName: "",
  CityName: "",
  CountyName: "",
  Address: "",
  Province: "",
  City: "",
  County: "",
  cityStr: "",
});
const handleCityChange = (value: { name: string; code: string }[]) => {
  console.log(value);
  formData.value.Province = value[0].code;
  formData.value.City = value[1].code;
  formData.value.County = value[2] ? value[2].code : "";
  formData.value.ProvinceName = value[0].name;
  formData.value.CityName = value[1].name;
  formData.value.CountyName = value[2] ? value[2].name : "";
};
const handleProcessing = (addressInfo: OrderAddress) => {
  Object.assign(formData.value, addressInfo);
  let addStr: string[] = [];
  if (!addressInfo) {
    addStr = [];
  } else {
    addStr = [addressInfo.Province!, addressInfo.City!];
    if (addressInfo.County) {
      addStr.push(addressInfo.County!);
    }
  }
  formData.value.cityStr = JSON.stringify(addStr);
};

const handleSubmit = async (): Promise<OrderAddressInputDTO | null> => {
  if (!formRef.value) return null;
  try {
    await formRef.value.validate();
    const copyData = JSON.parse(JSON.stringify(formData.value));
    delete copyData.cityStr;
    copyData.OrderNo = props.treatOrderNo;
    const params: OrderAddressInputDTO = {
      OrderNo: props.treatOrderNo,
      Name: copyData.Name,
      Tel: copyData.Tel,
      ProvinceName: copyData.ProvinceName,
      CityName: copyData.CityName,
      CountyName: copyData.CountyName,
      Address: copyData.Address,
      Province: copyData.Province,
      City: copyData.City,
      County: copyData.County,
    };
    return params;
  } catch (error) {
    return null;
  }
};

interface Props {
  addressInfo: OrderAddress[];
  treatOrderNo: string;
}
const props = defineProps<Props>();
watch(
  () => props.addressInfo,
  (newVal) => {
    handleProcessing(newVal[0]);
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
