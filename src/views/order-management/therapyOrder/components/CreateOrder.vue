<template>
  <div class="service-content">
    <div class="flex items-center mb-3">
      <span class="whitespace-nowrap">方案编号:</span>
      <el-input v-model="prescriptionId" placeholder="治疗方案编号" clearable class="mx-2 w-44" />
      <el-button type="primary" class="h-7 mr-2" :loading="loading" @click="handleSearch">
        查询
      </el-button>
      <span v-if="showError" class="text-red-500 text-sm">该订单状态并不是待执行的状态</span>
    </div>

    <div v-if="showInfo" v-loading="loading" class="mt-3">
      <div class="mb-2">方案编号:{{ dataObj.Id }}</div>
      <br />
      <div class="mb-2">方案下达人:{{ dataObj.OrganizationName + " " + dataObj.DoctorName }}</div>
      <div class="mb-3">
        <p class="mb-1">患者信息:</p>
        <p class="mb-1">{{ dataObj.UserName + " " + dataObj.Sex + "  " + dataObj.Age }}</p>
        <p>诊断:{{ dataObj.Disposal }}</p>
      </div>
    </div>

    <div v-for="item in prescriptionList" :key="item.Id" class="mb-3">
      <div class="flex justify-between items-baseline">
        <div>
          <div>
            <div v-if="item.MoItemChargeMode == 1" class="font-semibold text-sm">
              {{ item.MoName }} {{ "(部位" + item.Part + ")" }}
            </div>
            <div v-if="item.MoItemChargeMode != 1" class="font-semibold text-sm">
              {{ item.MoName }}
            </div>
          </div>
          <div>
            <div v-if="item.MoItemChargeMode === 3" class="text-sm text-gray-500 mt-2">
              {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.MoDay }}天
            </div>
            <div v-if="item.MoItemChargeMode === 5" class="text-sm text-gray-500 mt-2">
              {{ item.MoMonth }}个月
            </div>
            <div
              v-if="item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2"
              class="text-sm text-gray-500 mt-2"
            >
              {{ item.FreqDay }}天{{ item.Freq }}次,共{{ item.TotalCount }}次
            </div>
          </div>
        </div>
        <div class="text-right">
          <span v-if="item.MoItemChargeMode === 1" class="text-sm">
            <span>
              ￥{{
                item.Price * item.Part > 0
                  ? (item.Price * item.Part).toFixed(2)
                  : item.Price * item.Part
              }}
            </span>
            <span v-if="item.ShowPrice > item.Price" class="line-through text-gray-400 ml-1">
              ￥{{
                item.ShowPrice * item.Part > 0
                  ? (item.ShowPrice * item.Part).toFixed(2)
                  : item.ShowPrice * item.Part
              }}
            </span>
          </span>
          <span v-if="item.MoItemChargeMode != 1" class="text-sm">
            <span>￥{{ item.Price > 0 ? item.Price.toFixed(2) : item.Price }}</span>
            <span v-if="item.ShowPrice > item.Price" class="line-through text-gray-400 ml-1">
              ￥{{ item.ShowPrice > 0 ? item.ShowPrice.toFixed(2) : item.ShowPrice }}
            </span>
          </span>
          <p v-if="item.MoItemChargeMode === 4" class="text-gray-500 text-sm mt-1">x1</p>
          <p v-if="item.MoItemChargeMode === 3" class="text-gray-500 text-sm mt-1">
            x{{ item.MoDay }}
          </p>
          <p
            v-if="item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2"
            class="text-gray-500 text-sm mt-1"
          >
            x{{ item.TotalCount }}
          </p>
          <p v-if="item.MoItemChargeMode === 5" class="text-gray-500 text-sm mt-1">
            x{{ item.MoMonth }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";
import { PrescriptionDetail } from "@/api/consult/types";

const prescriptionId = ref<string>("");
const showError = ref<boolean>(false);
const showInfo = ref<boolean>(false);
const loading = ref<boolean>(false);
const params = ref({
  PayCompany: "kangfx",
  TreatPayCompany: "kangfx",
  PrescriptionId: "",
  IsChecked: false,
});
let prescriptionList: any[] = [];
let dataObj = reactive<any>({});
const handleSearch = () => {
  if (!prescriptionId.value) {
    ElMessage.warning("请输入处方编号");
    return;
  }
  loading.value = true;
  Consult_Api.getPrescriptionInfo({ PrescriptionId: prescriptionId.value })
    .then((res) => {
      console.log(res);
      if (res.Type !== 200 || res.Data.State !== 1) {
        showError.value = true;
        showInfo.value = false;
        prescriptionList = [];
        dataObj = {};
        params.value.PrescriptionId = "";
      } else {
        showError.value = false;
        showInfo.value = true;
        handleInitResData(res.Data);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const handleInitResData = (data: PrescriptionDetail) => {
  data.PrescriptionDetails.map((v, index) => {
    if (v.CreatorId === data.UserId) {
      data.PrescriptionDetails.splice(index, 1);
    }
  });
  prescriptionList = data.PrescriptionDetails;
  dataObj = data;
  params.value.PrescriptionId = data.Id;
};

const handleSubmit = () => {
  return params.value;
};
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
.service-content {
  padding: 12px;
  height: 500px;
  overflow-y: auto;
  width: 100%;
  padding-top: 0 !important;
}
</style>
