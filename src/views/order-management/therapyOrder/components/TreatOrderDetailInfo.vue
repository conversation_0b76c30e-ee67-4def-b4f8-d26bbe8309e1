<template>
  <div class="order-detail">
    <el-card class="mb-2">
      <div class="flex items-center justify-between mb-2">
        <div class="text-lg font-medium">方案下达人</div>
        <div class="text-gray-600">
          <span class="mr-4">{{ formData.CreatorName }}</span>
          <span>{{ formData.OrganizationName }}</span>
        </div>
      </div>
    </el-card>

    <el-card class="mb-2">
      <div class="text-lg font-medium mb-2">患者信息</div>
      <div class="flex items-center mb-2">
        <span class="text-gray-600 mr-4">{{ formData.PatUser.Name }}</span>
        <span class="text-gray-600 mr-4">{{ formData.PatUser.Sex }}</span>
        <span class="text-gray-600">{{ handleGetAge() }}</span>
      </div>
      <div class="text-gray-600">诊断：{{ handleGetDiagnosis() }}</div>
    </el-card>

    <el-card v-if="formData.OrderAddresss.length" class="mb-2">
      <div class="text-lg font-medium mb-2">收货地址</div>
      <div class="grid grid-cols-2 gap-2">
        <div class="text-gray-600">
          <span class="font-medium">收货人：</span>
          <span>{{ formData.OrderAddresss[0].Name }}</span>
        </div>
        <div class="text-gray-600">
          <span class="font-medium">联系电话：</span>
          <span>{{ formData.OrderAddresss[0].Tel }}</span>
        </div>
        <div class="text-gray-600 col-span-2">
          <span class="font-medium">收货地址：</span>
          <span>
            {{
              formData.OrderAddresss?.[0]?.ProvinceName! +
              formData.OrderAddresss?.[0]?.CityName! +
              formData.OrderAddresss?.[0]?.CountyName! +
              formData.OrderAddresss?.[0]?.Address!
            }}
          </span>
        </div>
      </div>
    </el-card>

    <el-card class="mb-4">
      <div class="text-lg font-medium mb-4 flex justify-between items-center">
        <span>项目治疗费用</span>
        <span class="text-primary text-xl">¥{{ formData.TotalAmount }}</span>
      </div>
      <div
        v-for="(item, index) in formData.TreatOrderMoOutputDtos"
        :key="index"
        class="mb-4 last:mb-0"
      >
        <div class="flex justify-between items-center mb-2">
          <span class="text-gray-800">{{ item.MoName }}</span>
          <div>
            <span v-if="item.Part > 1 && item.MoItemChargeMode === 1" class="text-gray-600 mr-2">
              （部位{{ item.Part }}）
            </span>
            <span class="text-primary">¥{{ handleGetPrice(item) }}</span>
          </div>
        </div>
        <div class="flex justify-between text-gray-600 text-sm">
          <span>{{ handleGetDescInfo(item) }}</span>
          <span>
            <em v-if="item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2">
              ×{{ item.TotalCount }}
            </em>
            <em v-if="item.MoItemChargeMode === 3">×{{ item.MoDay }}</em>
            <em v-if="item.MoItemChargeMode === 4">×1</em>
            <em v-if="item.MoItemChargeMode === 5">×{{ item.MoMonth }}</em>
          </span>
        </div>
      </div>
    </el-card>

    <el-card v-if="formData.RentDataMoney" class="mb-4">
      <div class="text-lg font-medium mb-4 flex justify-between items-center">
        <span>设备使用保证金</span>
        <span class="text-primary text-xl">¥{{ formData.RentDataMoney }}</span>
      </div>

      <div v-for="(item, index) in formData.RentDataInfos" :key="index" class="mb-4 last:mb-0">
        <div class="flex justify-between items-center mb-2">
          <span class="text-gray-800">{{ item.RentDataName }}</span>
          <div>
            <span class="text-primary">¥{{ item.RentDataAmount }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <el-card>
      <div class="text-lg font-medium mb-4">订单信息</div>
      <ul class="grid grid-cols-2 gap-4">
        <li class="text-gray-600">
          订单金额：
          <span class="text-primary">¥{{ formData.TotalAmount }}</span>
        </li>
        <li class="text-gray-600">订单编号：{{ formData.TreatOrder.OrderNo }}</li>
        <li class="text-gray-600">创建时间：{{ handleDateFormat(formData.CreatedTime) }}</li>
        <li v-if="formData.PayTime" class="text-gray-600">
          支付时间：{{ handleDateFormat(formData.PayTime) }}
        </li>
        <li class="text-gray-600">支付方式：{{ handleGetPayType() }}</li>
        <li v-if="formData.OrderGift" class="text-gray-600 col-span-2">
          备注：{{ handleGetOrderGift("Mark", formData.OrderGift) }}
        </li>
        <li v-if="formData.OrderGift" class="text-gray-600 col-span-2">
          赠品：{{ handleGetOrderGift("Gift", formData.OrderGift) }}
        </li>
        <li
          v-if="(formData.TreatOrder && formData.TreatOrder.RefundPrice) || 0 > 0"
          class="text-gray-600 col-span-2"
        >
          退款金额：
          <span class="text-primary">￥{{ formData.TreatOrder.RefundPrice }}</span>
          <el-button type="primary" link @click="handleLookDetail(formData.TreatOrderNo)">
            查看详情
          </el-button>
        </li>
        <li v-if="formData.DeliveryTime" class="text-gray-600">
          发货时间：{{ handleDateFormat(formData.DeliveryTime) }}
        </li>
        <li v-if="formData.CollectTime" class="text-gray-600">
          收货时间：{{ handleDateFormat(formData.CollectTime) }}
        </li>
        <li v-if="formData.CancelTime" class="text-gray-600">
          取消时间：{{ handleDateFormat(formData.CancelTime) }}
        </li>
      </ul>
    </el-card>

    <el-dialog v-model="dialogVisible.aftersale" title="退款详情" width="30%" destroy-on-close>
      <div class="h-[400px] overflow-y-auto">
        <el-timeline>
          <el-timeline-item
            v-for="(s, index) in aftersaleList"
            :key="index"
            :timestamp="s.RefundTime"
            placement="top"
          >
            <el-card>
              <p>退款金额：￥{{ s.RefundPrice }}</p>
              <p>操作人：{{ s.RefundOptUserName }}</p>
              <p>备注：{{ s.Reason }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.aftersale = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { TreatOrderDetail } from "@/api/consult/types";
import Order_Api from "@/api/order";
import { AftersaleRefund } from "@/api/order/types";
import { PayTypeEnum } from "@/enums/OrderEnum";
import dayjs from "dayjs";

const formData = ref<TreatOrderDetail>({} as TreatOrderDetail);
const dialogVisible = ref({
  aftersale: false,
});
const aftersaleList = ref<AftersaleRefund[]>([]);
const payType = [
  {
    label: "微信",
    value: PayTypeEnum.WeChat,
  },
  {
    label: "支付宝",
    value: PayTypeEnum.Alipay,
  },
  {
    label: "免费",
    value: PayTypeEnum.Free,
  },
  {
    label: "其他",
    value: PayTypeEnum.Other,
  },
];
const handleLookDetail = async (orderNo: string) => {
  const params = {
    OrderType: "Treatment",
    OrderNo: orderNo,
    PageIndex: 1,
    PageSize: 9999,
  };
  const res = await Order_Api.getOrderAftersaleRefundsList(params);
  if (res.Type === 200) {
    res.Data.Data.forEach((v) => {
      v.RefundTime = dayjs(v.RefundTime).format("YYYY-MM-DD HH:mm:ss");
    });
    aftersaleList.value = res.Data.Data;
    dialogVisible.value.aftersale = true;
  } else {
    ElMessage.error(res.Content);
  }
};
const handleGetOrderGift = (type: string, item?: any) => {
  if (!item) return "";
  return item[type];
};

const handleGetAge = (): string => {
  if (!formData.value?.PatUser.Birthday) {
    return "无患者年龄";
  }
  const newBar = dayjs(formData.value?.PatUser.Birthday).format("YYYY-MM-DD");
  const today = new Date();
  const birthDateObj = new Date(newBar);
  const age = today.getFullYear() - birthDateObj.getFullYear();
  const monthDiff = today.getMonth() - birthDateObj.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
    return age - 1 + "岁";
  }
  return age + "岁";
};

const handleGetPrice = (item: any) => {
  var price = item.Price;
  if (item.MoItemChargeMode === 1) {
    price = item.Price * item.Part;
  }
  return price;
};

const handleGetPayType = () => {
  return payType.find((v) => v.value === formData.value?.PayType)?.label;
};

const handleDateFormat = (date?: string) => {
  if (!date) return "";
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
};
const handleGetDescInfo = (item: any) => {
  var desc = "";
  if (item.MoItemChargeMode === 3) {
    desc = `${item.FreqDay}天${item.Freq}次，共${item.MoDay}天`;
  } else if (item.MoItemChargeMode === 1 || item.MoItemChargeMode === 2) {
    desc = `${item.FreqDay}天${item.Freq}次，共${item.TotalCount}次`;
  } else if (item.MoItemChargeMode === 5) {
    desc = `${item.MoMonth}个月`;
  }
  return desc;
};
const handleGetDiagnosis = (): string => {
  const icdArr = formData.value?.ICD;
  if (icdArr) {
    if (Array.isArray(icdArr)) {
      return icdArr.map((v) => v.DiagnoseName).join("、");
    } else {
      return icdArr;
    }
  }
  return "";
};

const handleProcessingData = (data: TreatOrderDetail) => {
  formData.value = data;
};

interface Props {
  treatOrderDetail: TreatOrderDetail | null;
}
const props = defineProps<Props>();
watch(
  () => props.treatOrderDetail,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.order-detail {
  height: 600px;
  overflow-y: auto;
  :deep(.el-card__body) {
    padding: 20px;
  }
}

.text-primary {
  color: var(--el-color-primary);
}
</style>
