<template>
  <div class="send-express-form">
    <el-form ref="formRef" :model="formData" label-width="120px" class="express-form">
      <el-form-item label="收货人：" prop="receiver">
        <div class="receiver-info">{{ formData.Name + " " + formData.Tel }}</div>
      </el-form-item>
      <el-form-item label="收货地址：" prop="address">
        <div class="address-info">
          {{
            (formData.ProvinceName ?? "") +
            (formData.CityName ?? "") +
            (formData.CountyName ?? "") +
            (formData.Address ?? "")
          }}
        </div>
      </el-form-item>

      <!-- 快递信息组 -->
      <div
        v-for="(item, index) in expressInfos"
        :key="index"
        :ref="
          (el) => {
            if (el) expressGroups[index] = el;
          }
        "
        class="express-group"
      >
        <div class="group-header">
          <span class="group-title">快递信息 #{{ index + 1 }}</span>
          <el-button
            v-if="expressInfos.length > 1"
            type="danger"
            link
            @click="removeExpressInfo(index)"
          >
            删除
          </el-button>
        </div>

        <el-form-item
          :label="index === 0 ? '快递单号：' : ''"
          :prop="'expressInfos.' + index + '.ExpressNum'"
          :rules="[
            {
              validator: (rule, value, callback) =>
                validateAtLeastOne(rule, value, callback, index),
              trigger: 'change',
            },
          ]"
        >
          <el-input
            v-model="item.ExpressNum"
            placeholder="请输入快递单号"
            clearable
            @change="() => handleInputChange(index)"
          />
        </el-form-item>

        <el-form-item
          :label="index === 0 ? '发货备注：' : ''"
          :prop="'expressInfos.' + index + '.Remark'"
          :rules="[
            {
              validator: (rule, value, callback) =>
                validateAtLeastOne(rule, value, callback, index),
              trigger: 'change',
            },
          ]"
        >
          <el-input
            v-model="item.Remark"
            type="textarea"
            :rows="2"
            placeholder="请输入发货备注"
            @change="() => handleInputChange(index)"
          />
        </el-form-item>
      </div>

      <!-- 添加按钮 -->
      <div class="add-button-wrapper">
        <el-button type="primary" link @click="addExpressInfo">
          <el-icon><Plus /></el-icon>
          添加快递信息
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from "vue";
import { Plus } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import Order_Api from "@/api/order";
import { SendExpressInputDTO } from "@/api/order/types";

const formRef = ref<FormInstance>();
const expressGroups = ref<(Element | ComponentPublicInstance)[]>([]);

// 分离基本信息和快递信息
const formData = ref<OrderAddress>({
  Name: "",
  Tel: "",
  ProvinceName: "",
  CityName: "",
  CountyName: "",
  Address: "",
  Province: "",
  City: "",
  County: "",
});

// 快递信息列表
let expressInfos = reactive<OrderExpress[]>([
  {
    ExpressNum: "",
    Remark: "",
    OrderNo: "",
    ExpressComCode: "",
    ExpressComName: "",
  },
]);

// 自定义验证规则：快递单号和发货备注至少填写一个
const validateAtLeastOne = (rule: any, value: string, callback: any, index: number) => {
  const currentGroup = expressInfos[index];
  if (!currentGroup.ExpressNum && !currentGroup.Remark) {
    callback(new Error("快递单号和发货备注至少填写一个"));
  } else {
    callback();
  }
};

// 添加新的快递信息组
const addExpressInfo = () => {
  expressInfos.push({
    ExpressNum: "",
    Remark: "",
    ExpressComCode: "",
    OrderNo: props.treatOrderNo,
    ExpressComName: "",
  });

  // 等待DOM更新后滚动到新添加的组
  nextTick(() => {
    const newGroupIndex = expressInfos.length - 1;
    const newGroup = expressGroups.value[newGroupIndex];
    if (newGroup && newGroup instanceof Element) {
      newGroup.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  });
};

// 删除快递信息组
const removeExpressInfo = (index: number) => {
  expressInfos.splice(index, 1);
};

// 输入框变化时触发验证
const handleInputChange = (index: number) => {
  formRef.value?.validateField([
    `expressInfos.${index}.ExpressNum`,
    `expressInfos.${index}.Remark`,
  ]);
};

const handleCheckExpressInfo = async (): Promise<OrderExpress[] | null> => {
  const nums = expressInfos.filter((item) => item.ExpressNum).map((item) => item.ExpressNum!);
  const numsStr = nums.join(",");
  if (numsStr) {
    // 获取快递信息
    const res = await Order_Api.getExpressCompany({ nums: numsStr });
    if (res.Type === 200) {
      // 在nums中 返回的对象中的key没有 则表示快递单号填写有问题 需要提示用户
      const notFoundNums = nums.filter((item) => !res.Data[item]);
      if (notFoundNums.length > 0) {
        ElNotification.warning(`未找到快递单号${notFoundNums.join(",")}`);
        return null;
      }
      let orderExpresses: OrderExpress[] = [];
      nums.forEach((v) => {
        orderExpresses.push({
          OrderNo: props.treatOrderNo,
          ExpressNum: v,
          ExpressComCode: res.Data[v] ? res.Data[v].ComCode : "",
          ExpressComName: res.Data[v] ? res.Data[v].Name : "",
          Remark: expressInfos.filter((s) => s.ExpressNum === v)[0].Remark,
        });
      });
      return orderExpresses;
    }
  }
  return null;
};
const handleSubmit = async (): Promise<SendExpressInputDTO | null> => {
  const nums = expressInfos.filter((item) => item.ExpressNum);
  let params: SendExpressInputDTO = {
    PrescriptionId: props.orderId,
    OrderExpresses: [],
  };
  if (nums.length) {
    const list = await handleCheckExpressInfo();
    if (list) {
      params.OrderExpresses = list;
    } else {
      return null;
    }
  } else {
    expressInfos.forEach((item) => {
      params.OrderExpresses.push({
        OrderNo: props.treatOrderNo,
        Remark: item.Remark,
      });
    });
  }
  return params;
};

interface Props {
  expressInfo: OrderExpress[];
  addressInfo: OrderAddress[];
  orderId: string;
  treatOrderNo: string;
}
const props = defineProps<Props>();
watch(
  () => [props.expressInfo, props.addressInfo],
  () => {
    Object.assign(expressInfos, props.expressInfo);
    Object.assign(formData.value, props.addressInfo[0]);
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style scoped lang="scss">
.send-express-form {
  padding: 20px;
  height: 400px;
  overflow-y: auto;

  .express-form {
    max-width: 600px;

    :deep(.el-form-item) {
      margin-bottom: 12px; // 减小表单项间距
    }
  }

  .receiver-info,
  .address-info {
    line-height: 32px;
    color: #606266;
  }

  .express-group {
    margin-bottom: 16px; // 减小组间距
    padding: 12px; // 减小内边距
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px; // 减小标题底部间距

      .group-title {
        font-size: 14px; // 稍微减小标题字号
        font-weight: bold;
        color: #606266;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    // 优化表单项在组内的间距
    :deep(.el-form-item) {
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .add-button-wrapper {
    margin-top: 12px;
    display: flex;
    justify-content: center;
  }
}
</style>
