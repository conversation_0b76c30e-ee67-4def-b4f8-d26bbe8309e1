<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="医院" prop="OrgId">
                <HospitalSelect
                  v-model="queryParams.OrgId"
                  @change="
                    () => {
                      queryParams.DeptId = undefined;
                      queryParams.UserId = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室" prop="DeptId">
                <DeptSelect
                  v-model="queryParams.DeptId"
                  :org-id="queryParams.OrgId"
                  :disabled="!queryParams.OrgId"
                />
              </el-form-item>
              <el-form-item label="用户" prop="UserId">
                <UserSelect
                  v-model="queryParams.UserId"
                  :disabled="!queryParams.OrgId"
                  :org-ids="queryParams.OrgId ? [queryParams.OrgId!] : null"
                  :dept-ids="queryParams.DeptId ? [queryParams.DeptId!] : null"
                  :scopeable="false"
                />
              </el-form-item>
              <el-form-item label="状态" prop="IsDeleted">
                <KSelect
                  v-model="queryParams.IsDeleted"
                  :data="[
                    { label: '使用中', value: false },
                    { label: '已删除', value: true },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="是否申领" prop="Application">
                <KSelect
                  v-model="queryParams.Application"
                  :data="[
                    { label: '已申领', value: true },
                    { label: '未申领', value: false },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          border
        >
          <el-table-column prop="Id" label="编号" width="150" align="center" />
          <el-table-column prop="OrgName" label="医院" width="150" align="center" />
          <el-table-column prop="DeptName" label="科室" width="100" align="center" />
          <el-table-column prop="UserName" label="用户" width="80" align="center" />
          <el-table-column
            prop="CreatedTime"
            label="生成时间"
            width="150"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column prop="CardImgs" label="台卡" align="center" min-width="120">
            <template #default="scope">
              <el-image
                v-for="(item, index) in scope.row.Images"
                :key="index"
                class="w-80px h-80px mx-10px"
                :src="item.Url"
                fit="contain"
                preview-teleported
                :initial-index="index"
                :preview-src-list="scope.row.Images.map((e: TableCardImage) => e.Url)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="DeletedTime" label="状态" width="80" align="center">
            <template #default="scope">
              {{ scope.row.DeletedTime ? "已删除" : "使用中" }}
            </template>
          </el-table-column>
          <el-table-column prop="CreatorName" label="创建人" width="80" align="center" />
          <el-table-column prop="Download" label="是否下载" width="80" align="center">
            <template #default="scope">
              {{ scope.row.Download ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column prop="Application" label="是否申领" width="80" align="center">
            <template #default="scope">
              {{ scope.row.Application ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column prop="CardSource" label="来源" width="80" align="center">
            <template #default="scope">
              {{ ["医生", "医助"][scope.row.CardSource] }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { GetCardsListParams } from "@/api/consult/types";
import Consult_Api from "@/api/consult";

const kEnableDebug = false;
defineOptions({
  name: "TableCardManagement",
});

interface ShowTableCardOrder extends TableCardDetail {
  Images?: TableCardImage[];
  ShowState?: string;
}

const { tableLoading, pageData, total, tableFluidHeight, tableResize, tableDateFormat } =
  useTableConfig<ShowTableCardOrder>();

const queryParams = reactive<GetCardsListParams>({
  PageSize: 10,
  PageIndex: 1,
});

// 点击搜索
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

// 请求表格数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Consult_Api.getCardsList(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  pageData.value = r.Data.Data.map((e) => {
    let images: TableCardImage[] = [];
    try {
      images = JSON.parse(e.CardImgs ?? "[]") ?? [];
    } catch (error) {
      kEnableDebug && console.warn(error);
    }
    return {
      ...e,
      Images: images,
    };
  });
  total.value = r.Data.TotalCount;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
