<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer is-show-toggle>
          <template #left>
            <el-form ref="queryFormRef" label-position="right" :model="queryParams" :inline="true">
              <el-form-item prop="isEnable" label="是否启用">
                <el-select
                  v-model="queryParams.isEnable"
                  clearable
                  placeholder="请选择"
                  :empty-values="[null, undefined]"
                  value-on-clear=""
                >
                  <el-option label="全部" value="" />
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item prop="moItemUseScope" label="使用范围">
                <el-select v-model="queryParams.moItemUseScope" placeholder="请选择" filterable>
                  <el-option label="全部" :value="0" />
                  <el-option label="居家" :value="AdviceMoItemUseScope.Home" />
                  <el-option label="线下" :value="AdviceMoItemUseScope.Community" />
                  <el-option label="院内" :value="AdviceMoItemUseScope.Hospital" />
                </el-select>
              </el-form-item>
              <el-form-item prop="catelog" label="康复分类">
                <el-select
                  v-model="queryParams.catelog"
                  clearable
                  placeholder="请选择"
                  filterable
                  :empty-values="[null, undefined]"
                  value-on-clear=""
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="item in recoveryList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Key!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="moItemMethod" label="下达方式">
                <el-select v-model="queryParams.moItemMethod" placeholder="请选择">
                  <el-option label="全部" :value="-1" />
                  <el-option label="普通" :value="AdviceMoItemMethod.General" />
                  <el-option label="穴位" :value="AdviceMoItemMethod.Acupoint" />
                  <el-option label="咨询" :value="AdviceMoItemMethod.Consultation" />
                  <el-option label="评定" :value="AdviceMoItemMethod.Evaluation" />
                  <el-option label="辅具" :value="AdviceMoItemMethod.Equipment" />
                  <el-option label="耗材" :value="AdviceMoItemMethod.Consumables" />
                  <el-option label="宣教" :value="AdviceMoItemMethod.Education" />
                </el-select>
              </el-form-item>
              <el-form-item prop="moType" label="医嘱类型">
                <el-select
                  v-model="queryParams.moType"
                  clearable
                  placeholder="请选择"
                  :value-on-clear="0"
                  :empty-values="[null, undefined]"
                >
                  <el-option label="全部" :value="0" />
                  <el-option
                    v-for="(item, index) in moItemTypeList"
                    :key="'DT' + index"
                    :label="item.Name"
                    :value="item.Value + ''"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="actionUnit" label="康复训练">
                <el-select
                  v-model="queryParams.actionUnit"
                  clearable
                  filterable
                  remote
                  :remote-method="handleSearchActionUnit"
                  :loading="actionLoading"
                  placeholder="输入关键字筛选"
                  value-on-clear=""
                  :empty-values="[null, undefined]"
                >
                  <el-option
                    v-for="(item, index) in actionUnitList"
                    :key="index"
                    :label="item.Name"
                    :value="item.ContentId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="tag" label="分组">
                <el-select
                  v-model="queryParams.tag"
                  filterable
                  placeholder="输入关键字搜索"
                  :empty-values="[null, undefined]"
                  value-on-clear=""
                  clearable
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="item in moItemGroupList"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="isDefaultPush" label="默认推送">
                <el-select
                  v-model="queryParams.isDefaultPush"
                  clearable
                  placeholder="请选择"
                  :empty-values="[null, undefined]"
                  value-on-clear=""
                >
                  <el-option label="全部" value="" />
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item prop="keywordMode" label="关键字类型">
                <el-select v-model="queryParams.keywordMode" placeholder="请选择">
                  <el-option label="模糊查询" :value="1" />
                  <el-option label="精确查询" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item prop="keywords" label="关键字">
                <el-input
                  v-model="queryParams.keywords"
                  :placeholder="queryParams.keywordMode === 1 ? '名称/编码/拼音码' : '名称'"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handlePushHospital">批量推送</el-button>
            <el-button type="primary" @click="handleAddAdvice">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          highlight-current-row
          row-key="Id"
          border
          :height="tableFluidHeight"
          style="text-align: center; flex: 1"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" width="55" align="center" reserve-selection />
          <el-table-column
            prop="Name"
            label="名称"
            :show-overflow-tooltip="true"
            align="center"
            width="180"
          />
          <el-table-column
            prop="AliasName"
            label="简称"
            :show-overflow-tooltip="true"
            align="center"
            width="100"
          />
          <el-table-column label="分组" width="180" align="center">
            <template #default="scope">
              {{ scope.row.Tags ? scope.row.Tags.join("，") : "" }}
            </template>
          </el-table-column>
          <el-table-column prop="MoItemUseScope" label="使用范围" align="center" width="120">
            <template #default="scope">
              {{ ["", "居家", "线下", "院内"][scope.row.MoItemUseScope] }}
            </template>
          </el-table-column>
          <el-table-column label="下达方式" align="center" width="120">
            <template #default="scope">
              {{
                ["普通医嘱", "穴位", "咨询", "评定", "辅具", "耗材", "宣教"][scope.row.MoItemMethod]
              }}
            </template>
          </el-table-column>
          <el-table-column prop="MoItemAmount" label="医嘱价格" width="120" align="center" />
          <el-table-column label="计费方式" width="120" align="center">
            <template #default="scope">
              {{ ["", "按部位", "按次", "按天", "按项目", "按月"][scope.row.ChargeMode] }}
            </template>
          </el-table-column>
          <el-table-column label="下达场景" width="120" align="center">
            <template #default="scope">
              {{ ["", "问诊/咨询", "患者管理", "问诊/咨询;患者管理"][scope.row.Scene] }}
            </template>
          </el-table-column>
          <el-table-column label="下达权限" width="120" align="center">
            <template #default="scope">
              <span v-if="scope.row.DoctorVisibility">医生</span>
              <span v-if="scope.row.TherapistVisibility">
                <br />
                治疗师
              </span>
              <span v-if="scope.row.TherapistByDoctorVisibility">
                <br />
                治疗师（需医生确认）
              </span>
              <span v-if="scope.row.NurseVisibility">
                <br />
                护士
              </span>
              <span v-if="scope.row.NurseByDoctorVisibility">
                <br />
                护士（需医生确认）
              </span>
            </template>
          </el-table-column>
          <el-table-column label="康复分类" width="120" align="center">
            <template #default="scope">
              {{ scope.row.Catelog ? scope.row.Catelog.join(",") : "" }}
            </template>
          </el-table-column>
          <el-table-column prop="MoType" label="医嘱类别" width="120" align="center">
            <template #default="scope">
              {{ moItemTypeList.find((item) => item.Value === scope.row.MoType)?.Name || "" }}
            </template>
          </el-table-column>
          <el-table-column label="是否启用" width="100" align="center">
            <template #default="scope">
              {{ scope.row.IsEnable ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column prop="CreatedTime" label="创建时间" width="150" align="center" />
          <el-table-column prop="IsDefaultPush" label="是否推送" width="120" align="center">
            <template #default="scope">
              {{ scope.row.IsDefaultPush ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button
                link
                size="small"
                type="primary"
                @click="handlePreviewOrEdit(scope.row, true)"
              >
                查看
              </el-button>
              <el-button
                link
                size="small"
                type="primary"
                @click="handlePreviewOrEdit(scope.row, false)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
  <el-dialog v-model="showDialog.push" title="请选择推送的机构" width="700px" destroy-on-close>
    <HospitalTransfer
      :loading="dialogConfirmLoading"
      @cancel="showDialog.push = false"
      @submit="handlePushHospitalSubmit"
    />
  </el-dialog>
  <el-dialog
    v-model="showDialog.adviceContent"
    :title="adviceContentTitle"
    width="800px"
    :close-on-click-modal="isPreview"
    destroy-on-close
  >
    <AdviceContent v-if="showDialog.adviceContent" ref="adviceContentRef" :info="info" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showDialog.adviceContent = false">取消</el-button>
        <el-button
          v-if="!isPreview"
          type="primary"
          :loading="dialogConfirmLoading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import { AdviceMoItemMethod, AdviceMoItemUseScope } from "@/enums/AdviceEnum";
import AdviceContent from "./components/AdviceContent.vue";
import { useTableConfig } from "@/hooks/useTableConfig";
import {
  getActionUnitList,
  getMoItemGroupList,
  getMoItemTypeList,
  getRecoveryTypeList,
} from "@/utils/dict";
import Content_Api from "@/api/content";
import {
  InsertOrUpdateMoItemInputDTO,
  MoItemPageData,
  MoItemPageDataInputDTO,
} from "@/api/content/types";
defineOptions({
  name: "AdviceManagement",
  inheritAttrs: false,
});
const { tableLoading, pageData, total, tableRef, selectedTableIds, tableFluidHeight, tableResize } =
  useTableConfig<MoItemPageData>();
export interface PageInsertOrUpdateMoItemParams
  extends Omit<InsertOrUpdateMoItemInputDTO, "Scene" | "Visibility"> {
  Scene: string[];
  Visibility: string[];
}
const queryParams = ref({
  page: 1,
  pageSize: 10,
  keywords: "",
  keywordMode: 1,
  isEnable: "",
  moItemUseScope: 0,
  catelog: "",
  moItemMethod: -1,
  moType: 0,
  tag: "",
  actionUnit: "",
  isDefaultPush: "",
});
const recoveryList = ref<ReadDict[]>([]);
const moItemTypeList = ref<MoItemTypeList[]>([]);
const moItemGroupList = ref<string[]>([]);
const actionUnitList = ref<ActionUnit[]>([]);
const actionLoading = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const isPreview = ref<boolean>(false);
const adviceContentTitle = ref<string>("添加医嘱");
const adviceContentRef = ref<InstanceType<typeof AdviceContent> | null>(null);
const info = ref<BaseMoItemData | null>(null);
provide("moItemTypeList", moItemTypeList);
provide("recoveryList", recoveryList);
provide("moItemGroupList", moItemGroupList);
provide("isPreview", isPreview);
const showDialog = ref({
  push: false,
  adviceContent: false,
});
const handleQuery = () => {
  queryParams.value.page = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  try {
    const copyData = JSON.parse(JSON.stringify(queryParams.value));
    Object.keys(copyData).forEach((key) => {
      if (copyData[key] === "") {
        copyData[key] = undefined;
      }
    });
    let sendData: MoItemPageDataInputDTO = {
      page: copyData.page,
      pageSize: copyData.pageSize,
      isEnable: copyData.isEnable,
      moItemUseScope: copyData.moItemUseScope === 0 ? undefined : [copyData.moItemUseScope],
      moType: copyData.moType === 0 ? undefined : copyData.moType,
      actionUnit: copyData.actionUnit,
      keywords: copyData.keywords,
      isDefaultPush: copyData.isDefaultPush,
      keywordMode: copyData.keywordMode,
      moItemMethod: copyData.moItemMethod === -1 ? undefined : copyData.moItemMethod,
      tag: copyData.tag,
      catelog: copyData.catelog,
    };
    tableLoading.value = true;
    const res = await Content_Api.getMoItemPageData(sendData);
    if (res.Type === 200) {
      res.Data.Data.forEach((item) => {
        item.CreatedTime = dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
      });
      pageData.value = res.Data.Data;
      total.value = res.Data.TotalCount;
    }
  } finally {
    tableLoading.value = false;
  }
};

const fetchRecoveryTypeList = async () => {
  const list = await getRecoveryTypeList({
    PageSize: 999,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
  });
  recoveryList.value = list;
};

const fetchMoItemTypeList = async () => {
  const list = await getMoItemTypeList();
  moItemTypeList.value = list;
};

const fetchMoItemGroupList = async () => {
  const list = await getMoItemGroupList();
  moItemGroupList.value = list;
};

/**
 * 搜索康复训练
 * @param value
 */
const handleSearchActionUnit = async (value: string) => {
  actionLoading.value = true;
  try {
    const list = await getActionUnitList({
      Enable: true,
      Keyword: value,
      UseScope: 0,
      PageSize: 50,
      PageIndex: 1,
    });
    actionUnitList.value = list;
  } catch (err) {
    console.error("handleSearchActionUnitError", err);
  } finally {
    actionLoading.value = false;
  }
};

const handleTableSelect = (selection: MoItemPageData[]) => {
  selectedTableIds.value = selection.map((item) => item.Id);
};
const handlePushHospitalSubmit = async (orgIds: string[]) => {
  if (!orgIds.length) {
    ElMessage.warning("请选择推送的机构");
    return;
  }
  try {
    dialogConfirmLoading.value = true;
    const res = await Content_Api.pushMoItem({
      Ids: selectedTableIds.value,
      OrgIds: orgIds,
    });
    if (res.Type === 200) {
      ElNotification.success("推送成功");
      showDialog.value.push = false;
      tableRef.value?.clearSelection();
      selectedTableIds.value = [];
    } else {
      ElNotification.error(res.Content);
    }
  } catch (err) {
    console.error("handlePushHospitalSubmitError", err);
  } finally {
    dialogConfirmLoading.value = false;
  }
};
const handlePushHospital = () => {
  if (!selectedTableIds.value.length) {
    ElMessage.error("请选择医嘱");
    return;
  }
  showDialog.value.push = true;
};
const handleSubmit = async () => {
  const params = await adviceContentRef.value?.handleSubmitData();
  if (params) {
    dialogConfirmLoading.value = true;
    Content_Api.insertOrUpdateMoItem(params)
      .then((res) => {
        if (res.Type === 200) {
          ElNotification.success(res.Content);
          showDialog.value.adviceContent = false;
          handleGetTableList();
        } else {
          ElNotification.error(res.Content);
        }
      })
      .finally(() => {
        dialogConfirmLoading.value = false;
      });
  }
};
const handleAddAdvice = () => {
  adviceContentTitle.value = `添加医嘱`;
  isPreview.value = false;
  info.value = null;
  showDialog.value.adviceContent = true;
};
const handlePreviewOrEdit = async (row: MoItemPageData, isPreviewData: boolean) => {
  adviceContentTitle.value = isPreviewData ? `查看${row.Name}医嘱` : `编辑${row.Name}医嘱`;
  if (!row.Id) {
    ElMessage.error("医嘱ID不能为空");
  } else {
    // 获取医嘱详情
    const res = await Content_Api.getMoItemByIds({ Ids: [row.Id] });
    if (res.Type !== 200 || !res.Data.length) {
      ElMessage.error("获取医嘱详情失败");
      return;
    }
    const infoItem = res.Data[0];
    info.value = infoItem;
    isPreview.value = isPreviewData;
    showDialog.value.adviceContent = true;
  }
};

onMounted(() => {
  fetchRecoveryTypeList();
  fetchMoItemTypeList();
  fetchMoItemGroupList();
});
onActivated(() => {
  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  handleGetTableList();
});
</script>
<style scoped lang="scss"></style>
