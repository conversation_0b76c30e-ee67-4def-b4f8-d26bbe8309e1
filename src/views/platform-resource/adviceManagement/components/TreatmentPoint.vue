<template>
  <div class="h-full">
    <el-transfer
      v-model="targetKeys"
      :data="leftListData"
      :disabled="isPreview"
      :props="{
        key: 'Id',
        label: 'Name',
      }"
      :titles="['待选择', '已选择']"
      filterable
      :filter-method="filterOption"
      style="width: 100%"
    />
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import type { TransferDataItem } from "element-plus";
const isPreview = inject("isPreview") as Ref<boolean>;

interface Props {
  treatSelectData: string[];
}

const props = defineProps<Props>();

const targetKeys = ref<string[]>([]);
const leftListData = ref<BaseOrganization[]>([]);
const disabled = ref(false);
const loading = ref(false);

// 过滤方法
const filterOption = (query: string, item: TransferDataItem): boolean => {
  const org = item as unknown as BaseOrganization;
  return org.Name!.includes(query) || (org.Code || "").includes(query);
};

// 加载机构列表数据
const loadOrganizationList = async () => {
  loading.value = true;
  try {
    const res = await Passport_Api.getOrganizationList({
      PageIndex: 1,
      PageSize: 9999,
      IsEnabled: true,
      IsTreatment: true,
      Pageable: true,
      DtoTypeName: "QueryOrgDetailOutputDTO1",
      Keyword: "",
      Scopeable: true,
    });

    if (res.Type === 200) {
      leftListData.value = res.Data.Rows;
    } else {
      leftListData.value = [];
    }
  } catch (error) {
    console.error("加载机构列表失败:", error);
    leftListData.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听props变化
watch(
  () => props.treatSelectData,
  (newVal) => {
    targetKeys.value = newVal;
  },
  {
    immediate: true,
  }
);

// 组件挂载时加载数据
onMounted(() => {
  loadOrganizationList();
});

// 向父组件暴露方法
const handleSelectFinish = (): string[] => {
  return targetKeys.value;
};

defineExpose({
  handleSelectFinish,
});
</script>

<style lang="scss" scoped>
:deep(.el-transfer) {
  width: 100%;
}
:deep(.el-transfer-panel) {
  width: calc(50% - 100px) !important;
  height: 100% !important;
}
</style>
