<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form ref="queryFormRef" label-position="right" :model="queryParams" :inline="true">
              <el-form-item prop="packType" label="类型">
                <el-select
                  v-model="queryParams.packType"
                  placeholder="请选择"
                  clearable
                  multiple
                  :multiple-limit="1"
                  :empty-values="[undefined, null, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="居家" :value="AdviceMoItemUseScope.Home" />
                  <el-option label="线下" :value="AdviceMoItemUseScope.Community" />
                  <el-option label="院内" :value="AdviceMoItemUseScope.Hospital" />
                </el-select>
              </el-form-item>
              <el-form-item prop="diseasesId" label="适用疾病">
                <el-select
                  v-model="queryParams.diseasesId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[undefined, null, '']"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="item in diseaseList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="deptId" label="科别">
                <el-select
                  v-model="queryParams.deptId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[undefined, null, '']"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="isDefaultPush" label="默认推送">
                <el-select
                  v-model="queryParams.isDefaultPush"
                  clearable
                  placeholder="请选择"
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item prop="isEnable" label="是否启用">
                <el-select
                  v-model="queryParams.isEnable"
                  clearable
                  placeholder="请选择"
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item prop="keywords" label="关键字">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handlePushOperation">批量操作</el-button>
            <el-button type="primary" @click="handleAdd">新增</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          highlight-current-row
          row-key="Id"
          border
          :height="tableFluidHeight"
          style="text-align: center; flex: 1"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" width="55" align="center" reserve-selection />
          <el-table-column
            prop="Name"
            label="名称"
            :show-overflow-tooltip="true"
            align="center"
            width="180"
          />
          <el-table-column label="类型" width="80" align="center">
            <template #default="scope">
              {{ ["", "居家", "线下", "院内"][scope.row.Type] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="ExecutDay"
            label="执行天数/天"
            :show-overflow-tooltip="true"
            align="center"
            width="100"
          />
          <el-table-column label="总价（元）" prop="Amount" width="100" align="center" />
          <el-table-column
            label="方案说明"
            show-overflow-tooltip
            prop="TherapistRemark"
            align="center"
            width="180"
          />
          <el-table-column
            label="适用疾病"
            show-overflow-tooltip
            prop="DiseasesName"
            align="center"
            width="120"
          />
          <el-table-column
            label="诊断"
            show-overflow-tooltip
            prop="DiagnosisName"
            align="center"
            width="180"
          />
          <el-table-column label="适用科别" prop="DeptNames" align="center" width="120" />
          <el-table-column label="排序" prop="Sort" align="center" width="100" />
          <el-table-column
            label="创建时间"
            prop="CreatedTime"
            width="150"
            align="center"
            :formatter="handleFormatTime"
          />
          <el-table-column label="是否推送" width="80" align="center">
            <template #default="scope">
              {{ scope.row.IsDefaultPush ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column label="是否启用" width="80" align="center">
            <template #default="scope">
              {{ scope.row.IsEnable ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column
            label="服务包备注"
            show-overflow-tooltip
            prop="Remark"
            width="180"
            align="center"
          />
          <el-table-column
            label="部位"
            prop="PartName"
            width="150"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button
                link
                size="small"
                type="primary"
                @click="handlePreviewOrEdit(scope.row, true)"
              >
                查看
              </el-button>
              <el-button
                link
                size="small"
                type="primary"
                @click="handlePreviewOrEdit(scope.row, false)"
              >
                编辑
              </el-button>
              <el-button link size="small" type="primary" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <div v-show="pushMenuShow" ref="menuRef">
      <ul id="menu" class="menu" :style="'top:' + clickPoint.y + 'px;left:' + clickPoint.x + 'px;'">
        <li class="menu_item" @click="handlePushOperationClick('push')">批量推送</li>
        <li class="menu_item" @click="handlePushOperationClick('enable')">启用</li>
        <li class="menu_item" @click="handlePushOperationClick('disable')">停用</li>
        <li class="menu_item" @click="handlePushOperationClick('defaultPush')">默认推送</li>
        <li class="menu_item" @click="handlePushOperationClick('notDefaultPush')">不默认推送</li>
        <li class="menu_item" @click="handlePushOperationClick('dept')">设置科别</li>
      </ul>
    </div>
    <el-dialog v-model="showDialog.push" title="请选择推送的机构" width="700px" destroy-on-close>
      <HospitalTransfer
        :loading="dialogConfirmLoading"
        @cancel="showDialog.push = false"
        @submit="handlePushHospitalSubmit"
      />
    </el-dialog>
    <el-dialog v-model="showDialog.dept" title="请选择设置的科别" width="400" destroy-on-close>
      <div class="flex items-center">
        <span>科别：</span>
        <el-select v-model="setDeptIds" style="flex: 1" placeholder="请选择" multiple filterable>
          <el-option v-for="item in deptList" :key="item.Id" :label="item.Key" :value="item.Id!" />
        </el-select>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.dept = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handlePushDeptSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.serviceContent"
      :title="serviceContentTitle"
      width="800px"
      destroy-on-close
      :close-on-click-modal="isOnlyPreview"
      :close-on-press-escape="isOnlyPreview"
    >
      <ServiceContent
        ref="serviceContentRef"
        :dept-list="deptList"
        :disease-list="diseaseList"
        :pack-detail="packDetail"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.serviceContent = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleServiceContentSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { AdviceMoItemUseScope } from "@/enums/AdviceEnum";
import { GetMoItemPackInputDTO, MoItemPackInputDTO, MoItemPackItem } from "@/api/content/types";
import Content_Api from "@/api/content";
import dayjs from "dayjs";
import { getDeptDivisionsList, getDiseaseList } from "@/utils/dict";
import { useClickPoint } from "@/hooks/useClickPoint";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useTemplateRef } from "vue";
import { onClickOutside } from "@vueuse/core";
import ServiceContent from "./components/ServiceContent.vue";

const { clickPoint, handleClick } = useClickPoint();
const diseaseList = ref<ReadDict[]>([]);
const deptList = ref<ReadDict[]>([]);
const setDeptIds = ref<string[]>([]);
const pushMenuShow = ref<boolean>(false);
const menuRef = useTemplateRef("menuRef");
const serviceContentTitle = ref<string>("添加服务包");
const serviceContentRef = ref<InstanceType<typeof ServiceContent> | null>(null);
const packDetail = ref<MoItemPackInputDTO | null>(null);
const isOnlyPreview = ref<boolean>(false);
const { tableLoading, pageData, total, tableRef, selectedTableIds, tableFluidHeight, tableResize } =
  useTableConfig<MoItemPackItem>();

provide("deptList", deptList);
provide("diseaseList", diseaseList);
provide("isOnlyPreview", isOnlyPreview);
onClickOutside(menuRef, (event) => {
  pushMenuShow.value = false;
});
defineOptions({
  name: "ServicePackageManagement",
});
interface PageDialogShow {
  push: boolean;
  dept: boolean;
  serviceContent: boolean;
}
const queryParams = ref<GetMoItemPackInputDTO>({
  packType: null,
  diseasesId: null,
  deptId: null,
  isDefaultPush: null,
  isEnable: null,
  keywords: "",
  pageIndex: 1,
  pageSize: 10,
  loadPackAmount: true,
});
const showDialog = ref<PageDialogShow>({
  push: false,
  dept: false,
  serviceContent: false,
});
const dialogConfirmLoading = ref<boolean>(false);

const handleFormatTime = (row: MoItemPackItem, column: unknown, cellValue: string) => {
  return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
};
const handleQuery = () => {
  queryParams.value.pageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  // 将 null 的数据去除掉
  Object.keys(copyData).forEach((key) => {
    if (copyData[key] === null) {
      delete copyData[key];
    }
  });
  const res = await Content_Api.getMoItemPackList(copyData);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

const handlePushOperation = (event: MouseEvent) => {
  if (!selectedTableIds.value.length) {
    ElMessage.warning("请选择服务包");
    return;
  }
  handleClick(event);
  clickPoint.value.x = event.clientX - 90;
  clickPoint.value.y = event.clientY + 10;
  pushMenuShow.value = true;
};
const handleAdd = () => {
  isOnlyPreview.value = false;
  serviceContentTitle.value = "添加服务包";
  packDetail.value = null;
  showDialog.value.serviceContent = true;
};
const handleDelete = (row: MoItemPackItem) => {
  ElMessageBox.confirm("确定要删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Content_Api.deleteMoItemPack({ Id: row.Id })
      .then((res) => {
        if (res.Type === 200) {
          ElNotification.success("删除成功");
          handleGetTableList();
        } else {
          ElNotification.error(res.Content);
        }
      })
      .catch(() => {
        ElNotification.error("删除失败");
      });
  });
};
const handlePreviewOrEdit = async (row: MoItemPackItem, isPreview: boolean) => {
  console.log("查看/编辑", row, isPreview);
  isOnlyPreview.value = isPreview;
  serviceContentTitle.value = isPreview ? `查看${row.Name}服务包` : `编辑${row.Name}服务包`;
  const res = await Content_Api.getPackDetailByID({
    packId: row.Id,
    loadPackAmount: false,
  });
  if (res.Type === 200) {
    packDetail.value = res.Data;
  }
  showDialog.value.serviceContent = true;
};
const fetchDiseaseList = async () => {
  const list = await getDiseaseList();
  diseaseList.value = list;
};
const fetchDeptList = async () => {
  const list = await getDeptDivisionsList();
  deptList.value = list;
};
const handleTableSelect = (selection: MoItemPackItem[]) => {
  selectedTableIds.value = selection.map((item) => item.Id);
};
const handlePushOperationClick = (type: string) => {
  switch (type) {
    case "push":
      showDialog.value.push = true;
      break;
    case "enable":
      console.log("启用");
      handleChangeServicePackage({
        params: {
          Ids: selectedTableIds.value,
          IsEnable: true,
        },
        tip: "确定将所选数据【启用】",
      });
      break;
    case "disable":
      console.log("停用");
      handleChangeServicePackage({
        params: {
          Ids: selectedTableIds.value,
          IsEnable: false,
        },
        tip: "确定将所选数据【停用】",
      });
      break;
    case "defaultPush":
      console.log("默认推送");
      handleChangeServicePackage({
        params: {
          Ids: selectedTableIds.value,
          IsDefaultPush: true,
        },
        tip: "确定将所选数据【默认推送】",
      });
      break;
    case "notDefaultPush":
      console.log("不默认推送");
      handleChangeServicePackage({
        params: {
          Ids: selectedTableIds.value,
          IsDefaultPush: false,
        },
        tip: "确定将所选数据【不默认推送】",
      });
      break;
    case "dept":
      showDialog.value.dept = true;
      break;
  }
  pushMenuShow.value = false;
};
const handleChangeServicePackage = async (params: {
  params: {
    Ids: string[];
    IsEnable?: boolean;
    IsDefaultPush?: boolean;
    DeptIds?: string[];
  };
  tip: string;
}) => {
  ElMessageBox.confirm(params.tip, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Content_Api.batchOperateMoItemPack(params.params)
      .then((res) => {
        if (res.Type === 200) {
          ElMessage.success("操作成功");
          // 清空选中
          selectedTableIds.value = [];
          tableRef.value?.clearSelection();
          handleGetTableList();
        } else {
          ElNotification.error(res.Content);
        }
      })
      .catch(() => {
        ElNotification.error("操作失败");
      });
  });
};

const handlePushHospitalSubmit = (orgIds: string[]) => {
  if (!orgIds.length) {
    ElMessage.error("请选择推送的机构");
    return;
  }
  dialogConfirmLoading.value = true;
  Content_Api.pushMoItemPack({
    Ids: selectedTableIds.value,
    OrgIds: orgIds,
  })
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success("推送成功");
        showDialog.value.push = false;
        tableRef.value?.clearSelection();
        selectedTableIds.value = [];
      } else {
        ElMessage.error(res.Content);
      }
    })
    .catch(() => {
      ElMessage.error("推送失败");
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handlePushDeptSubmit = () => {
  if (!setDeptIds.value.length) {
    ElMessage.error("请选择科别");
    return;
  }
  dialogConfirmLoading.value = true;
  Content_Api.batchOperateMoItemPack({
    Ids: selectedTableIds.value,
    DeptIds: setDeptIds.value,
  })
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success("操作成功");
        // 清空选中
        selectedTableIds.value = [];
        setDeptIds.value = [];
        tableRef.value?.clearSelection();
        showDialog.value.dept = false;
        handleGetTableList();
      } else {
        ElNotification.error(res.Content);
      }
    })
    .catch(() => {
      ElNotification.error("操作失败");
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handleServiceContentSubmit = async () => {
  const sendData = await serviceContentRef.value?.handleSubmit();
  if (!sendData) {
    return;
  }
  console.log("sendData", sendData);
  const func = sendData.PackId ? Content_Api.updateMoItemPack : Content_Api.addMoItemPack;
  try {
    dialogConfirmLoading.value = true;
    func(sendData)
      .then((res) => {
        if (res.Type === 200) {
          ElNotification.success("操作成功");
          showDialog.value.serviceContent = false;
          handleGetTableList();
        } else {
          ElNotification.error(res.Content);
        }
      })
      .finally(() => {
        dialogConfirmLoading.value = false;
      });
  } catch (err) {
    ElNotification.error("操作失败");
  } finally {
    dialogConfirmLoading.value = false;
  }
};
onMounted(() => {
  fetchDiseaseList();
  fetchDeptList();
});
onActivated(() => {
  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  handleGetTableList();
});
</script>

<style scoped lang="scss">
.menu {
  width: 80px;
  position: fixed;
  border-radius: 10px;
  border: 1px solid #eeeeee;
  background-color: #ffffff;
  color: var(--el-color-primary);
  z-index: 999;
  padding: 0;
  .menu_item {
    line-height: 30px;
    text-align: center;
  }
  li:hover {
    background-color: var(--el-color-primary);
    color: white;
  }
  li {
    font-size: 14px;
    list-style: none;
  }
}
</style>
