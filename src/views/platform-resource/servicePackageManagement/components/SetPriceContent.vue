<template>
  <div class="container">
    <div class="flex items-center justify-start mb-10px">
      <div class="flex items-center justify-start">
        <span class="mr-10px">收费项目</span>
        <el-select
          v-model="topAddParams.ChargeItemId"
          placeholder="请选择收费项目"
          style="width: 150px"
        >
          <el-option
            v-for="item in chargeItemList"
            :key="item.Id"
            :label="item.Name"
            :value="item.ChargeItemId"
          />
        </el-select>
      </div>
      <div class="flex items-center justify-start">
        <span class="m-x-10px">数量</span>
        <el-input-number
          v-model="topAddParams.Quantity"
          :step="1"
          step-strictly
          :min="1"
          class="m-x-10px"
        />
      </div>
      <el-button type="primary" @click="handleAddPrice">添加</el-button>
    </div>
    <el-table :data="formData" align="center" border>
      <el-table-column prop="Name" label="名称" align="center" width="180" />
      <el-table-column prop="Code" label="编码" align="center" />
      <el-table-column prop="Quantity" label="数量" align="center" />
      <el-table-column prop="Unit" label="单位" align="center" />
      <el-table-column prop="Amount" label="价格" align="center">
        <template #default="scope">
          {{ calculateAmount([scope.row.Price, scope.row.Quantity], "*") }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="180" align="center">
        <template #default="scope">
          <el-button
            :disabled="isOnlyPreview"
            link
            type="danger"
            @click="handleDeletePrice(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <span class="mt-20px">总价：{{ "￥" + totalAmount }}</span>
  </div>
</template>

<script setup lang="ts">
import { calculateAmount } from "@/utils";
import Content_Api from "@/api/content";
import { PageBaseChargeItemType } from "@/api/content/types";
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
// 计算合计金额
const totalAmount = computed(() => {
  const amounts = formData.map((s) => calculateAmount([s.Price, s.Quantity!], "*"));
  return calculateAmount(amounts, "+");
});

const formData = reactive<PageBaseChargeItemType[]>([]);
const chargeItemList = ref<PageBaseChargeItemType[]>([]);
const topAddParams = reactive<{
  ChargeItemId: string;
  Quantity?: number;
}>({
  ChargeItemId: "",
  Quantity: undefined,
});
const handleSubmit = (): PageBaseChargeItemType[] => {
  return formData;
};
const handleGetChargeItemList = async () => {
  const res = await Content_Api.getChargeItemPageData({
    page: 1,
    pageSize: 1000,
    isEnable: true,
    keywords: "",
    type: 3,
  });
  if (res.Type === 200) {
    const newData = res.Data.Data.map((item): PageBaseChargeItemType => {
      return {
        ...item,
        ChargeItemId: item.Id,
      };
    });
    chargeItemList.value = newData;
  } else {
    chargeItemList.value = [];
  }
};
const handleAddPrice = () => {
  if (!topAddParams.ChargeItemId) {
    ElMessage.error("请选择收费项目");
    return;
  }
  if (!topAddParams.Quantity) {
    ElMessage.error("请输入数量");
    return;
  }
  const chargeItemId = topAddParams.ChargeItemId;
  if (formData.some((s) => s.ChargeItemId === chargeItemId)) {
    ElMessage.warning("收费项目已存在");
    return;
  }
  const chargeItem = chargeItemList.value.find((s) => s.ChargeItemId === chargeItemId);
  formData.push({
    ...chargeItem!,
    Quantity: topAddParams.Quantity,
  });
};
const handleDeletePrice = (item: PageBaseChargeItemType) => {
  const indexof = formData.indexOf(item);
  formData.splice(indexof, 1);
};

defineExpose({
  handleSubmit,
});
interface Props {
  moItemId: string;
  info: PageBaseChargeItemType[];
}
const props = defineProps<Props>();
watch(
  () => props.info,
  async (newVal) => {
    if (newVal) {
      // 使用 Object.assign 更新属性，保持响应式
      Object.assign(formData, newVal);
      // 获取收费项目列表
      handleGetChargeItemList();
    }
  },
  {
    immediate: true,
  }
);
</script>

<style lang="scss" scoped>
.container {
  height: 400px;
  overflow-y: auto;
  width: 100%;
  padding: 20px;
}
</style>
