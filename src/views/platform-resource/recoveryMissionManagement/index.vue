<template>
  <el-container class="w-full h-full">
    <!-- 左侧目录 -->
    <el-aside width="230px" class="p-10px">
      <el-tree
        class="w-full h-full p-10px"
        :data="recoveryMissionTypeList"
        :props="defaultTreeProps"
        node-key="Id"
        :current-node-key="queryParams.RecoveryMissionType"
        highlight-current
        default-expand-all
        @node-click="onTreeClick"
      />
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <template #search>
          <!-- 顶部筛选条件 -->
          <TBSearchContainer>
            <template #left>
              <el-form label-position="right" :model="queryParams" :inline="true">
                <el-form-item label="是否启用" prop="Enable">
                  <KSelect
                    v-model="queryParams.Enable"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="是否默认推送" prop="IsDefaultPush">
                  <KSelect
                    v-model="queryParams.IsDefaultPush"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="科别" prop="DeptId">
                  <KSelect
                    v-model="queryParams.DeptId"
                    :data="departmentList"
                    :props="{ label: 'Key', value: 'Id' }"
                    :loading="departmentLoading"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="首页推荐" prop="IsRecommend">
                  <KSelect
                    v-model="queryParams.IsRecommend"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="关键字" prop="KeyWord">
                  <el-input
                    v-model="queryParams.KeyWord"
                    clearable
                    placeholder="输入标题/作者名"
                    @keyup.enter="requestRecoveryMissionData"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="requestRecoveryMissionData">
                搜索
              </el-button>
              <el-button type="primary" @click="onBatchPushMissions">批量推送</el-button>
              <el-button type="primary" @click="onAddMission">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <!-- 量表列表 -->
        <template #table>
          <el-table
            :ref="kTableRef"
            :data="pageData"
            highlight-current-row
            border
            :height="tableFluidHeight"
            @selection-change="(selection) => handleSelectionChange(selection, 'ContentId')"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              prop="RecoveryMissionTypeName"
              label="类别"
              show-overflow-tooltip
              align="center"
              width="150"
            />
            <el-table-column
              prop="Title"
              label="标题"
              show-overflow-tooltip
              align="center"
              min-width="200"
            />
            <el-table-column
              prop="ShowDisease"
              label="疾病种类"
              show-overflow-tooltip
              align="center"
              width="150"
            />
            <el-table-column
              prop="DeptName"
              label="科别"
              show-overflow-tooltip
              align="center"
              width="150"
            />
            <el-table-column prop="AuthorName" label="作者" align="center" width="100" />
            <el-table-column prop="CreatedTime" label="上传时间" align="center" width="180" />
            <el-table-column prop="CreatorName" label="操作员" align="center" width="100" />
            <el-table-column prop="IsEnbleText" label="是否启用" align="center" width="80" />
            <el-table-column prop="IsRecommendText" label="首页推送" align="center" width="80" />
            <el-table-column prop="IsDefaultPushText" label="是否推送" align="center" width="80" />
            <el-table-column fixed="right" label="操作" width="180" align="center">
              <template #default="scope">
                <el-button link size="small" type="primary" @click="onMissionDetail(scope.row)">
                  查看
                </el-button>
                <el-button link size="small" type="primary" @click="onEditMission(scope.row)">
                  编辑
                </el-button>
                <el-button link size="small" type="primary" @click="onDeleteMission(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.PageIndex"
            v-model:limit="queryParams.PageSize"
            @pagination="requestRecoveryMissionData"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>

  <!-- 添加/编辑/查看量表 -->
  <el-dialog
    v-model="showMissionDialog.isShow"
    :title="showMissionDialog.title"
    width="800"
    destroy-on-close
    @close="showMissionDialog.isShow = false"
  >
    <RecoveryMissionForm
      :mission="showMissionDialog.mission"
      :recovery-types="recoveryMissionTypeList.find((item) => item.Name === '全部')?.Children ?? []"
      :departments="departmentList"
      :diseases="diseaseList"
      :disabled="showMissionDialog.disabled"
      @cancel="showMissionDialog.isShow = false"
      @submit="onConfirmSubmitMission"
    />
  </el-dialog>
  <!-- 选择机构 -->
  <el-dialog v-model="showOrgDialog" title="选择机构" width="700px" destroy-on-close>
    <HospitalTransfer
      :loading="orgDialogLoading"
      @cancel="showOrgDialog = false"
      @submit="onConfirmOrganizations"
    />
  </el-dialog>
</template>

<script setup lang="ts">
const kEnableDebug = true;
defineOptions({
  name: "RecoveryMissionManagement",
});

import Content_Api from "@/api/content";
import { PageQueryContentInputDTO, RecoveryMissionTypesInputDTO } from "@/api/content/types";
import Dictionary_Api from "@/api/dictionary";
import { useTableConfig } from "@/hooks/useTableConfig";
import useOrgDialog from "@/hooks/useOrgDialog";
import dayjs from "dayjs";

interface TableData extends BaseRecoveryMission {
  IsEnbleText?: string;
  IsDefaultPushText?: string;
  IsRecommendText?: string;
  // 疾病逗号拼接
  ShowDisease?: string;
}

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  selectedTableIds,
  handleSelectionChange,
} = useTableConfig<TableData>();

const { showOrgDialog, orgDialogLoading } = useOrgDialog();

// 查询条件
const queryParams = reactive<PageQueryContentInputDTO>({
  PageIndex: 1,
  PageSize: 20,
});

// 左侧列表数据结构
const recoveryMissionTypeList = ref<RecoveryMissionType[]>([]);
const defaultTreeProps = reactive({
  children: "Children",
  label: "Name",
});

// 树点击事件
async function onTreeClick(data: RecoveryMissionType) {
  kEnableDebug && console.debug("树点击事件", data);
  queryParams.RecoveryMissionType = data.Id;
  queryParams.PageIndex = 1;

  const r = await requestRecoveryMissionData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 宣教弹窗
const showMissionDialog = reactive({
  isShow: false,
  title: "",
  mission: {} as BaseRecoveryMission,
  disabled: false,
});

// 批量推送宣教
async function onBatchPushMissions() {
  kEnableDebug && console.debug("批量推送宣教");

  if (!selectedTableIds.value?.length) {
    ElMessage.error("请选择需要推送的康复宣教");
    return;
  }

  showOrgDialog.value = true;
}

// 选择机构
async function onConfirmOrganizations(organizationIds: string[]) {
  kEnableDebug && console.log("选择机构", organizationIds);

  // 推送
  orgDialogLoading.value = true;
  const r = await Content_Api.pushRecoveryMissions({
    Ids: selectedTableIds.value ?? [],
    OrgIds: organizationIds,
  });
  orgDialogLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showOrgDialog.value = false;
  ElNotification.success("推送成功");

  // 清空选项
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
}

// 添加宣教
async function onAddMission() {
  kEnableDebug && console.debug("添加宣教");

  const showAlert = () => {
    ElMessageBox.alert("请先选择分类", "系统提示", {
      confirmButtonText: "确定",
      type: "warning",
    });
  };

  if (!queryParams.RecoveryMissionType) {
    showAlert();
    return;
  }

  const item = findRecoveryMissionById(
    recoveryMissionTypeList.value,
    queryParams.RecoveryMissionType
  );
  if (item?.Code === "001" && item.Name === "全部") {
    showAlert();
    return;
  }

  showMissionDialog.mission = {
    RecoveryMissionType: queryParams.RecoveryMissionType,
    RecoveryMissionTypeName: item?.Name,
    Enable: true,
    IsRecommend: false,
    IsDefaultPush: false,
    Sort: 0,
  };
  showMissionDialog.disabled = false;
  showMissionDialog.title = "添加宣教";
  showMissionDialog.isShow = true;
}

// 查看宣教
async function onMissionDetail(item: BaseRecoveryMission) {
  kEnableDebug && console.debug("查看宣教", item);
  if (!item.ContentId) {
    ElMessage.error("宣教ID为空");
    return;
  }

  const r = await requestRecoveryMissionDetail(item.ContentId);
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showMissionDialog.mission = r.Data;
  showMissionDialog.title = "查看宣教";
  showMissionDialog.disabled = true;
  showMissionDialog.isShow = true;
}

// 编辑宣教
async function onEditMission(item: BaseRecoveryMission) {
  kEnableDebug && console.debug("编辑宣教", item);
  if (!item.ContentId) {
    ElMessage.error("宣教ID为空");
    return;
  }

  const r = await requestRecoveryMissionDetail(item.ContentId);
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showMissionDialog.mission = r.Data;
  showMissionDialog.title = "编辑宣教";
  showMissionDialog.disabled = false;
  showMissionDialog.isShow = true;
}

// 删除宣教
async function onDeleteMission(item: BaseRecoveryMission) {
  kEnableDebug && console.debug("删除宣教", item);

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const contentId = item.ContentId;
    if (!contentId) {
      ElMessage.error("宣教ID为空");
      return;
    }

    tableLoading.value = true;
    const r = await Content_Api.deleteRecoveryMissionById(contentId);
    tableLoading.value = false;
    if (r.Type !== 200) {
      ElMessage.error(r.Content);
      return;
    }
    ElNotification.success("删除成功");

    const r1 = await requestRecoveryMissionData();
    if (r1.Type !== 200) {
      ElMessage.error(r1.Content);
    }
  });
}

// 确认提交宣教
async function onConfirmSubmitMission() {
  kEnableDebug && console.debug("确认提交宣教");

  showMissionDialog.isShow = false;
  showMissionDialog.mission = {
    Enable: true,
    IsRecommend: false,
    IsDefaultPush: false,
  };

  const r = await requestRecoveryMissionData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 递归查询当前类别名称
function findRecoveryMissionById(
  tree: RecoveryMissionType[],
  targetId: string
): RecoveryMissionType | undefined {
  // 遍历树中的每个节点
  for (const node of tree) {
    // 如果当前节点的 Id 匹配目标 Id，返回该节点
    if (node.Id === targetId) {
      return node;
    }

    // 如果当前节点有子节点，递归查找子节点
    if (node.Children && node.Children.length > 0) {
      const foundNode = findRecoveryMissionById(node.Children, targetId);
      if (foundNode) {
        return foundNode; // 如果在子节点中找到，直接返回
      }
    }
  }

  // 如果未找到匹配的节点，返回 undefined
  return undefined;
}

// 请求康复宣教分类
async function requestRecoveryMissionTypeData() {
  const params: RecoveryMissionTypesInputDTO = {
    isShowOperation: true,
    isShowDisease: true,
    isShowRegime: true,
  };
  const r = await Content_Api.getAllRecoveryMissionTypes(params);
  if (r.Type === 200) {
    recoveryMissionTypeList.value = r.Data;
  }

  return r;
}

// 请求康复宣教列表
async function requestRecoveryMissionData() {
  tableLoading.value = true;
  const r = await Content_Api.pageQueryContent(queryParams);
  tableLoading.value = false;
  if (r.Type === 200) {
    pageData.value = r.Data.Data.map((item) => {
      const diseases = diseaseList
        .filter((d) => {
          return d.Id && item.Diseases?.includes(d.Id);
        })
        .map((d) => d.Key)
        .join(",");
      return {
        ...item,
        IsEnbleText: item.Enable ? "是" : "否",
        IsDefaultPushText: item.IsDefaultPush ? "是" : "否",
        IsRecommendText: item.IsRecommend ? "是" : "否",
        ShowDisease: diseases,
        CreatedTime: item.CreatedTime ? dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss") : "",
      };
    });
    total.value = r.Data.Total;
  }

  return r;
}

// 请求宣教详情
async function requestRecoveryMissionDetail(contentId: string) {
  tableLoading.value = true;
  const r = await Content_Api.getRecoveryMissionById(contentId);
  tableLoading.value = false;
  return r;
}

// 科别列表
const departmentList = reactive<ReadDict[]>([]);
const departmentLoading = ref<boolean>(false);

// 请求科别列表
async function requestDepartmentData() {
  departmentLoading.value = true;
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: 1,
      PageSize: 200,
      SortConditions: [
        { SortField: "CustomSort", ListSortDirection: 1 },
        { SortField: "CreatedTime", ListSortDirection: 1 },
      ],
    },
    FilterGroup: {
      Rules: [
        { Field: "DictId", Value: "5", Operate: 3 },
        { Field: "IsEnabled", Value: true, Operate: 3 },
        { Field: "IsPublish", Value: true, Operate: 3 },
      ],
      Groups: [
        {
          Rules: [
            { Field: "Key", Value: "", Operate: 11 },
            { Field: "Value", Value: "", Operate: 11 },
            { Field: "PinyinCode", Value: "", Operate: 11 },
          ],
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  const r = await Dictionary_Api.readDict(params);
  departmentLoading.value = false;
  if (r.Type === 200) {
    departmentList.push(...r.Data.Rows);
  }

  return r;
}

// 疾病列表
const diseaseList: ReadDict[] = [];

// 请求疾病列表
async function requestDiseaseData() {
  const r = await Dictionary_Api.getDict({ code: "DiseaseDict" });
  if (r.Type === 200) {
    diseaseList.push(...(r.Data ?? []));
  }
  return r;
}

onMounted(async () => {
  const rs = await Promise.all([
    requestRecoveryMissionTypeData(),
    requestDiseaseData(),
    requestDepartmentData(),
  ]);
  const fail = rs.find((r) => r.Type !== 200);
  if (fail) {
    ElMessage.error(fail.Content);
  }
});
onActivated(() => {
  requestRecoveryMissionData();
});
</script>

<style lang="scss" scoped></style>
