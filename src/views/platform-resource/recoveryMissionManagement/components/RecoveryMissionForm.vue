<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto max-h-600px min-h-600px">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      label-width="75px"
      :disabled="props.disabled"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="标题" prop="Title">
            <el-input v-model="formData.Title" clearable placeholder="请输入标题" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="作者" label-width="60px" prop="AuthorName">
            <el-input v-model="formData.AuthorName" clearable placeholder="请输入作者" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类别" label-width="60px" prop="RecoveryMissionType">
            <el-select
              ref="selectRef"
              v-model="formData.RecoveryMissionTypeName"
              placeholder="请选择"
            >
              <template #empty>
                <el-tree
                  class="w-full h-full p-10px"
                  :data="props.recoveryTypes"
                  :props="{ label: 'Name', children: 'Children' }"
                  node-key="Id"
                  :current-node-key="formData.RecoveryMissionType"
                  highlight-current
                  default-expand-all
                  @node-click="onTreeClick"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="是否启用" prop="Enable">
          <el-switch v-model="formData.Enable" />
        </el-form-item>
        <el-form-item label="是否默认推送" label-width="130px" prop="IsDefaultPush">
          <el-switch v-model="formData.IsDefaultPush" />
        </el-form-item>
        <el-form-item label="首页推荐" label-width="100px" prop="IsRecommend">
          <el-switch v-model="formData.IsRecommend" />
        </el-form-item>
        <el-form-item label="排序" prop="Sort">
          <el-input-number v-model="formData.Sort" clearable />
        </el-form-item>
      </el-row>
      <el-form-item label="来源">
        <el-radio-group v-model="isOriginal">
          <el-radio :value="true">原创</el-radio>
          <el-radio :value="false">转载</el-radio>
        </el-radio-group>
        <el-input
          v-show="!isOriginal"
          v-model="formData.Source"
          class="flex-1"
          clearable
          placeholder="请输入转载来源"
        />
      </el-form-item>
      <el-form-item label="科别" prop="Depts" class="mb-20px!">
        <TagsSelect
          v-model="selectedDepartments"
          :options="props.departments"
          :props="{ label: 'Key', value: 'Id' }"
          :disabled="props.disabled"
        />
      </el-form-item>
      <el-form-item label="疾病种类" label-width="77px" prop="Diseases" class="mb-20px!">
        <TagsSelect
          v-model="selectedDiseases"
          :options="props.diseases"
          :props="{ label: 'Key', value: 'Id' }"
          :disabled="props.disabled"
        />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="封面图" prop="ShowImg">
            <SingleImageUpload
              v-model="formData.ShowImg!"
              :disabled="props.disabled"
              :max-file-size="20"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="音频" prop="Media">
            <FileUpload
              v-model="formData.Media!"
              accept=".mp3"
              uploadBtnText="上传音频文件"
              :disabled="props.disabled"
              :max-file-size="20"
              :limit="1"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="摘要" prop="Summary">
        <el-input
          v-model="formData.Summary"
          placeholder="请输入宣教摘要"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </el-form-item>
      <el-form-item label="内容" prop="Text">
        <WangEditor v-model="formData.Text" height="450px" />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { FormRules, FormInstance } from "element-plus";

const kEnableDebug = true;
defineOptions({
  name: "RecoveryMissionForm",
});

const props = defineProps<{
  mission: BaseRecoveryMission;
  recoveryTypes: RecoveryMissionType[]; // 康复分类
  departments: ReadDict[]; // 科别
  diseases: ReadDict[]; // 疾病
  disabled: boolean;
}>();
const emit = defineEmits(["cancel", "submit"]);

const formRef = useTemplateRef<FormInstance>("ruleFormRef");
const selectRef = useTemplateRef("selectRef");

const formLoading = ref(false);
// 表单数据
const formData = reactive<BaseRecoveryMission>({
  Enable: true,
  IsDefaultPush: false,
  IsRecommend: false,
  Media: [],
});
// 表单规则
const rules = reactive<FormRules<BaseRecoveryMission>>({
  Title: [{ required: true, message: "请输入宣教名称", trigger: "blur" }],
  Text: [{ required: true, message: "请输入内容", trigger: "blur" }],
  RecoveryMissionType: [{ required: true, message: "请选择康复分类", trigger: "node-click" }],
  AuthorName: [{ required: true, message: "请输入作者", trigger: "blur" }],
});
// 是否是原创
const isOriginal = ref(true);

// 选择的科别
const selectedDepartments = ref<ReadDict[]>([]);
watch(selectedDepartments, (values) => {
  formData.Depts = values.map((item) => item.Id ?? "");
});

// 选择的疾病
const selectedDiseases = ref<ReadDict[]>([]);
watch(selectedDiseases, (values) => {
  formData.Diseases = values.map((item) => item.Id ?? "");
});

// 点击选择类别
function onTreeClick(data: RecoveryMissionType) {
  formData.RecoveryMissionType = data.Id;
  formData.RecoveryMissionTypeName = data.Name;

  // 主动关闭类别弹窗
  selectRef.value?.blur();
}

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;
  if (!formData.Text) {
    ElMessage.warning("请输入内容");
    return;
  }

  formRef.value.validate((valid, fields) => {
    if (valid) {
      formData.Source = isOriginal.value ? undefined : formData.Source;
      formData.Depts = selectedDepartments.value.map((item) => item.Id ?? "");
      formData.Diseases = selectedDiseases.value.map((item) => item.Id ?? "");
      if (props.mission.ContentId) {
        requestUpdateMission();
      } else {
        requestAddMission();
      }
    } else {
      kEnableDebug && console.debug("宣教 - 提交失败", fields, formData);
    }
  });
}

// 新增宣教
async function requestAddMission() {
  console.debug("requestAddMission", formData);

  formLoading.value = true;
  const res = await Content_Api.createRecoveryMissionContent(formData);
  formLoading.value = false;
  if (res.Type === 200) {
    ElNotification.success("新增成功");
    emit("submit");
  } else {
    ElNotification.error("新增失败");
  }
}

// 更新宣教
async function requestUpdateMission() {
  console.debug("requestUpdateMission", formData);

  formLoading.value = true;
  const res = await Content_Api.updateRecoveryMissionContent(formData);
  formLoading.value = false;
  if (res.Type === 200) {
    ElNotification.success("更新成功");
    emit("submit");
  } else {
    ElNotification.error("更新失败");
  }
}
onMounted(async () => {
  const data = JSON.parse(JSON.stringify(props.mission));
  Object.assign(formData, data);
  formData.ShowImg ??= "";
  isOriginal.value = data.Source ? false : true;

  // 暂时解决 WangEditor 无法获取到内容的问题
  formData.Text = "";
  await nextTick();
  formData.Text = data.Text;

  // 设置科别
  selectedDepartments.value = props.departments.filter(
    (item) => item.Id && formData.Depts?.includes(item.Id)
  );

  // 设置疾病
  selectedDiseases.value = props.diseases.filter(
    (item) => item.Id && formData.Diseases?.includes(item.Id)
  );

  kEnableDebug && console.debug("RecoveryMissionForm - onMounted", formData);
});
</script>

<style lang="scss" scoped></style>
