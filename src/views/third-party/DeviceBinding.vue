<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="query" inline @submit.prevent="onSubmit">
              <el-form-item label="设备编码">
                <el-input v-model="query.keywords" type="text" placeholder="请输入设备编码" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" native-type="submit">搜索</el-button>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" @click="dialogVisible = true">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          :height="tableFluidHeight"
          border
          stripe
          show-overflow-tooltip
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="DeviceTypeName" label="设备类型" />
          <el-table-column prop="DeviceCode" label="设备编码" />
          <el-table-column prop="UserName" label="绑定用户" />
          <el-table-column
            prop="IsSend"
            label="是否发送到物联"
            :formatter="(row, countColumn, cellValue) => (cellValue ? '是' : '否')"
          />
          <el-table-column prop="CreatedTime" label="绑定时间" :formatter="formatDate" />
          <el-table-column fixed="right" label="操作">
            <template #default="{ row }">
              <el-button link type="danger" @click="unbind(row)">解绑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="pages.pageIndex"
          v-model:limit="pages.pageSize"
          @pagination="loadData"
        />
      </template>
    </BaseTableSearchContainer>

    <el-dialog
      v-model="dialogVisible"
      title="添加设备绑定"
      :close-on-click-modal="false"
      destroy-on-close
      align-center
      @close="resetAddForm"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="auto"
        label-position="right"
        :disabled="submitting"
        @submit.prevent="onSubmitAddForm"
      >
        <el-form-item label="绑定用户" prop="UserId" trigger="change">
          <UserSelect
            ref="userSelectRef"
            v-model="addForm.UserId"
            placeholder="请输入用户姓名/手机号搜索"
          />
          <!-- <el-select
            v-model="addForm.Id"
            filterable
            remote
            reserve-keyword
            placeholder="请输入用户姓名/手机号搜索"
            :remote-method="remoteMethod"
            :loading="userListLoading"
            @change="selectUserChange"
          >
            <el-option
              v-for="item in userList"
              :key="item.Id"
              :label="(item.Name || item.NickName) + ':' + item.PhoneNumber"
              :value="item.Id"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="设备类型" prop="DeviceType" trigger="change">
          <el-select v-model="addForm.DeviceType" placeholder="请选择设备类型">
            <el-option
              v-for="item in deviceTypeList"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备编码" prop="Code">
          <el-input v-model="addForm.Code" placeholder="请输入设备编码" />
        </el-form-item>
        <div class="text-right">
          <el-button type="primary" @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" native-type="submit">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Training_Api from "@/api/training";
import dayjs from "dayjs";
import Content_Api from "@/api/content";
import { useUserStoreHook } from "@/store";
import UserSelect from "@/components/UserSelect/index.vue";
import type { FormInstance } from "element-plus";
import type { BindingDeviceInputDTO } from "@/api/training/types";

interface DeviceBindingItem {
  Id: string;
  DeviceTypeName: string;
  DeviceCode: string;
  UserName: string;
  IsSend: boolean;
  CreatedTime: string;
}
interface DeviceTypeItem {
  Id: string;
  Name: string;
  Code: string;
  DeviceFactory: string;
}
export default {
  setup() {
    const query = reactive({
      keywords: "",
    });

    const pages = reactive({
      pageIndex: 1,
      pageSize: 20,
      pageSizes: [10, 20, 50, 100],
    });

    const { kTableRef, tableLoading, pageData, total, tableResize, tableFluidHeight } =
      useTableConfig<DeviceBindingItem>();

    const dialogVisible = ref(false);
    const submitting = ref(false);
    const userSelectRef = useTemplateRef<InstanceType<typeof UserSelect>>("userSelectRef");
    const selectedUser = computed(() => {
      if (userSelectRef.value && userSelectRef.value.userOptions.length > 0) {
        return userSelectRef.value.userOptions[0];
      }
      return null;
    });
    const deviceTypeList = ref<DeviceTypeItem[]>([]);
    const deviceType = computed(() => {
      return deviceTypeList.value.find((item) => item.Id === addForm.DeviceType);
    });
    const addFormRef = useTemplateRef<FormInstance>("addFormRef");
    const addForm = reactive({
      UserId: "",
      DeviceType: "",
      Code: "",
    });
    const addFormRules = {
      UserId: [
        {
          required: true,
          message: "请选择用户",
          trigger: "blur",
        },
      ],
      DeviceType: [
        {
          required: true,
          message: "请选择设备类型",
          trigger: "blur",
        },
      ],
      Code: [
        {
          required: true,
          message: "请输入设备编码",
          trigger: "blur",
        },
      ],
    };

    return {
      query,
      pages,
      kTableRef,
      tableLoading,
      pageData,
      total,
      tableResize,
      tableFluidHeight,
      userSelectRef,
      addFormRef,
      dialogVisible,
      submitting,
      deviceTypeList,
      addForm,
      deviceType,
      addFormRules,
      selectedUser,
    };
  },
  mounted() {
    this.loadData();
    this.loadDeviceTypeList();
  },
  methods: {
    resetAddForm() {
      this.addFormRef?.resetFields();
    },
    onSubmitAddForm() {
      this.addFormRef!.validate((valid: boolean, invalidFields: any) => {
        if (!valid) {
          console.warn("表单验证失败", invalidFields);
          return;
        }
        this.bindDevice();
      });
    },
    onSubmit() {
      this.loadData();
    },
    async loadDeviceTypeList() {
      const res = await Content_Api.getInstrumentsPageData({
        page: 1,
        pageSize: 999,
        isEnable: true,
        isIOTDevice: true,
      });
      if (res.Type != 200) {
        console.error(res.Content);
        return;
      }
      this.deviceTypeList = res.Data.Data;
    },
    formatDate(row: DeviceBindingItem, column: any, cellValue: string, index: number) {
      if (!cellValue) return "--";
      return dayjs(cellValue).format("YYYY-MM-DD HH:mm");
    },
    async loadData() {
      this.tableLoading = true;
      const res = await Training_Api.getDeviceList({
        pageIndex: this.pages.pageIndex,
        pageSize: this.pages.pageSize,
        keywords: this.query.keywords,
      });
      this.tableLoading = false;

      if (res.Type != 200) {
        ElMessage.error(res.Content);
        return;
      }

      this.pageData = res.Data.Data;
      this.total = res.Data.Total;
    },
    async loadUserList(keyword: string) {
      console.log(keyword);
      // this.loading = false;
      // const res = await GetUserInfoByKeyword(keyword);
      // if (res.Type === 200) {
      //   this.options = res.Data;
      // }
    },
    // remoteMethod(query: string) {
    //   if (query !== "") {
    //     this.userListLoading = true;
    //     // debounce(
    //     this.loadUserList(query);
    //     // );
    //   } else {
    //     this.userList = [];
    //   }
    // },
    unbind(row: DeviceBindingItem) {
      ElMessageBox.confirm("此操作将解绑该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await Training_Api.unBindingUserDevice({ id: row.Id });
        if (res.Type != 200) {
          ElNotification.error(res.Content);
          return;
        }

        ElNotification.success(res.Content ?? "操作成功");
        this.loadData();
      });
    },

    async bindDevice() {
      const bindingData = {
        DeviceCode: this.addForm.Code,
        DeviceTypeCode: this.deviceType!.Code!,
        DeviceFactory: this.deviceType!.DeviceFactory!,
        MenberId: this.addForm.UserId,
      };

      this.submitting = true;
      // 检查是否绑定多个相同类型的设备
      const res0 = await Training_Api.checkIsBindingUser([bindingData]);
      if (res0.Type != 200) {
        this.submitting = false;
        ElMessage.error(res0.Content);
        return;
      }
      if (!res0.Data) {
        ElMessage.warning("已绑定相同类型的设备,不能重复绑定");
        this.submitting = false;
        return;
      }

      const userId = useUserStoreHook().userInfo.Id;
      const data: BindingDeviceInputDTO = {
        ...bindingData,
        UserId: userId, // 操作人（登陆人）
        UserName: this.selectedUser?.Name ?? undefined,
        Birthday: this.selectedUser?.Birthday ?? undefined,
        Sex: this.selectedUser?.Sex ?? undefined,
      };

      const res = await Training_Api.bindingUserDevice([data]);
      if (res.Type != 200) {
        ElMessage.error(res.Content);
        this.submitting = false;
        return;
      }

      ElNotification.success("设备绑定成功");
      this.dialogVisible = false;
      this.submitting = false;
      this.resetAddForm();

      this.pages.pageIndex = 1;
      this.loadData();
    },
  },
};
</script>
<style scoped lang="scss"></style>
