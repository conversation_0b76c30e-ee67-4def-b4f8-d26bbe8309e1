<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="机构">
                <HospitalSelect v-model="queryParams.sourceOrganizationNameKeyword" keyId="Name" />
              </el-form-item>
              <el-form-item label="是否认证">
                <el-select
                  v-model="queryParams.hasUserAuth"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否测试数据">
                <el-select
                  v-model="queryParams.isTest"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" value="是" />
                  <el-option label="否" value="否" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="keyword">
                <el-input
                  v-model="queryParams.keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="!pageData.length"
              :loading="exportLoading"
              @click="handleExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Name" label="姓名" align="center" />
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column prop="Age" label="年龄" align="center" />
          <el-table-column label="认证时间" prop="AuthTime" align="center" width="180">
            <template #default="scope">
              {{ dateFormat(scope.row.AuthTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="HasVisitedRegister" label="是否报道" align="center" />
          <el-table-column prop="ConsultFinishCount" label="问诊次数" align="center" />
          <el-table-column prop="TreatFinishCount" label="咨询次数" align="center" />
          <el-table-column prop="PhoneNumber" label="注册电话" align="center" width="180" />
          <el-table-column label="获客方式" prop="inviteWay" align="center">
            <template #default="scope">
              {{ scope.row.inviteWay }}
            </template>
          </el-table-column>
          <el-table-column label="获客角色" prop="inviteRole" align="center">
            <template #default="scope">
              {{ scope.row.inviteRole }}
            </template>
          </el-table-column>
          <el-table-column label="机构" prop="inviterOrganization" align="center">
            <template #default="scope">
              {{ scope.row.inviterOrganization }}
            </template>
          </el-table-column>
          <el-table-column label="医院级别" prop="organizationLevel" align="center">
            <template #default="scope">
              {{ scope.row.organizationLevel }}
            </template>
          </el-table-column>
          <el-table-column label="医生/治疗师" prop="inviterUser" align="center">
            <template #default="scope">
              {{ scope.row.inviterUser }}
            </template>
          </el-table-column>
          <el-table-column prop="CreatedTime" label="注册时间" align="center" width="180">
            <template #default="scope">
              {{ dateFormat(scope.row.CreatedTime) }}
            </template>
          </el-table-column>
          <el-table-column label="是否测试数据" align="center" prop="IsTest" width="100">
            <template #default="scope">
              {{ scope.row.IsTest }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { EpPropMergeTypeWithNull } from "element-plus";
import Report_Api from "@/api/report";
import { useUserStore } from "@/store";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import { ExportTaskRedashDTO, PatientList } from "@/api/report/types";
import { ExportEnum } from "@/enums/Other";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

const userStore = useUserStore();

defineOptions({
  name: "UserQuery",
  inheritAttrs: false,
});

interface QueryParams {
  LoginUserId: string;
  endTime: string;
  startTime: string;
  hasUserAuth: EpPropMergeTypeWithNull<string>;
  isTest: EpPropMergeTypeWithNull<string>;
  keyword: EpPropMergeTypeWithNull<string>;
  sourceOrganizationNameKeyword: EpPropMergeTypeWithNull<string>;
  PageIndex: number;
  PageSize: number;
}

const queryParams = ref<QueryParams>({
  LoginUserId: "",
  endTime: dayjs().format("YYYY-MM-DD 23:59:59"),
  startTime: dayjs().format("YYYY-MM-01 00:00:00"),
  hasUserAuth: null,
  isTest: null,
  keyword: null,
  sourceOrganizationNameKeyword: null,
  PageIndex: 1,
  PageSize: 10,
});

const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);
const exportLoading = ref<boolean>(false);
const queryResultId = ref<number>(0);
const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<PatientList>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  queryParams.value.LoginUserId = userStore.userInfo.Id;
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  const params = convertToRedashParams<QueryParams>(copyData, "Report_PatientList");
  const res = await Report_Api.getRedashList<PatientList>(params);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
    queryResultId.value = res.Data.QueryResultId;
  }
};

const dateFormat = (time: string) => {
  if (!time) return "";
  return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
};

const handleExport = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  const exportParams = convertToRedashParams<QueryParams>(copyData, "Report_PatientList");
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `普通用户查询-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId.value,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.value.PageIndex,
    PageSize: queryParams.value.PageSize,
    QueryName: "Report_PatientList",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
};

onActivated(() => {
  handleGetTableList();
});

watch(timeRange, (newVal) => {
  queryParams.value.startTime = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.endTime = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});
</script>

<style scoped lang="scss"></style>
