<template>
  <div v-loading="formLoading" class="px-20px pt-10px pb-20px overflow-y-auto max-h-600px">
    <el-form :ref="kFormRef" :model="formData" :rules="rules" label-width="100px" :inline="true">
      <el-form-item label="医联体名称" prop="Name">
        <el-input v-model="formData.Name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item
        v-for="(phone, index) in formData.AdminPhoneList"
        :key="index"
        :label="'管理员手机号' + (index + 1)"
        :prop="'AdminPhoneList[' + index + ']'"
      >
        <el-input
          v-model="formData.AdminPhoneList![index]"
          type="number"
          clearable
          class="mr-10px"
        />
        <div
          v-if="formData.AdminPhoneList!.length > 1"
          class="px-5px leading-1"
          @click="removeAdminPhone(index)"
        >
          <el-icon size="20">
            <RemoveFilled />
          </el-icon>
        </div>
        <div
          v-if="index === formData.AdminPhoneList!.length - 1"
          class="px-5px leading-1"
          @click="addAdminPhone"
        >
          <el-icon size="20">
            <CirclePlusFilled />
          </el-icon>
        </div>
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { FormRules, FormInstance } from "element-plus";
import { json } from "stream/consumers";

interface ConsortiumFormData extends Consortium {
  AdminPhoneList?: string[];
}

const kEnableDebug = false;
const kFormRef = "ruleFormRef";
const props = defineProps<{
  data: Consortium;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [Consortium[]];
}>();

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.data));
  Object.assign(formData, data);
  formData.AdminPhoneList = JSON.parse(data.AdminPhones ?? "[]");
  if (!formData.AdminPhoneList?.length) {
    formData.AdminPhoneList = [""];
  }
});

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = reactive<ConsortiumFormData>({});

// 表单验证规则
const rules = reactive<FormRules<Consortium>>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
});

// 删除管理员手机号
function removeAdminPhone(index: number) {
  formData.AdminPhoneList!.splice(index, 1);
}

// 添加管理员手机号
function addAdminPhone() {
  formData.AdminPhoneList!.push("");
}

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      formLoading.value = true;
      const r = await requestAddOrUpdateData();
      formLoading.value = false;
      if (r.Type === 200) {
        emit("submit", r.Data);
      } else {
        ElMessage.error(r.Content);
      }
    } else {
      kEnableDebug && console.debug("提交失败", fields, formData);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("requestAddOrUpdateData", formData);

  let params = {
    ...formData,
    AdminPhones: JSON.stringify(formData.AdminPhoneList?.filter((phone) => phone.trim())),
  };
  delete params.AdminPhoneList;
  if (props.data.Id) {
    const r = await Passport_Api.updateConsortium([params]);
    return {
      Type: r.Type,
      Data: [params],
      Content: r.Content,
    };
  }

  const r = await Passport_Api.addConsortium([params]);
  return r;
}
</script>

<style lang="scss" scoped></style>
