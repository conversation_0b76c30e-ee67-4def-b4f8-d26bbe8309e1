<template>
  <el-transfer
    v-model="selectIds"
    filterable
    :filter-method="filterMethod"
    :titles="['待选角色', '已选角色']"
    :data="showRoleList"
    :props="{
      key: 'Id',
      label: 'Name',
      disabled: 'Disabled',
    }"
  />
</template>

<script setup lang="ts">
import { TransferDataItem } from "element-plus";

interface PageBaseRole extends BaseRole {
  Disabled?: boolean;
}

const roleList = inject<Ref<PageBaseRole[]>>("roleList")!;
const showRoleList = computed(() => {
  return roleList.value.map((s) => {
    s.Disabled = hospitalRoles.includes(s.RoleType!);
    return s;
  });
});
const selectIds = ref<string[]>([]);
const hospitalRoles = [
  // 院长
  "director",
  // 运营
  "organizationOperate",
  "doctor",
  "patient",
  "therapist",
  "nurse",
  // 管理员
  "organizationAdmin",
  "officesDirector",
  "consortiumAdmin",
];

const filterMethod = (query: string, item: TransferDataItem) => {
  if (!item) return false;
  return item.Name?.toLowerCase().includes(query.toLowerCase());
};
const handleSubmit = (): string[] => {
  return selectIds.value;
};

interface Props {
  roleIds: string[] | null;
}
const props = defineProps<Props>();
watch(
  () => props.roleIds,
  (newVal) => {
    selectIds.value = newVal || [];
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
