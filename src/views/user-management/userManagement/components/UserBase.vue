<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
    scroll-to-error
    :disabled="isPreview"
  >
    <!-- 基本信息行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="姓名" prop="Name">
          <el-input v-model="formData.Name" type="text" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="用户名" prop="UserName">
          <el-input v-model="formData.UserName" type="text" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 昵称和编码行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="昵称">
          <el-input v-model="formData.NickName" type="text" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="编码">
          <el-input v-model="formData.Code" type="text" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 性别和生日行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="性别" prop="Sex">
          <el-select v-model="formData.Sex" placeholder="性别" style="width: 100%">
            <el-option
              v-for="item in sexDictList"
              :key="item.Key"
              :label="item.Key"
              :value="item.Value!"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生日">
          <el-date-picker
            v-model="formData.Birthday"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 手机号行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="手机号" prop="PhoneNumber">
          <el-input v-model="formData.PhoneNumber" type="text" />
        </el-form-item>
      </el-col>
      <el-col v-if="!formData.Id" :span="12">
        <el-form-item label="密码" prop="Password">
          <el-input v-model="formData.Password" type="password" show-password />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 开关状态行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="是否启用">
          <el-switch v-model="formData.IsEnabled" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="是否锁定">
          <el-switch v-model="formData.IsLocked" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 图片上传区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="头像">
          <SingleImageUpload v-model="formData.HeadImg" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="微信二维码" label-width="120px">
          <SingleImageUpload v-model="formData.UserExternalIdentify.WeChatQrCode" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { CreateUserInputDTO } from "@/api/passport/types";
import { FormInstance, FormRules } from "element-plus";

const formData = ref<CreateUserInputDTO>({
  Id: "",
  Name: "",
  UserName: "",
  NickName: "",
  Sex: "",
  Birthday: "",
  Code: "",
  PhoneNumber: "",
  Password: "",
  IsEnabled: true,
  IsLocked: false,
  HeadImg: "",
  UserExternalIdentify: {
    WeChatQrCode: "",
  },
});
const formRef = ref<FormInstance>();
const isPreview = inject("isPreview") as Ref<boolean>;
const sexDictList = inject("sexDictList") as Ref<ReadDict[]>;

const rules = reactive<FormRules>({
  Name: [{ required: true, message: "请输入名字", trigger: "blur" }],
  UserName: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: "用户名只能包含英文字母和数字",
      trigger: "blur",
    },
  ],
  PhoneNumber: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号格式",
      trigger: "blur",
    },
  ],
  Password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    {
      min: 6,
      message: "密码至少需要6位",
      trigger: "blur",
    },
  ],
});

const handleSubmit = async (): Promise<CreateUserInputDTO | null> => {
  if (!formRef.value) return null;
  try {
    await formRef.value.validate();
    return formData.value;
  } catch (error) {
    return null;
  }
};

interface Props {
  userInfo: CreateUserInputDTO | null;
}
const props = defineProps<Props>();
watch(
  () => props.userInfo,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue = JSON.parse(JSON.stringify(newVal));
    formData.value = copyValue;
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
.el-form {
  .el-row {
    margin-bottom: 8px;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-divider {
    margin: 24px 0 16px 0;

    :deep(.el-divider__text) {
      font-weight: 500;
      color: #606266;
    }
  }

  // 确保开关组件在表单项中垂直居中
  .el-form-item__content {
    .el-switch {
      margin-top: 2px;
    }
  }
}
</style>
