<template>
  <div class="organization-content">
    <!-- 职业信息表单 -->
    <el-card class="form-card" shadow="never">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        class="doctor-form"
        :disabled="isPreview"
      >
        <!-- 基本信息区域 -->
        <div class="info-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="16">
            <!-- 主要执业机构 - 可编辑 -->
            <el-col :span="12">
              <el-form-item label="主要执业机构" prop="PracticeOrganizationName">
                <el-input
                  v-model="doctorInfo.PracticeOrganizationName"
                  placeholder="请输入主要执业机构"
                  :disabled="isPreview"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 现任职机构 - 只读 -->
            <el-col :span="12">
              <el-form-item label="现任职机构">
                <div class="readonly-field">{{ doctorInfo.OrganizationName || "暂无" }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <!-- 科室信息 -->
            <el-col :span="8">
              <el-form-item label="科室">
                <div class="readonly-field">{{ doctorInfo.DeptName || "暂无" }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他科室">
                <div class="readonly-field">{{ doctorInfo.OtherDeptName || "暂无" }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="人员类型">
                <div class="readonly-field">{{ doctorInfo.WorkerType || "暂无" }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <!-- 执业信息 -->
            <el-col :span="8">
              <el-form-item label="执业级别">
                <div class="readonly-field">{{ doctorInfo.PracticeLevel || "暂无" }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="执业类型">
                <div class="readonly-field">{{ doctorInfo.PracticeType || "暂无" }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职称">
                <div class="readonly-field">{{ doctorInfo.WorkerTitle || "暂无" }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <!-- 状态和角色 -->
            <el-col :span="8">
              <el-form-item label="审核状态">
                <el-tag :type="doctorInfo.State === '已审核' ? 'success' : 'warning'" size="small">
                  {{ doctorInfo.State || "暂无" }}
                </el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="角色">
                <div class="readonly-field">{{ doctorInfo.RoleNames || "暂无" }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工作年限">
                <div class="readonly-field">{{ doctorInfo.ClinicalWorkYears || "暂无" }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 详细信息区域 -->
        <div class="info-section">
          <h4 class="section-title">详细信息</h4>
          <el-row :gutter="16">
            <!-- 擅长 - 可编辑 -->
            <el-col :span="12">
              <el-form-item label="专业擅长" prop="Skilled">
                <el-input
                  v-model="doctorInfo.Skilled"
                  type="textarea"
                  :rows="4"
                  placeholder="请描述医生的专业擅长领域"
                  :disabled="isPreview"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <!-- 个人简介 - 可编辑 -->
            <el-col :span="12">
              <el-form-item label="个人简介" prop="Abstract">
                <el-input
                  v-model="doctorInfo.Abstract"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入个人简介"
                  :disabled="isPreview"
                  maxlength="1000"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 标签管理区域 -->
        <div class="info-section">
          <h4 class="section-title">标签管理</h4>
          <el-row :gutter="16">
            <!-- 擅长标签 - 可编辑 -->
            <el-col :span="12">
              <el-form-item label="擅长标签" prop="SkilledTags">
                <el-input
                  v-model="doctorInfo.SkilledTags"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入擅长标签，多个标签请用逗号分隔"
                  :disabled="isPreview"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <!-- 简介标签 - 可编辑 -->
            <el-col :span="12">
              <el-form-item label="简介标签" prop="AbstractTags">
                <el-input
                  v-model="doctorInfo.AbstractTags"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入简介标签，多个标签请用逗号分隔"
                  :disabled="isPreview"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, watch } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { dayjs } from "element-plus";
import { OrganizationAuthentication } from "@/api/passport/types";

// 注入的属性
const isPreview = inject("isPreview", ref(false));
const workerTitleDictList = inject("workerTitleDictList", ref<ReadDict[]>([]));
const workerTypeDictList = inject("workerTypeDictList", ref<ReadDict[]>([]));

// 响应式数据
const formRef = ref<FormInstance>();

// 医生基本信息
const doctorInfo = ref({
  WorkerTitle: "",
  DeptName: "",
  WorkerType: "",
  PracticeLevel: "",
  PracticeType: "",
  PracticeRange: "",
  PracticeOrganizationName: "",
  PracticeOrganizationCode: "",
  OrganizationName: "",
  RoleNames: "",
  State: "",
  OtherDeptName: "",
  ClinicalWorkYears: "",
  Skilled: "",
  Abstract: "",
  SkilledTags: "",
  AbstractTags: "",
});

// 表单数据
const formData = reactive({
  practiceOrganization: "北京协和医院",
  skilled: "心血管疾病诊治、冠心病介入治疗、心律失常诊治",
  profileDescription:
    "从事心血管内科临床工作15年，擅长冠心病、高血压、心律失常等疾病的诊治，具有丰富的临床经验。",
  skilledTags: "冠心病，高血压，心律失常，心力衰竭",
  profileTags: "经验丰富，技术精湛，耐心细致",
});

// 表单验证规则
const rules: FormRules = {
  practiceOrganization: [{ required: true, message: "请输入主要执业机构", trigger: "blur" }],
  skilled: [
    { required: true, message: "请输入擅长领域", trigger: "blur" },
    { max: 500, message: "擅长领域不能超过500个字符", trigger: "blur" },
  ],
  profileDescription: [
    { required: true, message: "请输入个人简介", trigger: "blur" },
    { max: 1000, message: "个人简介不能超过1000个字符", trigger: "blur" },
  ],
};

const handleProcessData = (newVal: OrganizationAuthentication) => {
  const workerTypeItem = workerTypeDictList.value.find((s) => s.Value === newVal.WorkerType);
  if (workerTypeItem) {
    doctorInfo.value.WorkerType = workerTypeItem.Key!;
  }
  const workerTitleItem = workerTitleDictList.value.find((s) => s.Value === newVal.WorkerTitle);
  if (workerTitleItem) {
    doctorInfo.value.WorkerTitle = workerTitleItem.Key!;
  }
  const deptItem = newVal.UserDepartments.find((s) => s.IsMain);
  if (deptItem) {
    doctorInfo.value.DeptName = deptItem.DeptName;
  }
  doctorInfo.value.PracticeLevel = newVal.PracticeLevel;
  doctorInfo.value.PracticeType = newVal.PracticeType;
  doctorInfo.value.PracticeRange = newVal.PracticeRange;
  doctorInfo.value.PracticeOrganizationName = newVal.PracticeOrganizationName;
  doctorInfo.value.PracticeOrganizationCode = newVal.PracticeOrganizationCode;
  doctorInfo.value.OrganizationName = newVal.OrganizationName;
  doctorInfo.value.RoleNames = newVal.RoleNames.join(",");
  doctorInfo.value.State = newVal.State === 0 ? "审核中" : newVal.State === 1 ? "已审核" : "";
  const otherDeptItem = newVal.UserDepartments.filter((s) => !s.IsMain);
  if (otherDeptItem) {
    doctorInfo.value.OtherDeptName = otherDeptItem.map((s) => s.DeptName).join(",");
  }
  doctorInfo.value.ClinicalWorkYears = newVal.UserWork?.ClinicalWorkYears || "";
  doctorInfo.value.Skilled = newVal.Skilled;
  doctorInfo.value.Abstract = newVal.Abstract;
  if (newVal.SkilledTags) {
    doctorInfo.value.SkilledTags = JSON.parse(newVal.SkilledTags).join("\n");
  }
  if (newVal.AbstractTags) {
    doctorInfo.value.AbstractTags = JSON.parse(newVal.AbstractTags).join("\n");
  }
};

const handleSubmit = (): OrganizationAuthentication => {
  const copyData = JSON.parse(JSON.stringify(props.organizationInfo));
  const params = {
    ...copyData,
    PracticeOrganizationName: doctorInfo.value.PracticeOrganizationName,
    Skilled: doctorInfo.value.Skilled,
    Abstract: doctorInfo.value.Abstract,
    SkilledTags: JSON.stringify(
      doctorInfo.value.SkilledTags.split(/[;；\n]/)
        .filter((v) => v)
        .map((v) => v.trim())
    ),
    AbstractTags: JSON.stringify(
      doctorInfo.value.AbstractTags.split(/[;；\n]/)
        .filter((v) => v)
        .map((v) => v.trim())
    ),
  };
  return params;
};

interface Props {
  organizationInfo: OrganizationAuthentication | null;
}
const props = defineProps<Props>();
watch(
  () => props.organizationInfo,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    handleProcessData(newVal);
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
.organization-content {
  .form-card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;

    :deep(.el-card__header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;
      padding: 16px 24px;
    }

    .card-header {
      .card-title {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;

        .el-icon {
          font-size: 20px;
          color: #ffffff;
        }
      }
    }

    :deep(.el-card__body) {
      padding: 24px;
    }

    .doctor-form {
      .info-section {
        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          margin: 0 0 20px 0;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          padding-bottom: 8px;
          border-bottom: 2px solid #e8f4fd;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2);
          }
        }
      }

      :deep(.el-form-item) {
        margin-bottom: 18px;

        .el-form-item__label {
          font-weight: 500;
          color: #34495e;
          font-size: 14px;
        }
      }

      :deep(.el-input__wrapper) {
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;

        &:hover {
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }

        &.is-focus {
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
      }

      :deep(.el-textarea__inner) {
        border-radius: 6px;
        border: 1px solid #d1d5db;
        transition: all 0.2s ease;

        &:hover {
          border-color: #9ca3af;
        }

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
      }

      :deep(.el-tag) {
        border-radius: 4px;
        font-weight: 500;
      }

      .form-actions {
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid #e9ecef;
        text-align: center;

        .el-button {
          min-width: 100px;
          margin: 0 8px;
          border-radius: 6px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .organization-content {
    .form-card .doctor-form {
      .info-section {
        .el-row {
          .el-col {
            &[class*="span-6"] {
              flex: 0 0 50%;
              max-width: 50%;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .organization-content {
    .form-card {
      :deep(.el-card__header) {
        padding: 12px 16px;
      }

      :deep(.el-card__body) {
        padding: 16px;
      }

      .card-header .card-title {
        font-size: 16px;
      }

      .doctor-form {
        :deep(.el-form-item__label) {
          width: 80px !important;
          font-size: 13px;
        }

        .info-section {
          .section-title {
            font-size: 14px;
            margin-bottom: 16px;
          }
        }

        .el-row .el-col {
          margin-bottom: 12px;

          &[class*="span-"] {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }

        :deep(.el-form-item) {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .organization-content {
    padding: 8px;

    .form-card {
      .doctor-form {
        :deep(.el-form-item__label) {
          width: 70px !important;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
