<template>
  <div v-if="props.baseUserInfo" class="user-certification-container">
    <!-- 用户头像和基本信息卡片 -->
    <el-card class="user-profile-card" shadow="never">
      <div class="profile-header">
        <div class="avatar-section">
          <el-avatar :size="80" class="user-avatar">
            <el-image
              v-if="props.baseUserInfo.HeadImg"
              :src="props.baseUserInfo.HeadImg"
              alt="用户头像"
              fit="cover"
            />
            <el-icon v-else :size="40"><User /></el-icon>
          </el-avatar>
        </div>
        <div class="profile-info">
          <div class="user-name">{{ props.baseUserInfo.Name }}</div>
          <div class="user-details">
            <span class="detail-item">
              <el-icon><User /></el-icon>
              {{ props.baseUserInfo.UserName }}
            </span>
            <span class="detail-item">
              <el-icon><Phone /></el-icon>
              {{ props.baseUserInfo.PhoneNumber }}
            </span>
          </div>
        </div>
        <div class="status-section">
          <el-tag :type="getStatusType(props.baseUserInfo.State)" size="large" class="status-tag">
            {{ getStatusText(props.baseUserInfo.State) }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 详细信息卡片 -->
    <el-card class="user-details-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><InfoFilled /></el-icon>
          <span>详细信息</span>
        </div>
      </template>

      <div class="details-content">
        <!-- 基本信息行 -->
        <el-row :gutter="24" class="info-row">
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">姓名</label>
              <div class="info-value">{{ props.baseUserInfo.Name }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">用户名</label>
              <div class="info-value">{{ props.baseUserInfo.UserName }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">昵称</label>
              <div class="info-value">{{ props.baseUserInfo.NickName || "暂无" }}</div>
            </div>
          </el-col>
        </el-row>

        <!-- 个人信息行 -->
        <el-row :gutter="24" class="info-row">
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">性别</label>
              <div class="info-value">
                <el-tag :type="props.baseUserInfo.Sex === '男' ? 'primary' : 'danger'" size="small">
                  {{ props.baseUserInfo.Sex }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">出生日期</label>
              <div class="info-value">{{ formatDate(props.baseUserInfo.Birthday) }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">年龄</label>
              <div class="info-value">{{ calculateAge(props.baseUserInfo.Birthday) }}岁</div>
            </div>
          </el-col>
        </el-row>

        <!-- 联系信息行 -->
        <el-row :gutter="24" class="info-row">
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">手机号码</label>
              <div class="info-value phone-number">
                <el-icon><Phone /></el-icon>
                {{ props.baseUserInfo.PhoneNumber }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>

  <!-- 空状态 -->
  <div v-else class="empty-state">
    <el-empty description="暂无用户信息" />
  </div>
</template>

<script setup lang="ts">
import { PagePatientCertificationUserInfo } from "../types";
import { PatientCertificationState } from "@/enums/UserEnum";
import { User, Phone, InfoFilled } from "@element-plus/icons-vue";
import dayjs from "dayjs";

interface Props {
  baseUserInfo: PagePatientCertificationUserInfo | null;
}
const props = defineProps<Props>();

// 获取状态类型
const getStatusType = (state: number): "success" | "warning" | "info" | "primary" | "danger" => {
  switch (state.toString()) {
    case PatientCertificationState.PendingReview:
      return "warning";
    case PatientCertificationState.Approved:
      return "success";
    case PatientCertificationState.Rejected:
      return "danger";
    default:
      return "info";
  }
};

// 获取状态文本
const getStatusText = (state: number): string => {
  switch (state) {
    case 0:
      return "待审核";
    case 2:
      return "审核通过";
    case 3:
      return "已拒绝";
    default:
      return "未知状态";
  }
};

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return "暂无";
  return dayjs(dateString).format("YYYY-MM-DD");
};

// 计算年龄
const calculateAge = (birthday: string): number => {
  if (!birthday) return 0;
  return dayjs().diff(dayjs(birthday), "year");
};
</script>

<style lang="scss" scoped>
.user-certification-container {
  padding: 0;

  .user-profile-card {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .profile-header {
      display: flex;
      align-items: center;
      gap: 20px;
      padding: 8px 0;

      .avatar-section {
        flex-shrink: 0;

        .user-avatar {
          border: 3px solid #f0f2f5;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          .el-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
      }

      .profile-info {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
          line-height: 1.2;
        }

        .user-details {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;

          .detail-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #606266;
            font-size: 14px;

            .el-icon {
              color: #909399;
              font-size: 16px;
            }
          }
        }
      }

      .status-section {
        flex-shrink: 0;

        .status-tag {
          font-weight: 500;
          padding: 8px 16px;
          border-radius: 20px;
        }
      }
    }
  }

  .user-details-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      .el-icon {
        color: #409eff;
        font-size: 18px;
      }
    }

    .details-content {
      .info-row {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          display: flex;
          align-items: center;
          gap: 10px;

          .info-label {
            display: block;
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }

          .info-value {
            font-size: 15px;
            color: #303133;
            font-weight: 400;
            min-height: 22px;
            display: flex;
            align-items: center;

            &.phone-number {
              gap: 6px;

              .el-icon {
                color: #409eff;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

// 响应式设计
@media (max-width: 768px) {
  .user-certification-container {
    .user-profile-card {
      .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;

        .profile-info {
          .user-details {
            justify-content: center;
          }
        }
      }
    }

    .user-details-card {
      .details-content {
        .info-row {
          .el-col {
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}
</style>
