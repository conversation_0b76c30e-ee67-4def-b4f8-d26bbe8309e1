<template>
  <el-tabs v-model="activeName" type="card">
    <el-tab-pane label="基本信息" name="user">
      <BaseCertification :base-user-info="baseUserInfo" />
    </el-tab-pane>
    <el-tab-pane label="身份认证" name="identity">
      <IdentityContent
        :user-certificates="props.patientCertificationInfo?.UserCertificates!"
        :user-id="props.patientCertificationInfo?.UserId!"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { PatientCertificationItem } from "@/api/passport/types";
import { PagePatientCertificationUserInfo } from "../types";
import BaseCertification from "./BaseCertification.vue";

const activeName = ref("user");
const baseUserInfo = ref<PagePatientCertificationUserInfo | null>(null);
const onProcessPatientCertificationInfo = (patientCertificationInfo: PatientCertificationItem) => {
  baseUserInfo.value = {
    HeadImg: patientCertificationInfo.HeadImg!,
    Name: patientCertificationInfo.Name!,
    UserName: patientCertificationInfo.UserName!,
    NickName: patientCertificationInfo.NickName!,
    Sex: patientCertificationInfo.Sex!,
    Birthday: patientCertificationInfo.Birthday!,
    PhoneNumber: patientCertificationInfo.PhoneNumber!,
    State: patientCertificationInfo.WorkflowStatus!,
  };
};

interface Props {
  patientCertificationInfo: PatientCertificationItem | null;
}
const props = defineProps<Props>();
watch(
  () => props.patientCertificationInfo,
  (newVal) => {
    if (!newVal) {
      return;
    }
    // 获取用户的所有信息
    onProcessPatientCertificationInfo(newVal);
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped></style>
