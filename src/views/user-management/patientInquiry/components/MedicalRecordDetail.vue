<template>
  <div
    v-loading="loading"
    class="overflow-y-auto h-600px p-20px flex flex-col justify-start items-stretch"
  >
    <el-row class="mb-20px">
      <el-text class="flex-1">姓名：{{ data.Name }}</el-text>
      <el-text class="flex-1">性别：{{ data.Sex }}</el-text>
      <el-text class="flex-1">年龄：{{ ageFormat(data.Age) }}</el-text>
      <div v-if="!isSelfBuild" class="flex-1" />
    </el-row>
    <el-row class="mb-10px">
      <el-text class="flex-1">就诊号：{{ data.ConsultId }}</el-text>
      <el-text v-if="!isSelfBuild" class="flex-1">就诊科室：{{ data.DepartmentName }}</el-text>
      <el-text class="flex-1">就诊医生：{{ data.DoctorName }}</el-text>
      <el-text class="flex-1">就诊时间：{{ data.InDate }}</el-text>
    </el-row>
    <template v-if="!isSelfBuild">
      <div>
        <h4 class="mb-10px">主诉</h4>
        <el-text>{{ data.ChiefComplaint }}</el-text>
      </div>
      <div>
        <h4 class="mb-10px">既往史</h4>
        <el-text>{{ data.PastHistory }}</el-text>
      </div>
      <div>
        <h4 class="mb-10px">现病史</h4>
        <el-text>{{ data.PresentHistory }}</el-text>
      </div>
      <div>
        <h4 class="mb-10px">过敏史</h4>
        <el-text>{{ data.Allergies }}</el-text>
      </div>
      <div>
        <h4 class="mb-10px">辅助检查结果</h4>
        <el-text>{{ data.AuxiliaryDiagnosis }}</el-text>
        <el-image
          v-for="(url, index) in reportUrlList"
          :key="index"
          class="w-150px h-150px mr-20px"
          :src="url"
          :initial-index="index"
          :preview-src-list="reportUrlList"
        />
      </div>
    </template>
    <div>
      <h4 class="mb-10px">诊断</h4>
      <el-text>{{ data.diagnosis }}</el-text>
    </div>
    <div v-if="data.Disposal">
      <h4 class="mb-10px">处置</h4>
      <div v-for="(text, index) in data.Disposal" :key="index">
        <el-text class="color-#333333">
          {{ text ? index + 1 + ":" + text : "" }}
        </el-text>
      </div>
    </div>
    <template v-if="isSelfBuild">
      <div>
        <h4 class="mb-10px">症状描述</h4>
        <el-text>{{ data.description }}</el-text>
      </div>
      <div>
        <h4 class="mb-10px">病历资料</h4>
        <el-image
          v-for="(url, index) in reportUrlList"
          :key="index"
          class="w-150px h-150px mr-20px"
          :src="url"
          :initial-index="index"
          :preview-src-list="reportUrlList"
        />
      </div>
    </template>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button type="primary" @click="emit('cancel')">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import Record_Api from "@/api/record";

interface UserVisitTemp extends VisitInfo {
  // 诊断
  diagnosis?: string;
  // 症状描述
  description?: string;
}

interface VisitData extends Omit<UserVisitTemp, "Disposal"> {
  Disposal?: string[];
}

const props = defineProps<{
  data: VisitInfo;
}>();

const emit = defineEmits<{
  cancel: [];
}>();

const data = reactive<VisitData>({});
const reportUrlList = ref<string[]>([]);
const isSelfBuild = ref(false);
const loading = ref(false);

// 格式化年龄
const ageFormat = (Age?: string) => {
  return Age ? (Age.includes("岁") ? Age : Age + "岁") : "";
};

onMounted(() => {
  isSelfBuild.value = props.data.IsSelfBuild ?? false;
  requestMedicalRecordDetail();
});

// 请求病历详情
async function requestMedicalRecordDetail() {
  loading.value = true;
  const res = await Record_Api.getVisit({ visitId: props.data.Id! });
  loading.value = false;
  if (res.Type !== 200) {
    ElMessage.error(res.Content);
    return;
  }
  if (!res.Data) return;

  Object.assign(data, res.Data.Vist);
  data.InDate = data.InDate ? dayjs(data.InDate).format("YYYY-MM-DD HH:mm") : "";

  data.diagnosis = res.Data.VisitDiagnoses?.filter((e) => e?.DiagnoseTypeName?.includes("诊断"))
    .map((e) => e.DiagnoseName)
    .join("\n");

  data.description = res.Data.VisitDiagnoses?.filter((e) => e?.DiagnoseTypeName === "症状描述")
    .map((e) => e.DiagnoseName)
    .join("\n");

  if (res.Data.Vist?.Disposal) {
    data.Disposal = res.Data.Vist.Disposal.split(`\n`);
  }

  if (res.Data.VisitReportDetails) {
    reportUrlList.value = res.Data.VisitReportDetails.map((e) => e.ReportUrl ?? "");
  }
}
</script>

<style lang="scss" scoped></style>
