<template>
  <div
    v-loading="loading"
    class="px-20px h-550px flex flex-col justify-start items-stretch overflow-y-auto"
  >
    <el-row>
      <el-text>姓名：{{ data.Name }}</el-text>
      <el-text>性别：{{ data.Sex }}</el-text>
      <el-text>出生日期：{{ data.Birthday }}</el-text>
      <el-text>手机号：{{ data.PhoneNumber }}</el-text>
    </el-row>
    <el-row>
      <el-text>民族：{{ data.Nation }}</el-text>
      <el-text>籍贯：{{ data.NativePlace }}</el-text>
      <el-text>婚姻状况：{{ data.Marital }}</el-text>
      <el-text>工作状态：{{ data.WorkState }}</el-text>
    </el-row>
    <el-row>
      <el-text>经济收入：{{ data.EconomicIncome }}</el-text>
      <el-text>教育程度：{{ data.Education }}</el-text>
      <el-text>血型：{{ data.Blood }}</el-text>
      <el-text>职业：{{ data.Professional }}</el-text>
    </el-row>
    <el-row>
      <el-text>联系人：{{ data.ContactPerson }}</el-text>
      <el-text>联系人关系：{{ data.ContactRelation }}</el-text>
      <el-text>联系人电话：{{ data.ContactPhone }}</el-text>
      <el-text />
    </el-row>
    <el-row>
      <el-text>家庭地址：{{ data.Address }}</el-text>
    </el-row>
    <el-row>
      <el-text>既往史：{{ data.PreviousHistory }}</el-text>
    </el-row>
    <el-row>
      <el-text>过敏史：{{ data.AllergicHistory }}</el-text>
    </el-row>
    <el-row>
      <el-text>家族病史：{{ data.FamilyHistory }}</el-text>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import Record_Api from "@/api/record";
import { UserArchive } from "@/api/record/types";
import dayjs from "dayjs";

const kEnableDebug = false;
const props = defineProps<{
  patientId: string;
}>();

onMounted(() => {
  if (props.patientId) {
    requestPatientInfo();
  }
});

const loading = ref(false);

// 定义合并类型
type UserArchiveWithOutput = UserArchive & UserOutputDTO;
const data = reactive<UserArchiveWithOutput>({});

// 请求患者信息
async function requestPatientInfo() {
  loading.value = true;
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: 1,
      PageSize: 10,
    },
    FilterGroup: {
      Rules: [
        {
          Field: "userId",
          Value: props.patientId!,
          Operate: 3,
        },
      ],
    },
  };
  const rs = await Promise.all([
    Record_Api.getUserArchives(params),
    Passport_Api.getUserById({ id: props.patientId! }),
  ]);
  loading.value = false;
  const failed = rs.find((r) => r.Type !== 200);
  if (failed) {
    ElMessage.error(failed.Content);
    return;
  }

  // 先赋值档案信息
  if (rs[0].Data.Rows.length > 0) {
    const archive = rs[0].Data.Rows[0];
    Object.assign(data, archive);
  }

  // 再赋值用户基本信息，只覆盖空值
  const userInfo = rs[1].Data;
  Object.keys(userInfo).forEach((key) => {
    const k = key as keyof typeof data;
    if (data[k] === undefined || data[k] === null) {
      (data[k] as any) = userInfo[k as keyof typeof userInfo];
    }
  });
  if (data.Birthday) {
    data.Birthday = dayjs(data.Birthday).format("YYYY-MM-DD");
  }

  // 拼接过敏史
  if (data.AllergicHistory) {
    const allergicHistory: TagsItem = JSON.parse(data.AllergicHistory);
    let tags: string[] = [];
    if (allergicHistory.Tags) {
      tags.push(...allergicHistory.Tags);
    }
    if (allergicHistory.Text) {
      tags.push(...allergicHistory.Text.split(" "));
    }
    data.AllergicHistory = tags.join("、");
  }

  // 拼接既往史
  if (data.PreviousHistory) {
    const previousHistory: TagsItem = JSON.parse(data.PreviousHistory);
    let tags: string[] = [];
    if (previousHistory.Tags) {
      tags.push(...previousHistory.Tags);
    }
    if (previousHistory.Text) {
      tags.push(...previousHistory.Text.split(" "));
    }
    data.PreviousHistory = tags.join("、");
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-row) {
  margin-bottom: 20px;
}

:deep(.el-text) {
  flex: 1;
}
</style>
