<template>
  <div v-loading="formLoading" class="p-10px overflow-y-auto max-h-600px">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="80"
      :disabled="props.disabled"
    >
      <el-row class="mb-10px">
        <el-form-item class="flex-1" label="角色名称" prop="Name" required>
          <el-input v-model="formData.Name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item class="flex-1" label="角色编码" prop="Code" required>
          <el-input v-model="formData.Code" placeholder="请输入角色编码" />
        </el-form-item>
      </el-row>
      <el-row class="mb-10px">
        <el-form-item class="flex-1" label="拼音码" prop="PinyinCode" required>
          <el-input v-model="formData.PinyinCode" placeholder="请输入拼音码" />
        </el-form-item>
        <el-form-item class="flex-1" label="类型编码" prop="RoleType" required>
          <el-input v-model="formData.RoleType" placeholder="请输入类型编码" />
        </el-form-item>
      </el-row>
      <el-row class="mb-10px">
        <el-form-item class="flex-1" label="是否启用" prop="IsEnabled">
          <el-switch v-model="formData.IsEnabled" />
        </el-form-item>
        <el-form-item class="flex-1" label="是否锁定" prop="IsLocked">
          <el-switch v-model="formData.IsLocked" />
        </el-form-item>
      </el-row>
      <el-form-item label="备注" prop="Remark">
        <el-input
          v-model="formData.Remark"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 6 }"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";
const props = defineProps<{
  data: BaseRole;
  disabled: boolean;
}>();

const emit = defineEmits(["cancel", "submit"]);

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.data));
  Object.assign(formData, data);
  formData.IsEnabled = formData.IsEnabled ?? true;
  formData.IsLocked = formData.IsLocked ?? false;
});

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = reactive<BaseRole>({});

// 表单验证规则
const rules = reactive<FormRules<BaseRole>>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
  PinyinCode: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  RoleType: [{ required: true, message: "请输入类型编码", trigger: "blur" }],
});

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields, formData);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("requestAddOrUpdateData", formData);
  emit("submit");
}
</script>

<style lang="scss" scoped></style>
