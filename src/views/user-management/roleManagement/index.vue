<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否启用" prop="IsEnabled">
                <KSelect
                  v-model="queryParams.IsEnabled"
                  :data="[
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="名称/备注"
                  prefix-icon="Search"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          row-key="Id"
          :height="tableFluidHeight"
          border
          highlight-current-row
        >
          <el-table-column prop="Name" label="名称" width="200" align="center" />
          <el-table-column prop="RoleType" label="类型编码" width="200" align="center" />
          <el-table-column prop="PinyinCode" label="拼音码" width="150" align="center" />
          <el-table-column prop="Remark" label="备注" align="center" min-width="200" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="200"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column label="是否启用" width="150" align="center">
            <template #default="scope">
              {{ scope.row.IsEnabled ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewDetail(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="600"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <RoleForm
      :data="showDataDialog.data"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Passport_Api from "@/api/passport";
import { ReadInputDTO } from "@/api/passport/types";

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "RoleManagement",
});

const { pageData, tableLoading, tableFluidHeight, tableResize, tableDateFormat } =
  useTableConfig<BaseRole>();

// 查询条件
const queryParams = reactive<ReadInputDTO>({});

// 查看编辑弹窗
const showDataDialog = reactive({
  isShow: false,
  title: "查看",
  disabled: true,
  data: {} as BaseRole,
});

// 点击搜索
function handleQuery() {
  requestTableList();
}

// 点击查看
async function onPreviewDetail(row?: BaseRole) {
  kEnableDebug && console.debug("查看", row);

  showDataDialog.data = row ?? {};
  showDataDialog.isShow = true;
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Passport_Api.read(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  pageData.value = r.Data;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
