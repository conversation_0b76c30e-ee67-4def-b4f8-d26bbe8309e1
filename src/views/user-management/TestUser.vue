<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form-item label="名称/手机号" prop="keyWords">
              <el-input
                v-model="keyword"
                clearable
                placeholder="请输入名称或者手机号"
                @input="onFilterChange"
              />
            </el-form-item>
          </template>
          <template #right>
            <el-button type="primary" @click="handleAddButtonClick">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          border
          row-key="UserId"
          :height="tableFluidHeight"
        >
          <el-table-column prop="Name" label="名称" align="center" />
          <el-table-column prop="PhoneNumber" label="手机号" align="center" />
          <el-table-column label="是否统计" width="120" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.ReportBan"
                name="ReportBan"
                active-color="#13ce66"
                inactive-color="#999999"
                @change="updateBan($event as boolean, row, 'ReportBan')"
              />
            </template>
          </el-table-column>
          <el-table-column label="是否接收短信" width="120" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.SmsBan"
                name="SmsBan"
                active-color="#13ce66"
                inactive-color="#999999"
                @change="updateBan($event as boolean, row, 'SmsBan')"
              />
            </template>
          </el-table-column>
          <el-table-column label="是否接收微信推送" width="150" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.WeChatBan"
                name="WeChatBan"
                active-color="#13ce66"
                inactive-color="#999999"
                @change="updateBan($event as boolean, row, 'WeChatBan')"
              />
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="200" align="center">
            <template #default="{ row }">
              <el-button link type="danger" @click="deleteUser(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>

    <!-- 添加测试用户 -->
    <el-dialog v-model="dialogVisible" title="添加测试用户" destroy-on-close align-center>
      <el-form :model="info" label-position="right" :disabled="submitting" @submit.prevent="submit">
        <el-form-item label="手机号:">
          <el-input v-model="info.PhoneNumber" type="text" :disabled="!!info.UserName" clearable />
        </el-form-item>
        <el-form-item label="用户名:">
          <el-input v-model="info.UserName" type="text" :disabled="!!info.PhoneNumber" clearable />
        </el-form-item>
        <p class="text-red">手机号与用户名二选一</p>
        <div class="text-right">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button :loading="submitting" type="primary" native-type="submit">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { type TestUserDTO } from "@/api/passport/types";
import { useTableConfig } from "@/hooks/useTableConfig";

const { pageData, tableLoading, tableFluidHeight, tableResize, kTableRef } =
  useTableConfig<TestUserDTO>();

let copyTableData: TestUserDTO[] = [];

const loadData = async () => {
  tableLoading.value = true;
  const res = await Passport_Api.getTestUsers();
  tableLoading.value = false;

  if (res.Type != 200) {
    ElMessage.error(res.Content);
    return;
  }

  res.Data.forEach((v) => {
    v.ReportBan = !v.ReportBan;
    v.SmsBan = !v.SmsBan;
    v.WeChatBan = !v.WeChatBan;
  });
  pageData.value = res.Data;
  copyTableData = [...res.Data];
};

onMounted(() => {
  loadData();
});

const keyword = ref("");
const onFilterChange = useDebounceFn((value: string) => {
  search(value);
}, 300);
const search = (keyword: string) => {
  const newData = copyTableData.filter(
    (v) =>
      (v.PhoneNumber && v.PhoneNumber.includes(keyword)) || (v.Name && v.Name.includes(keyword))
  );
  pageData.value = newData;
};

const updateBan = async (
  e: boolean,
  row: TestUserDTO,
  field: "ReportBan" | "SmsBan" | "WeChatBan"
) => {
  /** 页面上是反的，所以需要取反 */
  const data: Partial<TestUserDTO> = {
    UserId: row.UserId,
    ReportBan: !row.ReportBan,
    SmsBan: !row.SmsBan,
    WeChatBan: !row.WeChatBan,
    [field]: !e,
  };

  const res = await Passport_Api.updateTestUser(data);

  if (res.Type != 200) {
    /** 恢复状态 */
    row[field] = !e;
    ElNotification.error(res.Content ?? "更新失败");
    return;
  }

  ElNotification.success(res.Content ?? "更新成功");
};

const deleteUser = (row: TestUserDTO) => {
  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Passport_Api.removeTestUsers([row.UserId]);
    if (res.Type != 200) {
      ElNotification.error(res.Content ?? "删除失败");
      return;
    }
    ElNotification.success("删除成功");
    pageData.value = pageData.value.filter((v) => v.UserId !== row.UserId);
  });
};

const dialogVisible = ref(false);
const info = ref({
  PhoneNumber: "",
  UserName: "",
});

const submitting = ref(false);
const handleAddButtonClick = () => {
  info.value = {
    PhoneNumber: "",
    UserName: "",
  };
  dialogVisible.value = true;
};
const submit = async () => {
  if (!info.value.PhoneNumber && !info.value.UserName) {
    ElMessage.warning("请输入手机号或者用户名");
    return;
  }

  if (info.value.UserName) {
    const data = pageData.value.find((v) => v.Name === info.value.UserName);
    if (data) {
      ElMessage.warning("已存在该测试用户");
      return;
    }
  }

  if (info.value.PhoneNumber) {
    const data = pageData.value.find((v) => v.PhoneNumber === info.value.PhoneNumber);

    if (data) {
      ElMessage.warning("已存在该测试用户");
      return;
    }
    const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
    if (!reg.test(info.value.PhoneNumber)) {
      ElMessage.warning("手机号格式不正确");
      return;
    }
  }

  submitting.value = true;
  const res = await Passport_Api.createTestUser(info.value);

  await new Promise((resolve) => setTimeout(resolve, 3000));
  submitting.value = false;

  if (res.Type != 200) {
    ElNotification.error(res.Content ?? "添加失败");
    return;
  }

  loadData();
  dialogVisible.value = false;
  ElNotification.success("添加成功");
};
</script>
