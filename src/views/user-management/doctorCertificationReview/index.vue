<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="审核状态">
                <KSelect
                  v-model="queryParams.State"
                  :data="[
                    { label: '医院待审核', value: 0 },
                    { label: '平台待审核', value: 2 },
                    { label: '平台已拒绝', value: 3 },
                    { label: '平台已通过', value: 4 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/手机号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="Code" min-width="120" label="编码" />
          <el-table-column prop="Name" label="姓名" min-width="90" />
          <el-table-column prop="Sex" label="性别" min-width="80" />
          <el-table-column label="主科室" prop="Department.Name" min-width="120" />
          <el-table-column prop="PhoneNumber" label="手机号" min-width="120" />
          <el-table-column label="所在医院" prop="Organization.Name" min-width="150" />
          <el-table-column label="职称" prop="WorkerTitleName" min-width="100" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            min-width="160"
            :formatter="tableDateFormat"
          />
          <el-table-column prop="ReviewText" label="审核状态" min-width="100" />
          <el-table-column fixed="right" label="操作" min-width="100">
            <template #default="scope">
              <el-button
                v-if="scope.row.Operations.includes('detail')"
                link
                type="primary"
                @click="onPreviewDetail(scope.row)"
              >
                查看
              </el-button>
              <el-button
                v-if="scope.row.Operations.includes('review')"
                link
                type="primary"
                @click="onReview(scope.row)"
              >
                审核
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageInput.PageIndex"
          v-model:limit="queryParams.PageInput.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 审核/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="70%"
    destroy-on-close
  >
    <CertificationTabs
      :data="showDataDialog.data"
      :disabled="showDataDialog.disabled"
      @submit="onConfirmSubmitItem"
      @cancel="showDataDialog.isShow = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Passport_Api from "@/api/passport";
import { DoctorCertification, GetAuthDoctorsParams } from "@/api/passport/types";
import { getDictionaryList } from "@/utils/dict";
import { DoctorCertificationTable } from "./types/types";

// 调试开关
const kEnableDebug = true;
defineOptions({
  name: "DoctorCertificationReview",
});

const { pageData, tableLoading, tableFluidHeight, total, tableResize, tableDateFormat } =
  useTableConfig<DoctorCertificationTable>();

// 职称基础数据列表
let workerTitleList: ReadDict[] = [];

// 查询条件
const queryParams = reactive<GetAuthDoctorsParams>({
  PageInput: { PageIndex: 1, PageSize: 10 },
});

// State: 审核状态 (null: 全部, '0': 医院待审核, '2': 平台待审核, '3': 平台已拒绝, '4': 平台已通过)
// Type: 查询类型 (1: 医院审核, 2: 平台审核)
watch(
  () => queryParams.State,
  (value) => {
    if (value === undefined || value === null) {
      queryParams.Type = undefined;
    } else if (value === 0) {
      queryParams.Type = 1;
    } else {
      queryParams.Type = 2;
    }
  }
);

// 查看/审核弹窗
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as DoctorCertification, // 查看/审核详情
});

// 点击搜索
function handleQuery() {
  queryParams.PageInput.PageIndex = 1;
  requestTableList();
}

// 点击查看
async function onPreviewDetail(row?: DoctorCertification) {
  kEnableDebug && console.debug("查看", row);

  if (!row?.WorkflowId) {
    ElMessage.error("未获取到审核流程ID");
    return;
  }

  if (!row.Organization?.Id) {
    ElMessage.error("未获取到医院ID");
    return;
  }

  showDataDialog.title = "查看";
  showDataDialog.disabled = true;
  showDataDialog.data = row ?? {};
  showDataDialog.isShow = true;
}

// 点击审核
async function onReview(row?: DoctorCertification) {
  kEnableDebug && console.debug("审核", row);

  showDataDialog.title = "审核";
  showDataDialog.disabled = false;
  showDataDialog.data = row ?? {};
  showDataDialog.isShow = true;
}

// 确定审核提交
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定审核");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("操作成功");

  // 刷新列表
  queryParams.PageInput.PageIndex = 1;
  requestTableList();
}

/**
 * 解析当前审核流程
 *
 * {
 *   当 Node: DoctorAuthentication_Submit  医生提交审核流程
 *   且:
 *      Status = 2 标识医生审核通过
 * }
 * {
 *   当 Node: DoctorAuthentication_Hospital  医院审核流程
 *   且:
 *      Status = 0 标识医院待审核 （操作：医院可以通过、拒绝,拒绝需要填写拒绝原因；）
 *      Status = 2 标识医院审核通过 （操作：医院可以在用户管理中注销该用户）
 *      Status = 3 标识医院审核拒绝 （操作：医院可以撤回，撤回后为医院待审核状态；）
 * }
 * {
 *   当 Node: DoctorAuthentication_Platform 平台审核流程
 *   且:
 *      Status = 0 标识平台待审核 （操作：平台可以撤回，撤回后为平台待审核状态；）
 *      Status = 2 标识平台审核通过 （操作：平台和医院可以在用户管理中禁用该用户；）
 *      Status = 3 标识平台审核拒绝 （操作：平台可以撤回，撤回后为平台待审核状态；）
 * }
 */
function parseReviewProcess(row: DoctorCertificationTable) {
  if (!row.ExecutionPointers || row.ExecutionPointers.length === 0) {
    return row;
  }

  // 定义状态映射
  type ProcessStatusMap = Record<number, { text: string; buttons: string[] }>;
  const platformStatusMap: ProcessStatusMap = {
    0: {
      text: "平台待审核",
      buttons: ["review", "refuse", "agree"],
    },
    2: { text: "平台已通过", buttons: ["detail"] },
    3: { text: "平台已拒绝", buttons: ["detail"] },
  };

  const hospitalStatusMap: ProcessStatusMap = {
    0: { text: "医院待审核", buttons: ["review"] },
    2: {
      text: "平台待审核",
      buttons: ["review", "refuse", "agree"],
    },
    3: { text: "医院已拒绝", buttons: ["detail"] },
  };

  // 查找流程节点
  const findProcess = (nodeName: string) =>
    row.ExecutionPointers!.find((res) => res.Node === nodeName);

  // 获取平台流程状态
  const platformProcess = findProcess("DoctorAuthentication_Platform");
  if (platformProcess) {
    const status = platformStatusMap[platformProcess.Status ?? -1];
    if (status) {
      row.ReviewText = status.text;
      row.ReviewStatus = platformProcess.Status;
      row.Operations = status.buttons;
      return row;
    }
  }

  // 获取医院流程状态
  const hospitalProcess = findProcess("DoctorAuthentication_Hospital");
  if (hospitalProcess) {
    const status = hospitalStatusMap[hospitalProcess.Status ?? -1];
    if (status) {
      row.ReviewText = status.text;
      row.ReviewStatus = hospitalProcess.Status;
      row.Operations = status.buttons;
      return row;
    }
  }

  // 默认状态：医生提交审核资料，需要医院端进行审核
  row.ReviewText = "医院待审核";
  row.ReviewStatus = 0;
  row.Operations = ["review", "refuse", "agree"];
  return row;
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Passport_Api.getAuthDoctors(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Rows.map((item) => ({
    ...parseReviewProcess(item),
    WorkerTitleName: workerTitleList.find((e) => e.Value === item.WorkerTitle)?.Key,
  }));
  total.value = r.Data.Total;
}

onActivated(async () => {
  tableLoading.value = true;
  await requestWorkerTitle();
  requestTableList();
});

// 请求职称类型基础数据
async function requestWorkerTitle() {
  const workerTitles = await getDictionaryList("WorkerTitleDict");
  workerTitleList = workerTitles;
}
</script>

<style lang="scss" scoped></style>
