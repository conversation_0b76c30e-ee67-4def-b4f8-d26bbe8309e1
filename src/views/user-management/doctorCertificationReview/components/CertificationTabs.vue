<template>
  <div v-loading="tabsLoading" class="p-20px overflow-y-auto">
    <el-tabs type="card">
      <el-tab-pane label="基本信息">
        <CertificationBasicInfo :data="tabsData" />
      </el-tab-pane>
      <el-tab-pane label="身份认证">
        <IdentityContent
          :user-certificates="tabsData.UserCertificates ?? []"
          :user-id="tabsData.UserId ?? ''"
          :disabled="true"
        />
      </el-tab-pane>
      <el-tab-pane label="职业认证">
        <CertificationContent
          :worker-title="tabsData.WorkerTitle ?? ''"
          :worker-type="tabsData.WorkerType ?? ''"
          :professional-certification="tabsData.UserCertificates ?? []"
          :disabled="true"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button
      v-show="props.data.Operations?.includes('agree')"
      v-if="!props.disabled"
      type="primary"
      :loading="submitLoading"
      @click="onAgree"
    >
      通过
    </el-button>
    <el-button
      v-show="props.data.Operations?.includes('refuse')"
      v-if="!props.disabled"
      type="primary"
      :loading="submitLoading"
      @click="onReject"
    >
      拒绝
    </el-button>
    <el-button @click="emit('cancel')">取消</el-button>
  </div>

  <!-- 拒绝弹窗 -->
  <el-dialog v-model="rejectDialogVisible" title="拒绝" destroy-on-close>
    <RejectForm
      @submit="
        (reason) => {
          rejectDialogVisible = false;
          requestAuthDoctorOperation(reason);
        }
      "
      @cancel="rejectDialogVisible = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import Dictionary_Api from "@/api/dictionary";
import { CertificationTabsData, DoctorCertificationTable } from "../types/types";
import { DoctorAuthenticationParams } from "@/api/passport/types";

const kEnableDebug = true;

defineOptions({
  name: "CertificationForm",
});

const props = defineProps<{
  data: DoctorCertificationTable;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

// 数据
const tabsData = ref<CertificationTabsData>({});
const tabsLoading = ref(false);

onMounted(async () => {
  tabsLoading.value = true;
  const r0 = await requestBaseData();
  if (r0.Type !== 200) {
    tabsLoading.value = false;
    ElMessage.error(r0.Content);
    return r0;
  }

  const r1 = await requestAuthDoctorData();
  tabsLoading.value = false;
  if (r1.Type !== 200) {
    ElMessage.error(r1.Content);
  }
});

// 获取认证审核信息
async function requestAuthDoctorData() {
  const r = await Passport_Api.getSingleAuthDoctor(props.data.WorkflowId!);
  if (r.Type === 200) {
    tabsData.value = {
      ...props.data,
      ...r.Data,
      WorkerTypeName: workerTypeList.value.find((e) => e.Value === r.Data.WorkerType)?.Key,
    };
  }

  return r;
}

// 拒绝弹窗是否显示
const rejectDialogVisible = ref(false);

// 点击拒绝
function onReject() {
  kEnableDebug && console.debug("点击拒绝");
  rejectDialogVisible.value = true;
}

// 点击同意
function onAgree() {
  kEnableDebug && console.debug("点击同意");
  requestAuthDoctorOperation();
}

const submitLoading = ref(false);

// 请求审核
async function requestAuthDoctorOperation(reason?: string) {
  if (!props.data.WorkflowId) {
    ElMessage.error("未获取到WorkflowId");
    return;
  }

  if (!props.data.UserId) {
    ElMessage.error("未获取到UserId");
    return;
  }

  submitLoading.value = true;
  let r: ServerResult<null>;
  if (tabsData.value.ReviewText === "医院待审核") {
    const params: DoctorAuthenticationParams = {
      workflowId: props.data.WorkflowId!,
      userId: props.data.UserId!,
      reason: reason,
    };
    r = await Passport_Api.doctorAuthenticationOperationOneKey(params);
  } else {
    const params: DoctorAuthenticationParams = {
      workflowId: props.data.WorkflowId!,
      node: "DoctorAuthentication_Platform",
      userId: props.data.UserId!,
      reason: reason,
    };
    r = await Passport_Api.doctorAuthenticationOperation(params);
  }
  submitLoading.value = false;

  if (r.Type === 200) {
    emit("submit");
  }
  ElMessage.success(r.Content);
}

// 职称类型列表
const workerTypeList = ref<ReadDict[]>([]);
provide("workerTypeDictList", workerTypeList);

// 职称列表
const workerTitleList = ref<ReadDict[]>([]);
provide("workerTitleDictList", workerTitleList);

// 获取基础数据
async function requestBaseData() {
  const rs = await Promise.all([
    Dictionary_Api.getDict({ code: "WorkerTypeDict" }),
    Dictionary_Api.getDict({ code: "workerTitleDict" }),
  ]);

  const failed = rs.find((rs) => rs.Type !== 200);
  if (failed) {
    return failed;
  }

  workerTypeList.value = rs[0].Data;
  workerTitleList.value = rs[1].Data;

  return rs[0];
}
</script>

<style lang="scss" scoped></style>
