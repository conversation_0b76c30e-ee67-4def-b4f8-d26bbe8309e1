import { type AuthDoctorInfo, type DoctorCertification } from "@/api/passport/types";

export interface DoctorCertificationTable extends DoctorCertification {
  // 职称名称
  WorkerTitleName?: string;
  // 人员类型名称
  WorkerTypeName?: string;
  // 审核状态(文本)
  ReviewText?: string;
  // 审核状态(数字)
  ReviewStatus?: number;
  // 当前状态可操作按钮
  Operations?: string[];
}

export type CertificationTabsData = DoctorCertificationTable & AuthDoctorInfo;
