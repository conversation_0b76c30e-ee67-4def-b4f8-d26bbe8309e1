<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-width="120"
    label-position="right"
    scroll-to-first-error
    :disabled="isPreview"
  >
    <el-form-item label="医疗机构执业许可证登记号" prop="OrgRegisterNum">
      <el-input v-model="formData.OrgRegisterNum" />
    </el-form-item>
    <el-form-item label="证件图片" prop="CertificateImg">
      <SingleImageUpload v-model="formData.CertificateImg!" />
    </el-form-item>
    <el-form-item label="法人" prop="LegalPerson">
      <el-input v-model="formData.LegalPerson" />
    </el-form-item>
    <el-form-item label="签约时间" prop="SignAContractTime">
      <el-date-picker
        v-model="formData.SignAContractTime"
        type="datetime"
        placeholder="选择日期时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :clearable="false"
      />
    </el-form-item>
    <el-form-item label="合同有效期">
      <el-date-picker
        v-model="contractRange"
        unlink-panels
        type="daterange"
        :shortcuts="datePickerShortcuts"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>
    <FormItemContainer>
      <el-form-item label="平台签约人" prop="PlatformContractPerson">
        <el-input v-model="formData.PlatformContractPerson" />
      </el-form-item>
      <el-form-item label="医院签约人" prop="HosContractPerson">
        <el-input v-model="formData.HosContractPerson" />
      </el-form-item>
    </FormItemContainer>
  </el-form>
</template>

<script setup lang="ts">
import { FormRules } from "element-plus";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

export interface QualificationType {
  OrgRegisterNum?: string;
  CertificateImg?: string;
  LegalPerson?: string;
  SignAContractTime?: string;
  PlatformContractPerson?: string;
  HosContractPerson?: string;
  ContractStartTime?: string;
  ContractEndTime?: string;
}
const isPreview = inject<Ref<boolean>>("isPreview", ref(false));
const formRef = useTemplateRef("formRef");
const formData = ref<QualificationType>({
  OrgRegisterNum: "",
  CertificateImg: "",
  LegalPerson: "",
  SignAContractTime: "",
  PlatformContractPerson: "",
  HosContractPerson: "",
  ContractStartTime: "",
  ContractEndTime: "",
});
const rules = reactive<FormRules>({});
const contractRange = ref<string[]>([]);

const handleSubmitForm = (): Promise<QualificationType | null> => {
  return new Promise<QualificationType | null>((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve(formData.value);
      } else {
        resolve(null);
      }
    });
  });
};

interface Props {
  qualification: QualificationType | null;
}
const props = defineProps<Props>();
watch(
  () => props.qualification,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue = JSON.parse(JSON.stringify(newVal));
    contractRange.value = [copyValue.ContractStartTime, copyValue.ContractEndTime];
    formData.value = copyValue;
  },
  { immediate: true }
);

defineExpose({
  handleSubmitForm,
});
watch(contractRange, (newVal) => {
  if (!newVal) {
    formData.value.ContractStartTime = "";
    formData.value.ContractEndTime = "";
  } else {
    formData.value.ContractStartTime = newVal[0];
    formData.value.ContractEndTime = newVal[1];
  }
});
</script>

<style lang="scss" scoped></style>
