<template>
  <div class="service-content">
    <el-tabs v-model="activeName" type="card" class="advice-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="first">
        <HospitalBaseInfo ref="hospitalBaseInfoRef" :base-info="baseInfo" />
      </el-tab-pane>
      <el-tab-pane label="资质与协议" name="second">
        <HospitalQualification ref="hospitalQualificationRef" :qualification="qualification" />
      </el-tab-pane>
      <el-tab-pane label="开放功能" name="third">
        <HospitalOpenPrescription
          ref="hospitalOpenPrescriptionRef"
          :open-prescription="openPrescription"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { TabsPaneContext } from "element-plus";
import HospitalBaseInfo, { BaseInfoType } from "./HospitalBaseInfo.vue";
import HospitalQualification, { QualificationType } from "./HospitalQualification.vue";
import HospitalOpenPrescription, { OpenPrescriptionType } from "./HospitalOpenPrescription.vue";
const hospitalBaseInfoRef = useTemplateRef("hospitalBaseInfoRef");
const hospitalQualificationRef = useTemplateRef("hospitalQualificationRef");
const hospitalOpenPrescriptionRef = useTemplateRef("hospitalOpenPrescriptionRef");
const activeName = ref<string>("first");
const handleClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name as string;
};
const baseInfo = ref<BaseInfoType | null>(null);
const qualification = ref<QualificationType | null>(null);
const openPrescription = ref<OpenPrescriptionType | null>(null);
const handleSubmitInfo = async (): Promise<BaseOrganization | null> => {
  const baseInfo = await hospitalBaseInfoRef.value?.handleSubmitForm();
  if (!baseInfo) {
    activeName.value = "first";
    return null;
  }
  const qualification = await hospitalQualificationRef.value?.handleSubmitForm();
  if (!qualification) {
    activeName.value = "second";
    return null;
  }
  const openPrescription = await hospitalOpenPrescriptionRef.value?.handleSubmitForm();
  if (!openPrescription) {
    activeName.value = "third";
    return null;
  }
  const params: BaseOrganization = {
    ...baseInfo,
    ...qualification,
    ...openPrescription,
  };
  const data: BaseOrganization = handleGetParamsData(params);
  return data;
};
// 可能之后需要处理请求数据 现在是不需要处理 先把这个方法写出来
const handleGetParamsData = (data: BaseOrganization): BaseOrganization => {
  const params: BaseOrganization = {
    ...data,
  };
  if (props.hospitalInfo?.Id) {
    params.Id = props.hospitalInfo.Id;
  } else {
    delete params.Id;
  }
  if (!props.hospitalInfo?.CreatedTime) {
    params.CreatedTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
  }
  if (!params.Id || !params.SignAContractTime) {
    params.SignAContractTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
  }
  if (!params.City) {
    params.City = null;
  }
  return params;
};
const handleProcessData = (data: BaseOrganization) => {
  baseInfo.value = {
    Id: data.Id!,
    Name: data.Name,
    Alias: data.Alias,
    PinyinCode: data.PinyinCode,
    Code: data.Code,
    IsLocked: data.IsLocked,
    IsEnabled: data.IsEnabled,
    Category: data.Category,
    Level: data.Level,
    City: data.City,
    CityName: data.CityName,
    CityCode: data.CityCode,
    ProvinceCode: data.ProvinceCode,
    ProvinceName: data.ProvinceName,
    CountryCode: data.CountryCode,
    CountryName: data.CountryName,
    Address: data.Address,
    LatLon: data.LatLon,
    Phone: data.Phone,
    AdminPhone: data.AdminPhone,
    CustomerServicePhone: data.CustomerServicePhone,
    OrganizationPersonnel: data.OrganizationPersonnel!,
    AssistantQrCode: data.AssistantQrCode,
    HeadImg: data.HeadImg,
    QrCode: data.QrCode,
  };
  qualification.value = {
    OrgRegisterNum: data.OrgRegisterNum,
    CertificateImg: data.CertificateImg,
    LegalPerson: data.LegalPerson,
    SignAContractTime: data.SignAContractTime,
    PlatformContractPerson: data.PlatformContractPerson,
    HosContractPerson: data.HosContractPerson,
    ContractStartTime: data.ContractStartTime,
    ContractEndTime: data.ContractEndTime,
  };
  openPrescription.value = {
    IsTechExchange: data.IsTechExchange,
    IsReferral: data.IsReferral,
    IsTreatment: data.IsTreatment,
    Work: data.Work,
    Remark: data.Remark,
  };
};

interface Props {
  hospitalInfo: BaseOrganization | null;
}
const props = defineProps<Props>();
watch(
  () => props.hospitalInfo,
  async (newVal) => {
    if (!newVal) {
      baseInfo.value = null;
      qualification.value = null;
      openPrescription.value = null;
      return;
    }
    const copyValue = JSON.parse(JSON.stringify(newVal));
    handleProcessData(copyValue);
  },
  { immediate: true }
);
defineExpose({
  handleSubmitInfo,
});
</script>

<style lang="scss" scoped>
.advice-tabs {
  :deep(.el-tabs__header) {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: var(--el-bg-color);
  }
}
.service-content {
  padding: 20px;
  height: 600px;
  overflow-y: auto;
  width: 100%;
  padding-top: 0px !important;
}
</style>
