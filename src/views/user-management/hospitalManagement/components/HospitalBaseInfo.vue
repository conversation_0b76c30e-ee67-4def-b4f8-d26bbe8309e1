<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-position="right"
    scroll-to-first-error
    :label-width="100"
    :disabled="isPreview"
  >
    <el-form-item label="医院名称" prop="Name">
      <el-input v-model="formData.Name" @blur="handleGetPinyinCode" />
    </el-form-item>
    <el-form-item label="医院别名" prop="Alias">
      <el-input v-model="formData.Alias" />
    </el-form-item>
    <el-form-item label="拼音码" prop="PinyinCode">
      <el-input v-model="formData.PinyinCode" />
    </el-form-item>
    <el-form-item label="医院编码" prop="Code">
      <el-input v-model="formData.Code" />
    </el-form-item>
    <FormItemContainer>
      <el-form-item label="是否锁定" prop="IsLocked">
        <el-switch v-model="formData.IsLocked" />
      </el-form-item>
      <el-form-item label="是否启用" prop="IsEnabled">
        <el-switch v-model="formData.IsEnabled" />
      </el-form-item>
    </FormItemContainer>
    <FormItemContainer>
      <el-form-item label="医院类型" prop="Category" style="width: 40%">
        <el-select v-model="formData.Category" placeholder="请选择医院类型">
          <el-option
            v-for="(item, index) in hosTypeList"
            :key="item"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="医院等级" prop="Level" style="width: 40%">
        <el-select v-model="formData.Level" placeholder="请选择医院等级">
          <el-option
            v-for="item in hosLevelList"
            :key="item.Key"
            :label="item.Value"
            :value="Number(item.Key)"
          />
        </el-select>
      </el-form-item>
    </FormItemContainer>
    <el-form-item label="所在城市" prop="City">
      <CityPicker
        v-model="formData.City"
        :is-only-city="false"
        @getSelectCityInfo="handleCityChange"
      />
    </el-form-item>
    <el-form-item label="医院地址" prop="Address">
      <el-input v-model="formData.Address" @blur="handleGetAddressLatLon" />
    </el-form-item>
    <el-form-item label="经纬度" prop="LatLon">
      <el-input v-model="formData.LatLon" />
      <span style="color: red; font-size: 12px">
        医院地址自动算出。手动填写格式为：110.145600,45.124500
      </span>
    </el-form-item>
    <el-form-item label="医院电话" prop="Phone">
      <el-input v-model="formData.Phone" />
    </el-form-item>
    <FormItemContainer>
      <el-form-item label="管理员账号" prop="AdminPhone">
        <el-input v-model="formData.AdminPhone" />
      </el-form-item>
      <el-form-item label="客服电话" prop="CustomerServicePhone">
        <el-input v-model="formData.CustomerServicePhone" />
      </el-form-item>
    </FormItemContainer>
    <FormItemContainer>
      <el-form-item label="管理负责人" prop="ManageLeaderId">
        <UserSelect
          ref="manageLeaderSelectRef"
          v-model="formData.OrganizationPersonnel.ManageLeaderId"
          dto-type-name="QueryUserOutputDto3"
          @change="(e) => handleChangeUserSelect(e, 'ManageLeader')"
        />
      </el-form-item>
      <el-form-item label="销售负责人" prop="SaleLeaderId">
        <UserSelect
          ref="saleLeaderSelectRef"
          v-model="formData.OrganizationPersonnel.SaleLeaderId"
          dto-type-name="QueryUserOutputDto3"
          @change="(e) => handleChangeUserSelect(e, 'SaleLeader')"
        />
      </el-form-item>
    </FormItemContainer>
    <el-form-item label="医生助手">
      <UserSelect
        ref="assistantSelectRef"
        v-model="formData.OrganizationPersonnel.AssistantId"
        :role-types="['assistant']"
        @change="(e) => handleChangeUserSelect(e, 'Assistant')"
      />
    </el-form-item>
    <el-form-item label="医助二维码" prop="AssistantQrCode">
      <SingleImageUpload v-model="formData.AssistantQrCode!" />
    </el-form-item>
    <el-form-item label="医院logo" prop="HeadImg">
      <SingleImageUpload v-model="formData.HeadImg!" />
    </el-form-item>
    <el-form-item v-if="formData.Id && !isPreview" label="医院首页二维码">
      <QRCode
        v-model="formData.QrCode.ToHomePage"
        :size="200"
        :value="`https://oss-biz.kangfx.com?orgId=${formData.Id}&orgName=${formData.Name}&toIndex=index`"
        :need-upload="true"
      />
    </el-form-item>
    <el-form-item v-if="formData.Id && !isPreview" label="医院医生二维码">
      <QRCode
        v-model="formData.QrCode.ToDoctor"
        :size="200"
        :value="`https://oss-biz.kangfx.com?orgId=${formData.Id}&orgName=${formData.Name}`"
        :need-upload="true"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import Other_Api from "@/api/other";
import { chineseToPinyin } from "@/utils";
import { EpPropMergeTypeWithNull, FormRules } from "element-plus";
import UserSelect from "@/components/UserSelect/index.vue";
export interface BaseInfoType {
  Id?: string;
  Name?: string;
  Alias?: string;
  PinyinCode?: string;
  Code?: string;
  IsLocked?: boolean;
  IsEnabled?: boolean;
  Category?: number;
  Level?: EpPropMergeTypeWithNull<number>;
  City?: EpPropMergeTypeWithNull<string>;
  CityCode?: EpPropMergeTypeWithNull<string>;
  CityName?: string;
  ProvinceCode?: string;
  ProvinceName?: string;
  CountryCode?: string;
  CountryName?: string;
  Address?: string;
  LatLon?: string;
  Phone?: string;
  AdminPhone?: string;
  CustomerServicePhone?: string;
  OrganizationPersonnel: {
    AssistantId?: string;
    AssistantName?: string;
    ManageLeaderId?: string;
    ManageLeaderName?: string;
    SaleLeaderId?: string;
    SaleLeaderName?: string;
    CreatedTime?: string;
  };
  AssistantQrCode?: string;
  HeadImg?: string;
  QrCode?: string;
}
interface PageBaseInfoType extends Omit<BaseInfoType, "QrCode"> {
  QrCode: {
    ToDoctor: string;
    ToHomePage: string;
  };
}
const isPreview = inject<Ref<boolean>>("isPreview");
const hosLevelList = inject<Ref<ReadDict[]>>("hosLevelList");
const hosTypeList = inject<Ref<string[]>>("hosTypeList");
const formRef = useTemplateRef("formRef");

const assistantSelectRef = useTemplateRef("assistantSelectRef");
const manageLeaderSelectRef = useTemplateRef("manageLeaderSelectRef");
const saleLeaderSelectRef = useTemplateRef("saleLeaderSelectRef");
const formData = ref<PageBaseInfoType>({
  Id: "",
  Name: "",
  Alias: "",
  PinyinCode: "",
  Code: "",
  IsLocked: false,
  IsEnabled: true,
  Category: 0,
  Level: null,
  City: "",
  CityCode: "",
  CityName: "",
  ProvinceCode: "",
  ProvinceName: "",
  CountryCode: "",
  CountryName: "",
  Address: "",
  LatLon: "",
  Phone: "",
  AdminPhone: "",
  CustomerServicePhone: "",
  OrganizationPersonnel: {
    AssistantId: "",
    AssistantName: "",
    ManageLeaderId: "",
    ManageLeaderName: "",
    SaleLeaderId: "",
    SaleLeaderName: "",
  },
  AssistantQrCode: "",
  HeadImg: "",
  QrCode: {
    ToDoctor: "",
    ToHomePage: "",
  },
});
const rules = reactive<FormRules>({
  Name: [{ required: true, message: "请输入医院名称", trigger: "blur" }],
});

const handleCityChange = (value: { name: string; code: string }[]) => {
  formData.value.ProvinceCode = value[0].code;
  formData.value.ProvinceName = value[0].name;
  formData.value.CityCode = value[1].code;
  formData.value.CityName = value[1].name;
  if (value.length > 2) {
    formData.value.CountryCode = value[2].code;
    formData.value.CountryName = value[2].name;
  }
};

const handleGetAddressLatLon = async () => {
  if (!formData.value.Address) return;
  const res = await Other_Api.getLngLatByAddress(formData.value.Address);
  if (res.status === "1" && Number(res.count) > 0) {
    formData.value.LatLon = res.geocodes[0].location;
  }
};
const handleGetPinyinCode = () => {
  formData.value.PinyinCode = chineseToPinyin(formData.value.Name!);
  formData.value.Code = chineseToPinyin(formData.value.Name!);
};

type UserSelectType = "Assistant" | "ManageLeader" | "SaleLeader";
type PersonnelKey = "AssistantName" | "ManageLeaderName" | "SaleLeaderName";

const handleChangeUserSelect = (
  value: string | string[] | null | undefined,
  type: UserSelectType
) => {
  if (value) {
    const resMap = {
      Assistant: assistantSelectRef,
      ManageLeader: manageLeaderSelectRef,
      SaleLeader: saleLeaderSelectRef,
    } as const;
    const userOptions = resMap[type].value?.userOptions;
    const selectUser = userOptions?.find((item) => item.Id === (value as string));
    if (selectUser) {
      formData.value.OrganizationPersonnel![`${type}Name` as PersonnelKey] = selectUser.Name;
      if (type === "Assistant") {
        formData.value.AssistantQrCode = selectUser.UserExternalIdentify?.WeChatQrCode || "";
      }
    }
  } else {
    formData.value.OrganizationPersonnel![`${type}Name` as PersonnelKey] = "";
    formData.value.OrganizationPersonnel![`${type}Id` as PersonnelKey] = "";
    if (type === "Assistant") {
      formData.value.AssistantQrCode = "";
    }
  }
};

const handleSubmitForm = (): Promise<BaseInfoType | null> => {
  return new Promise<BaseInfoType | null>((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        const copyData: PageBaseInfoType = JSON.parse(JSON.stringify(formData.value));
        const data: BaseInfoType = {
          ...copyData,
          QrCode: JSON.stringify(copyData.QrCode),
          City: copyData.City || null,
        };
        resolve(data);
      } else {
        resolve(null);
      }
    });
  });
};

const handleInitFormData = (value: PageBaseInfoType) => {
  const personnel = value.OrganizationPersonnel || {};
  formData.value = {
    ...value,
    OrganizationPersonnel: {
      AssistantId: personnel.AssistantId || "",
      AssistantName: personnel.AssistantName || "",
      ManageLeaderId: personnel.ManageLeaderId || "",
      ManageLeaderName: personnel.ManageLeaderName || "",
      SaleLeaderId: personnel.SaleLeaderId || "",
      SaleLeaderName: personnel.SaleLeaderName || "",
    },
    QrCode: {
      ToDoctor: value.QrCode ? value.QrCode.ToDoctor : "",
      ToHomePage: value.QrCode ? value.QrCode.ToHomePage : "",
    },
  };
};

interface Props {
  baseInfo: BaseInfoType | null;
}
const props = defineProps<Props>();
watch(
  () => props.baseInfo,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue = JSON.parse(JSON.stringify(newVal));
    handleInitFormData(copyValue);
  },
  { immediate: true }
);
defineExpose({
  handleSubmitForm,
});
</script>

<style lang="scss" scoped></style>
