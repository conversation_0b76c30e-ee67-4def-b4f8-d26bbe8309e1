<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="机构">
                <HospitalSelect
                  v-model="queryParams.OrgId"
                  :scopeable="true"
                  @change="queryParams.DeptId = null"
                />
              </el-form-item>
              <el-form-item label="入驻科室">
                <DeptSelect
                  v-model="queryParams.DeptId"
                  :disabled="!queryParams.OrgId"
                  :org-id="queryParams.OrgId"
                />
              </el-form-item>
              <el-form-item label="角色">
                <el-select
                  v-model="queryParams.RoleCode"
                  placeholder="请选择"
                  clearable
                  style="width: 100px"
                  :multiple-limit="1"
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="医生" value="doctor" />
                  <el-option label="治疗师" value="therapist" />
                  <el-option label="护士" value="nurse" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否绑定医助">
                <el-select
                  v-model="queryParams.HasAssistant"
                  style="width: 100px"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                  @change="handleIsBindChange"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="医助">
                <UserSelect
                  v-model="queryParams.AssistantId"
                  :role-types="['assistant']"
                  :disabled="queryParams.HasAssistant === '0'"
                />
              </el-form-item>
              <el-form-item label="是否开启问诊">
                <el-select
                  v-model="queryParams.IsEnableConsult"
                  style="width: 100px"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否自运行">
                <el-select
                  v-model="queryParams.IsSelfReliance"
                  style="width: 100px"
                  clearable
                  placeholder="请选择"
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="活跃等级">
                <el-select
                  v-model="queryParams.Liveness"
                  style="width: 120px"
                  placeholder="请选择"
                  clearable
                  multiple
                  collapse-tags
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="明星医生" value="明星医生" />
                  <el-option label="高级" value="高级" />
                  <el-option label="中级" value="中级" />
                  <el-option label="普通" value="普通" />
                  <el-option label="未激活" value="未激活" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否上传银行卡信息">
                <el-select
                  v-model="queryParams.IsBankInfoComplete"
                  style="width: 120px"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="完整" value="1" />
                  <el-option label="不完整" value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="排序方式">
                <el-select
                  v-model="queryParams.OrderMode"
                  style="width: 120px"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  value-on-clear="0"
                >
                  <el-option label="上线时间" value="0" />
                  <el-option label="活跃等级" value="1" />
                  <el-option label="付费方案数正序" value="3" />
                  <el-option label="付费方案数倒序" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              v-hasNoPermission="['promoter', 'externalSeller']"
              type="primary"
              @click="handleBatchOperation"
            >
              批量操作
            </el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              :disabled="pageData.length <= 0"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="DoctorId"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" width="55" reserve-selection />
          <el-table-column label="姓名" align="center" width="120">
            <template #default="scope">
              {{ scope.row.DoctorName }}
            </template>
          </el-table-column>
          <el-table-column label="医院" align="center" width="180">
            <template #default="scope">
              {{ scope.row.OrgName }}
            </template>
          </el-table-column>
          <el-table-column label="主要执业机构" align="center" width="180">
            <template #default="scope">
              {{ scope.row.PracticeOrganizationName }}
            </template>
          </el-table-column>
          <el-table-column label="科室" prop="DeptName" align="center" width="120" />
          <el-table-column label="职称" align="center" width="120">
            <template #default="scope">
              {{ scope.row.WorkerTitle }}
            </template>
          </el-table-column>
          <el-table-column label="角色" align="center" width="80">
            <template #default="scope">
              {{ scope.row.RoleName }}
            </template>
          </el-table-column>
          <el-table-column label="手机号码" align="center" width="120">
            <template #default="scope">
              {{ scope.row.PhoneNumber }}
            </template>
          </el-table-column>
          <el-table-column label="医助" align="center" width="120">
            <template #default="scope">
              {{ scope.row.AssistantName }}
            </template>
          </el-table-column>
          <el-table-column label="上线时间" align="center" width="180">
            <template #default="scope">
              {{ scope.row.DoctorFirstAuthTime }}
            </template>
          </el-table-column>
          <el-table-column label="是否自运行" align="center">
            <!-- @change="(e) => handleSwitchChange(e as boolean, scope.row, 'SelfReliance')" -->
            <template #default="scope">
              <el-switch
                v-if="!tableLoading"
                v-model="scope.row.SelfReliance"
                active-value="是"
                inactive-value="否"
                active-color="#13ce66"
                inactive-color="#e7e9ee"
                :disabled="(userStore.userInfo.Roles as string[]).includes('promoter')"
                @change="(e) => handleSwitchChange(e as string, scope.row, 'SelfReliance')"
              />
            </template>
          </el-table-column>
          <el-table-column label="活跃等级" prop="Liveness" align="center" />
          <el-table-column label="大致开方频次" prop="PrescriptionFrequency" align="center">
            <template #default="scope">
              {{
                scope.row.PrescriptionFrequency
                  ? "一周" + scope.row.PrescriptionFrequency + "次"
                  : ""
              }}
            </template>
          </el-table-column>
          <el-table-column label="累计付费方案数" prop="PayPrescriptionTotalCount" align="center" />
          <el-table-column
            label="最后开方时间"
            prop="RecentlyExcuteTime"
            align="center"
            width="180"
          />
          <el-table-column label="备注" :show-overflow-tooltip="true" align="center" width="180">
            <template #default="scope">
              {{ scope.row.Remark }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="isShowOtherCol"
            label="开方结算比例（有指导人)"
            prop="PrescriptionSettleRatio1"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="开方结算比例（无指导人)"
            prop="PrescriptionSettleRatio2"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="指导结算比例"
            prop="GuideSettleRatio"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="地区"
            prop="Region"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="资质是否齐全"
            prop="IsCertificatesComplete"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="商务开发人"
            prop="MarketingName"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="银行卡号"
            prop="CardNumber"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="开户银行"
            prop="BankName"
            align="center"
            width="120"
          />
          <el-table-column
            v-if="isShowOtherCol"
            label="开户支行"
            prop="SubBranch"
            align="center"
            width="120"
          />
          <el-table-column fixed="right" label="操作" width="120" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleOpenPrescriptionInfo(scope.row)">
                开方信息
              </el-button>
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button
                v-hasNoPermission="['promoter', 'externalSeller']"
                link
                type="primary"
                @click="handlePreviewOrEdit(scope.row, false)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <div v-show="batchOperationMenuShow" ref="menuRef">
      <ul
        id="menu"
        class="menu"
        :style="'top:' + clickPoint.y + 'px;left:' + clickPoint.x + 'px;' + 'width: 150px'"
      >
        <li class="menu_item" @click="handleOperationClick('DelUserClaims')">解绑医助</li>
        <li class="menu_item" @click="handleOperationClick('SetUserClaims')">绑定医助</li>
        <li class="menu_item" @click="handleOperationClick('UpdatePart')">修改主要执业机构</li>
      </ul>
    </div>
    <el-dialog v-model="dialogVisible.setUserClaims" title="绑定医助" width="20%" destroy-on-close>
      <div class="p-10px">
        <div class="flex items-center justify-start m-x-10px">
          <span>医助</span>
          <span class="text-red-500 mr-10px">*</span>
          <UserSelect v-model="setUserClaimsParams.AssistantId" :role-types="['assistant']" />
        </div>
        <div class="flex items-center justify-start m-x-10px mt-10px">
          <span class="mr-10px">备 注</span>
          <el-input v-model="setUserClaimsParams.remark" style="width: 200px" type="textarea" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.setUserClaims = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleSetUserClaims">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="dialogVisible.updatePart"
      title="修改主要执业机构"
      width="20%"
      destroy-on-close
    >
      <div class="p-10px">
        <div class="flex items-center justify-start m-x-10px">
          <span>主要执业机构</span>
          <span class="text-red-500 mr-10px">*</span>
          <el-input v-model="practiceOrganizationName" style="width: 200px" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.updatePart = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleUpdatePart">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="dialogVisible.doctorContent"
      :title="doctorContentTitle"
      width="50%"
      destroy-on-close
    >
      <DoctorContent ref="doctorContentRef" :doctor-info="doctorInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.doctorContent = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleSubmitDoctorInfo"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";
import { DoctorInfo } from "@/api/consult/types";
import dayjs from "dayjs";
import { useUserStore } from "@/store";
import { useClickPoint } from "@/hooks/useClickPoint";
import { useTableConfig } from "@/hooks/useTableConfig";
import Passport_Api from "@/api/passport";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import router from "@/router";
import Report_Api from "@/api/report";
import {
  DoctorRedashItem,
  ExportTaskRedashDTO,
  GetDoctorRedashParamsInputDTO,
} from "@/api/report/types";
import { ExportEnum } from "@/enums/Other";
import { UpdatePartParams } from "@/api/passport/types";

const userStore = useUserStore();
defineOptions({
  name: "DoctorQuery",
  inheritAttrs: false,
});
const practiceOrganizationName = ref<string>("");
const dialogConfirmLoading = ref<boolean>(false);
const batchOperationMenuShow = ref<boolean>(false);
const doctorContentTitle = ref<string>("查看");
const doctorContentRef = useTemplateRef("doctorContentRef");
const dialogVisible = ref<{
  setUserClaims: boolean;
  updatePart: boolean;
  doctorContent: boolean;
}>({
  setUserClaims: false,
  updatePart: false,
  doctorContent: false,
});
const isPreview = ref<boolean>(false);
const doctorInfo = ref<DoctorInfo | null>(null);
const isDoctor = ref<boolean>(false);
const exportLoading = ref<boolean>(false);
const isShowOtherCol = ref<boolean>(false);
const queryResultId = ref<number>(0);
provide("isPreview", isPreview);
provide("isDoctor", isDoctor);
const { clickPoint, handleClick } = useClickPoint();
const { tableLoading, pageData, total, tableRef, selectedTableIds, tableFluidHeight, tableResize } =
  useTableConfig<DoctorRedashItem>();
const menuRef = useTemplateRef("menuRef");
onClickOutside(menuRef, (event) => {
  batchOperationMenuShow.value = false;
});
const queryParams = ref<GetDoctorRedashParamsInputDTO>({
  RoleCode: null,
  Keyword: "",
  IsEnableConsult: null,
  AssistantId: null,
  HasAssistant: null,
  DeptId: null,
  PageIndex: 1,
  PageSize: 10,
  IsSelfReliance: null,
  OrgId: null,
  Liveness: null,
  OrderMode: "0",
  IsBankInfoComplete: null,
  LoginUserId: userStore.userInfo?.Id,
});
const setUserClaimsParams = ref<{
  remark: string;
  AssistantId: string;
}>({
  remark: "",
  AssistantId: "",
});

const handleExportExcel = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  const exportParams = convertToRedashParams<GetDoctorRedashParamsInputDTO>(
    copyData,
    "Report_DoctorInfoList_CH"
  );
  isShowOtherCol.value = true;
  await nextTick();
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    // 这里是特殊的导出方式，需要单独处理
    ExportWay: ExportEnum.PlainClickHouse,
    FileName: `医生信息管理-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId.value,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.value.PageIndex,
    PageSize: 9999999,
    QueryName: "Report_DoctorInfoList_CH",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    isShowOtherCol.value = false;
    exportLoading.value = false;
  }
};

const handleIsBindChange = (value: boolean) => {
  if (!value) {
    queryParams.value.AssistantId = null;
  }
};
const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  // 将普通的数据转换为redash的参数
  const redashParams = convertToRedashParams(copyData, "Report_DoctorInfoList_CH");
  if (redashParams.parameters.Liveness && redashParams.parameters.Liveness !== "*") {
    redashParams.parameters.Liveness = redashParams.parameters.Liveness.join(",");
  }
  tableLoading.value = true;
  const res = await Report_Api.getRedashList<DoctorRedashItem>(redashParams);
  if (res.Type === 200) {
    res.Data.Data.forEach((v) => {
      v.DoctorFirstAuthTime =
        v.DoctorFirstAuthTime && dayjs(v.DoctorFirstAuthTime).format("YYYY-MM-DD HH:mm:ss");
      v.RecentlyExcuteTime =
        v.RecentlyExcuteTime && dayjs(v.RecentlyExcuteTime).format("YYYY-MM-DD HH:mm:ss");
    });
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
    queryResultId.value = res.Data.QueryResultId;
  }
  nextTick(() => {
    tableLoading.value = false;
  });
};
const handleSwitchChange = async (e: string, row: DoctorRedashItem, type: string) => {
  const isSelfReliance = e === "是";
  const data = {
    UserId: row.DoctorId,
    Organization: row.OrgId,
    [type]: isSelfReliance,
    OnlyFillNotNull: 1,
  };
  const res = await Consult_Api.setUserConsult(data);
  if (res.Type !== 200) {
    ElNotification.error(res.Content);
    handleGetTableList();
    return;
  }
  ElNotification.success({
    title: "修改成功",
    message: "刷新之后数据显示会有延迟，请耐心等待",
    type: "success",
  });
};
const handleTableSelect = (selection: DoctorRedashItem[]) => {
  selectedTableIds.value = selection.map((item) => item.DoctorId);
  if (selection.length === 1) {
    // 获取到医生绑定的治疗师
    setUserClaimsParams.value.AssistantId = selection[0].AssistantId ?? "";
    setUserClaimsParams.value.remark = selection[0].Remark ?? "";
  } else {
    setUserClaimsParams.value.AssistantId = "";
    setUserClaimsParams.value.remark = "";
  }
};
const handleBatchOperation = (event: MouseEvent) => {
  if (!selectedTableIds.value.length) {
    ElMessage.warning("请选择医生");
    return;
  }
  handleClick(event);
  clickPoint.value.x = event.clientX - 90;
  clickPoint.value.y = event.clientY + 10;
  batchOperationMenuShow.value = true;
};
const handleOperationClick = (type: string) => {
  switch (type) {
    case "DelUserClaims":
      handleDelUserClaims();
      break;
    case "SetUserClaims":
      if (selectedTableIds.value.length > 1) {
        setUserClaimsParams.value.AssistantId = "";
        setUserClaimsParams.value.remark = "";
      }
      dialogVisible.value.setUserClaims = true;
      break;
    case "UpdatePart":
      practiceOrganizationName.value = "";
      dialogVisible.value.updatePart = true;
      break;
    default:
      break;
  }
};
const handleInitTableSelect = () => {
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
};
const handleDelUserClaims = () => {
  ElMessageBox.confirm(
    "您已选择" + selectedTableIds.value.length + "位医生，确定解绑医助吗？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    const reqData: {
      AssistantId: string | null;
      DoctorId: string;
    }[] = [];
    selectedTableIds.value.forEach((v) => {
      const obj = {
        DoctorId: v,
        AssistantId: null,
      };
      reqData.push(obj);
    });
    Passport_Api.setDefaultAssistant(reqData).then((data) => {
      if (data.Type !== 500) {
        ElNotification({
          title: "成功",
          message: data.Content,
          type: "success",
        });
        handleInitTableSelect();
        handleGetTableList();
      } else {
        ElMessage({
          showClose: true,
          message: data.Content,
          type: "warning",
        });
      }
    });
  });
};
const handleSetUserClaims = () => {
  if (!setUserClaimsParams.value.AssistantId) {
    ElMessage.warning("请选择医助");
    return;
  }
  const dataPar: {
    DoctorId: string;
    AssistantId: string;
  }[] = [];
  selectedTableIds.value.forEach((v) => {
    const obj = {
      DoctorId: v,
      AssistantId: setUserClaimsParams.value.AssistantId,
    };
    dataPar.push(obj);
  });
  dialogConfirmLoading.value = true;
  Passport_Api.setDefaultAssistant(dataPar)
    .then((data) => {
      if (data.Type === 200) {
        ElNotification({
          title: "成功",
          message: data.Content,
          type: "success",
        });
        dialogVisible.value.setUserClaims = false;
        handleInitTableSelect();
        handleGetTableList();
      } else {
        ElMessage({
          showClose: true,
          message: data.Content,
          type: "warning",
        });
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handleUpdatePart = () => {
  if (!practiceOrganizationName.value) {
    ElMessage.warning("请输入主要执业机构");
    return;
  }
  const dataPar: UpdatePartParams[] = [];
  selectedTableIds.value.forEach((v) => {
    const obj = {
      PracticeOrganizationName: practiceOrganizationName.value,
      OnlyFillNotNull: true,
      UserId: v,
    };
    dataPar.push(obj);
  });
  dialogConfirmLoading.value = true;
  Passport_Api.updatePart(dataPar)
    .then((data) => {
      if (data.Type === 200) {
        ElNotification.success(data.Content);
        handleInitTableSelect();
        setTimeout(() => {
          handleGetTableList();
          dialogVisible.value.updatePart = false;
        }, 1000);
      } else {
        ElMessage({
          showClose: true,
          message: data.Content,
          type: "warning",
        });
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handlePreviewOrEdit = async (row: DoctorRedashItem, isPreviewState: boolean) => {
  isPreview.value = isPreviewState;
  isDoctor.value = row.RoleName === "医生";
  doctorContentTitle.value = isPreviewState ? "查看" + row.DoctorName : "编辑" + row.DoctorName;
  const res = await Consult_Api.getDoctorInfo({ DoctorId: row.DoctorId });
  if (res.Type === 200) {
    doctorInfo.value = res.Data;
    dialogVisible.value.doctorContent = true;
  } else {
    ElMessage.error(res.Content);
  }
};
const handleOpenPrescriptionInfo = (row: DoctorRedashItem) => {
  if (!row.DoctorId) {
    ElMessage.warning("医生ID为空");
    return;
  }
  router.push({
    path: "/medical-procedure/treatmentSchemeQuery",
    query: {
      userId: row.DoctorId,
    },
  });
};
const handleSubmitDoctorInfo = async () => {
  const params = await doctorContentRef.value?.handleSubmitInfo();
  if (!params) return;
  dialogConfirmLoading.value = true;
  try {
    const res = await Consult_Api.setDoctorInfo(params);
    if (res.Type !== 200) {
      ElNotification.error(res.Content);
      return;
    }
    ElNotification.success(res.Content);
    handleGetTableList();
    dialogVisible.value.doctorContent = false;
  } finally {
    dialogConfirmLoading.value = false;
  }
};
onActivated(() => {
  handleGetTableList();
});
</script>

<style scoped lang="scss"></style>
