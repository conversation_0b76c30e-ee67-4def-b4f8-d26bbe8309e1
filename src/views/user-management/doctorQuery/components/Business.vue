<template>
  <el-form
    ref="formRef"
    :model="formData"
    label-position="right"
    scroll-to-first-error
    :disabled="isPreview"
  >
    <FormItemContainer>
      <el-form-item label="项目来源">
        <el-radio-group v-model="formData.UserWork.SourceChannel.Tag">
          <el-radio label="医助开发">医助开发</el-radio>
          <el-radio label="销售">销售</el-radio>
          <el-radio label="经销商">经销商</el-radio>
          <el-radio label="其他">其他</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.UserWork.SourceChannel.Tag === '其他'"
        prop="UserWork.SourceChannel.Remark"
        :rules="[
          {
            required: formData.UserWork.SourceChannel.Tag === '其他',
            message: '当选择其他时,请填写具体来源',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="formData.UserWork.SourceChannel.Remark"
          style="width: 100px"
          placeholder="具体来源"
        />
      </el-form-item>
    </FormItemContainer>

    <el-form-item label="项目意向度">
      <el-radio-group v-model="formData.UserWork.ProjectIntention">
        <el-radio :value="0">无</el-radio>
        <el-radio :value="1">低</el-radio>
        <el-radio :value="2">中</el-radio>
        <el-radio :value="3">高</el-radio>
      </el-radio-group>
    </el-form-item>

    <FormItemContainer>
      <el-form-item label="区域">
        <el-input
          v-model="formData.UserWork.Region"
          style="width: 150px"
          placeholder="请输入市级城市"
        />
      </el-form-item>
      <el-form-item label="商务开发人">
        <UserSelect v-model="formData.Settlement.MarketingId" />
      </el-form-item>
      <el-form-item label="上线时间">
        <span>{{ formData.DoctorFirstAuthTime }}</span>
      </el-form-item>
    </FormItemContainer>

    <el-form-item label="结算方式">
      <el-radio-group v-model="formData.Settlement.SettlementMode">
        <el-radio :value="0">周结</el-radio>
        <el-radio :value="1">月结</el-radio>
      </el-radio-group>
    </el-form-item>

    <FormItemContainer class="mb-12px">
      <el-form-item label="开方结算比例" style="margin-bottom: 0 !important">
        <span class="mr-20px">
          开方结算比例（无指导人）：{{
            (setUserSettleRatio.prescriptionSettleRatio1 * 100).toFixed(2)
          }}% 开方结算比例（有指导人）：{{
            (setUserSettleRatio.prescriptionSettleRatio2 * 100).toFixed(2)
          }}% 指导结算比例：{{ (setUserSettleRatio.guideSettleRatio * 100).toFixed(2) }}%
        </span>
      </el-form-item>

      <el-text link class="mx-1 cursor-pointer" type="primary" @click="handleUpdateSettlementRatio">
        详情
      </el-text>
    </FormItemContainer>

    <el-form-item label="跟进记录">
      <div class="follow-up-container">
        <div v-if="!isPreview" class="add-follow-up">
          <div class="add-follow-up-header">
            <div class="input-group">
              <div class="input-item">
                <div class="input-row">
                  <div class="label">跟进人:</div>
                  <UserSelect
                    ref="userSelectRef"
                    v-model="newFollowUpData.user"
                    :role-types="null"
                  />
                </div>
              </div>
              <div class="input-item">
                <div class="input-row">
                  <div class="label">跟进时间:</div>
                  <el-date-picker
                    v-model="newFollowUpData.time"
                    type="datetime"
                    placeholder="选择跟进时间"
                    format="YYYY.MM.DD HH:mm"
                    value-format="YYYY.MM.DD HH:mm"
                    :default-value="new Date()"
                    class="custom-input"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="input-item">
            <div class="input-row">
              <div class="label">跟进内容:</div>
              <el-input
                v-model="newFollowUpData.content"
                type="textarea"
                :rows="3"
                placeholder="请输入跟进内容"
                class="custom-input"
              />
            </div>
          </div>

          <el-button
            type="primary"
            class="submit-btn"
            :disabled="
              !newFollowUpData.user || !newFollowUpData.time || !newFollowUpData.content.trim()
            "
            :loading="followUpLoading"
            @click="handleAddFollowUp"
          >
            添加跟进记录
          </el-button>
        </div>

        <div class="follow-up-list">
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in displayedRecords"
              :key="index"
              :timestamp="record.EventTime"
              placement="top"
            >
              <div class="follow-up-item">
                <div class="follow-up-header">
                  <span class="follow-up-user">{{ record.DirectorName }}</span>
                  <div v-if="!isPreview" class="follow-up-actions">
                    <el-button type="primary" link @click="handleEdit(index)">编辑</el-button>
                    <el-popconfirm title="确认删除这条跟进记录吗？" @confirm="handleDelete(index)">
                      <template #reference>
                        <el-button type="danger" link>删除</el-button>
                      </template>
                    </el-popconfirm>
                  </div>
                </div>
                <div v-if="editingRecord.Id !== record.Id" class="follow-up-content">
                  {{ record.Remark }}
                </div>
                <div v-else class="edit-form">
                  <div class="input-group">
                    <div class="input-item">
                      <div class="input-row">
                        <div class="label">跟进人:</div>
                        <UserSelect
                          ref="userSelectRef1"
                          v-model="editingRecord.DirectorId"
                          :role-types="['assistant']"
                        />
                      </div>
                    </div>
                    <div class="input-item">
                      <div class="input-row">
                        <div class="label">跟进时间:</div>
                        <el-date-picker
                          v-model="editingRecord.EventTime"
                          type="datetime"
                          placeholder="选择跟进时间"
                          format="YYYY.MM.DD HH:mm"
                          value-format="YYYY.MM.DD HH:mm"
                          class="custom-input"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="input-item">
                    <div class="input-row">
                      <div class="label">跟进内容:</div>
                      <el-input
                        v-model="editingRecord.Remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入跟进内容"
                        class="custom-input"
                      />
                    </div>
                  </div>
                  <div class="edit-actions">
                    <el-button @click="handleEditCancel">取消</el-button>
                    <el-button
                      type="primary"
                      :disabled="
                        !editingRecord.DirectorId ||
                        !editingRecord.EventTime ||
                        !editingRecord.Remark.trim()
                      "
                      :loading="followUpLoading"
                      @click="handleEditConfirm"
                    >
                      确认
                    </el-button>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-form-item>
  </el-form>

  <!-- 将展开/收起按钮移到表单外部 -->
  <div v-if="followUpRecords.length > defaultDisplayCount" class="expand-section">
    <div class="expand-divider">
      <span class="divider-line" />
    </div>
    <el-button class="expand-button" type="primary" link @click="toggleExpand">
      {{ isExpanded ? "收起记录" : "查看更多记录" }}
      <el-icon class="expand-icon" :class="{ 'is-expanded': isExpanded }">
        <ArrowDown />
      </el-icon>
    </el-button>
  </div>
  <el-dialog v-model="showSettlement" title="编辑结算比例" width="30%" destroy-on-close>
    <UserSettlement ref="userSettlementRef" :user-settlement-info="userSettlementInfo" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showSettlement = false">取消</el-button>
        <el-button
          v-if="!isPreview"
          type="primary"
          :loading="dialogConfirmLoading"
          @click="handleSetUserSettlement"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { dayjs, EpPropMergeTypeWithNull, FormInstance } from "element-plus";
import { ref, inject, Ref, computed } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import Passport_Api from "@/api/passport";
import { UserEventItem } from "@/api/passport/types";
import UserSelect from "@/components/UserSelect/index.vue";
import Report_Api from "@/api/report";
import { SettlementInfo } from "@/api/report/types";
import UserSettlement from "./UserSettlement.vue";
import Consult_Api from "@/api/consult";

interface PageSetUserSettleRatio {
  prescriptionSettleRatio1: number;
  prescriptionSettleRatio2: number;
  guideSettleRatio: number;
}
export interface PageBusinessData {
  UserWork: {
    SourceChannel: string;
    ProjectIntention: EpPropMergeTypeWithNull<number>;
    Region: string;
  };
  Settlement: {
    MarketingId: EpPropMergeTypeWithNull<string>;
    SettlementMode: EpPropMergeTypeWithNull<number>;
    PrescriptionSettleRatio1: number;
    PrescriptionSettleRatio2: number;
    GuideSettleRatio: number;
  };
  DoctorFirstAuthTime: string;
}
export interface PageExtendBusinessData extends Omit<PageBusinessData, "UserWork"> {
  UserWork: {
    SourceChannel: {
      Tag: string;
      Remark: string;
    };
    ProjectIntention: EpPropMergeTypeWithNull<number>;
    Region: string;
  };
}
const userSelectRef = ref<InstanceType<typeof UserSelect>>();
const userSelectRef1 = ref<InstanceType<typeof UserSelect>[]>([]);
const formRef = ref<FormInstance>();
const formData = ref<PageExtendBusinessData>({
  UserWork: {
    SourceChannel: {
      Tag: "",
      Remark: "",
    },
    ProjectIntention: null,
    Region: "",
  },
  Settlement: {
    MarketingId: null,
    SettlementMode: null,
    PrescriptionSettleRatio1: 0,
    PrescriptionSettleRatio2: 0,
    GuideSettleRatio: 0,
  },
  DoctorFirstAuthTime: "",
});
const doctorId = inject<Ref<string>>("doctorId", ref(""));
const followUpLoading = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const userSettlementRef = ref<InstanceType<typeof UserSettlement>>();

// 将跟进记录分离为独立的响应式数据
const followUpRecords = ref<UserEventItem[]>([]);

const newFollowUpData = ref({
  user: "",
  time: "",
  content: "",
});

const editingRecord = ref<UserEventItem>({
  Id: "",
  UserId: "",
  Remark: "",
  EventTime: "",
  DirectorId: "",
  DirectorName: "",
  Type: 0,
});

const isPreview = inject<Ref<boolean>>("isPreview", ref(false));
const defaultDisplayCount = 1; // 默认显示的记录数
const isExpanded = ref<boolean>(false);
const showSettlement = ref<boolean>(false);
const userSettlementInfo = ref<SettlementInfo | null>(null);
const setUserSettleRatio = ref<PageSetUserSettleRatio>({
  prescriptionSettleRatio1: 0,
  prescriptionSettleRatio2: 0,
  guideSettleRatio: 0,
});

// 更新计算属性中的引用
const displayedRecords = computed(() => {
  if (isExpanded.value || followUpRecords.value.length <= defaultDisplayCount) {
    return followUpRecords.value;
  }
  return followUpRecords.value.slice(0, defaultDisplayCount);
});

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const handleUpdateSettlementRatio = async () => {
  const res = await Report_Api.getRedashList<SettlementInfo>({
    queryName: "Report_SettlementInfo",
    parameters: {
      RoleName: "*",
      OrgIds: "*",
      DeptId: "*",
      DeptChiefId: "*",
      PresidentId: "*",
      NurseChiefId: "*",
      Keyword: "*",
      UserId: doctorId.value,
    },
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 1,
  });
  userSettlementInfo.value = res.Data.Data[0];
  showSettlement.value = true;
};
const handleSetUserSettlement = () => {
  const params = userSettlementRef.value?.handleSubmitInfo();
  console.log("params", params);
  if (!params) return;
  dialogConfirmLoading.value = true;
  Consult_Api.setSettlements({
    Mode: 0,
    Settlements: [
      {
        ...params,
        UserId: doctorId.value,
      },
    ],
  })
    .then((res) => {
      if (res.Type === 200) {
        // 直接修改显示的内容
        handleSetUserSettleRatio(
          params.PrescriptionSettleRatio1,
          params.PrescriptionSettleRatio2,
          params.GuideSettleRatio
        );
        showSettlement.value = false;
      } else {
        ElNotification.error({
          title: "修改失败",
          message: res.Content,
        });
      }
    })
    .catch((err) => {
      ElNotification.error({
        title: "修改失败",
        message: err,
      });
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleSetUserSettleRatio = (value1: number, value2: number, value3: number) => {
  setUserSettleRatio.value = {
    prescriptionSettleRatio1: value1,
    prescriptionSettleRatio2: value2,
    guideSettleRatio: value3,
  };
};

const handleAddFollowUp = async () => {
  if (
    !newFollowUpData.value.user ||
    !newFollowUpData.value.time ||
    !newFollowUpData.value.content.trim()
  )
    return;
  followUpLoading.value = true;
  const user = userSelectRef.value!.userOptions.find(
    (item) => item.Id === newFollowUpData.value.user
  )!;
  const res = await Passport_Api.insertUserEvent([
    {
      UserId: doctorId.value,
      Remark: newFollowUpData.value.content,
      EventTime: newFollowUpData.value.time,
      DirectorId: user.Id,
      DirectorName: user.Name,
      Type: 0,
    },
  ]);
  if (res.Type === 200) {
    handleGetUserEvent();
  }
  // 重置表单
  newFollowUpData.value = {
    user: "",
    time: "",
    content: "",
  };
  followUpLoading.value = false;
};

const handleEdit = async (index: number) => {
  const record = followUpRecords.value[index];
  await nextTick();
  editingRecord.value = record;
};

const handleEditConfirm = async () => {
  if (!editingRecord.value?.Remark.trim()) return;

  // 调用编辑接口
  const user = userSelectRef1.value[0]!.userOptions.find(
    (item) => item.Id === editingRecord.value.DirectorId
  )!;
  editingRecord.value.DirectorName = user.Name;
  const res = await Passport_Api.updateUserEvent([editingRecord.value]);
  if (res.Type === 200) {
    handleGetUserEvent();
  }

  handleEditCancel();
};

const handleEditCancel = () => {
  editingRecord.value = {
    Id: "",
    UserId: "",
    Remark: "",
    EventTime: "",
    DirectorId: "",
    DirectorName: "",
    Type: 0,
  };
};

const handleDelete = async (index: number) => {
  const res = await Passport_Api.deleteUserEvent({ Ids: [followUpRecords.value[index].Id!] });
  if (res.Type === 200) {
    followUpRecords.value.splice(index, 1);
  }
};

const handleFormSubmit = (): Promise<PageBusinessData | null> => {
  return new Promise<PageBusinessData | null>((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve({
          ...formData.value,
          UserWork: {
            ...formData.value.UserWork,
            SourceChannel: JSON.stringify(formData.value.UserWork.SourceChannel),
          },
        });
      } else {
        resolve(null);
      }
    });
  });
};

const handleProcessData = (data: PageBusinessData) => {
  const obj: PageExtendBusinessData = {
    ...data,
    UserWork: {
      SourceChannel: {
        Tag: "",
        Remark: "",
      },
      ProjectIntention: data.UserWork.ProjectIntention,
      Region: data.UserWork.Region,
    },
  };
  if (data.UserWork.SourceChannel) {
    obj.UserWork.SourceChannel = JSON.parse(data.UserWork.SourceChannel);
  }
  formData.value = obj;
  handleSetUserSettleRatio(
    obj.Settlement.PrescriptionSettleRatio1,
    obj.Settlement.PrescriptionSettleRatio2,
    obj.Settlement.GuideSettleRatio
  );
};
const handleGetUserEvent = async () => {
  if (!doctorId.value) return;
  const res = await Passport_Api.getUserEvent({
    UserId: doctorId.value,
    Type: 0,
    PageIndex: 1,
    PageSize: 100,
  });
  if (res.Type === 200) {
    res.Data.Rows.forEach((item) => {
      item.EventTime = dayjs(item.EventTime).format("YYYY-MM-DD HH:mm");
    });
    followUpRecords.value = res.Data.Rows;
  }
};
interface Props {
  businessInfo: PageBusinessData | null;
}
const props = defineProps<Props>();
watch(
  () => props.businessInfo,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue: PageBusinessData = JSON.parse(JSON.stringify(newVal));
    handleProcessData(copyValue);
  },
  { immediate: true }
);
defineExpose({
  handleFormSubmit,
});
onMounted(() => {
  handleGetUserEvent();
});
</script>

<style lang="scss" scoped>
.el-form {
  max-width: 900px;
  margin: 0 auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.follow-up-container {
  width: 100%;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

.follow-up-item {
  background: #fff;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }

  .edit-form {
    margin-top: 12px;

    .input-group {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .input-item {
        flex: 1;
      }
    }

    .input-item {
      .input-row {
        display: flex;
        align-items: flex-start;

        .label {
          min-width: 60px;
          font-size: 13px;
          font-weight: 500;
          color: #606266;
          text-align: right;
          margin-right: 8px;
        }
      }
    }

    .custom-input {
      flex: 1;
      min-width: 0;

      :deep(.el-input__wrapper),
      :deep(.el-textarea__inner) {
        box-shadow: 0 0 0 1px #dcdfe6;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc;
        }

        &:focus-within {
          box-shadow: 0 0 0 1px #409eff;
        }
      }
    }

    .edit-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 12px;
    }
  }
}

.follow-up-header {
  margin-bottom: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .follow-up-user {
    font-size: 13px;
    font-weight: 500;
    color: #409eff;
  }

  .follow-up-actions {
    display: flex;
    gap: 6px;
  }
}

.follow-up-content {
  color: #606266;
  line-height: 1.4;
  font-size: 13px;
}

.add-follow-up {
  position: relative;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;

  &::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
    height: 1px;
    background: linear-gradient(90deg, transparent, #dcdfe6, transparent);
  }

  .add-follow-up-header {
    margin-bottom: 16px;
  }

  .input-group {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .input-item {
      flex: 1;
    }
  }

  .input-item {
    .input-row {
      display: flex;
      align-items: flex-start;

      .label {
        min-width: 60px;
        font-size: 13px;
        font-weight: 500;
        color: #606266;
        text-align: right;
        margin-right: 8px;
      }
    }
  }

  .custom-input {
    flex: 1;
    min-width: 0;

    :deep(.el-input__wrapper),
    :deep(.el-textarea__inner) {
      box-shadow: 0 0 0 1px #dcdfe6;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc;
      }

      &:focus-within {
        box-shadow: 0 0 0 1px #409eff;
      }
    }
  }

  .submit-btn {
    margin-top: 16px;
    margin-left: 70px;
    width: 100px;
    height: 32px;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;

    &:not(:disabled):hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }
}

.follow-up-list {
  margin-top: 20px;
}

.mt-2 {
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.expand-section {
  position: relative;
  text-align: center;
  margin: 16px 0;

  .expand-divider {
    position: relative;
    height: 1px;
    margin-bottom: -10px;

    .divider-line {
      position: absolute;
      left: 0;
      right: 0;
      top: 50%;
      height: 1px;
      background: linear-gradient(90deg, transparent, #dcdfe6 20%, #dcdfe6 80%, transparent);
      z-index: 1;
    }
  }

  .expand-button {
    position: relative;
    padding: 6px 20px;
    font-size: 13px;
    font-weight: 500;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 12px;
    transition: all 0.3s ease;
    z-index: 2;

    &:hover {
      color: #409eff;
      border-color: #409eff;
      background: #ecf5ff;
    }

    .expand-icon {
      margin-left: 4px;
      font-size: 12px;
      transition: transform 0.3s ease;

      &.is-expanded {
        transform: rotate(180deg);
      }
    }
  }
}

// 添加列表展开/收起动画
.el-timeline-item {
  transition: all 0.3s ease-in-out;
}
</style>
