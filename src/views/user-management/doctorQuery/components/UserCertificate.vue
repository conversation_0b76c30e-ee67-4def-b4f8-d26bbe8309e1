<template>
  <div class="h-400px overflow-y-auto">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="right"
      scroll-to-first-error
      :disabled="isPreview"
      style="padding: 0 !important"
    >
      <el-form-item label="身份证" prop="idCard">
        <el-input
          v-model="certificates.idCard"
          disabled
          type="text"
          style="width: 150px"
          resize="none"
        />
      </el-form-item>
      <FormItemContainer>
        <el-form-item label="身份证正面" prop="idCard_front">
          <SingleImageUpload v-model="certificates.idCard_front" disabled />
        </el-form-item>
        <el-form-item label="身份证背面" prop="idCard_back">
          <SingleImageUpload v-model="certificates.idCard_back" disabled />
        </el-form-item>
      </FormItemContainer>
      <el-form-item label="职业类型" prop="WorkerType">
        <el-select
          v-model="formData.WorkerType"
          placeholder="职业类型"
          style="width: 150px"
          @change="handleChangeWorkerType"
        >
          <el-option
            v-for="(item, index) in workerTypeDict"
            :key="index"
            :label="item.Key"
            :value="item.Value!"
          />
        </el-select>
      </el-form-item>
      <template v-if="formData.WorkerType === 'doctor'">
        <el-form-item label="医师资格证编号：">
          <el-input
            v-model="certificates.doctorQualify_Number"
            type="text"
            style="width: 150px"
            resize="none"
          />
        </el-form-item>
        <el-form-item label="资格证:" prop="doctorQualify_Img">
          <MultiImageUpload v-model="certificates.doctorQualify_Img" :limit="3" />
        </el-form-item>
        <el-form-item label="医师执业证编号：">
          <el-input
            v-model="certificates.doctorPracticeQualify_Number"
            type="text"
            style="width: 150px"
            resize="none"
          />
        </el-form-item>
        <el-form-item label="执业证:" prop="doctorPracticeQualify_Img">
          <MultiImageUpload v-model="certificates.doctorPracticeQualify_Img" :limit="3" />
        </el-form-item>
        <el-form-item label="职称证编码：">
          <el-input
            v-model="certificates.worker_title_Number"
            type="text"
            style="width: 150px"
            resize="none"
          />
        </el-form-item>
        <el-form-item label="电子执业证照:">
          <MultiImageUpload v-model="certificates.electronicPractice_Img" />
        </el-form-item>
      </template>
      <template v-if="formData.WorkerType === 'therapist'">
        <el-form-item label="卫生专业技术资格证书编号：">
          <el-input
            v-model="certificates.therapistQualify_Number"
            type="text"
            style="width: 150px"
            resize="none"
          />
        </el-form-item>
        <el-form-item label="资格证:" prop="therapistQualify_Img">
          <MultiImageUpload v-model="certificates.therapistQualify_Img" :limit="3" />
        </el-form-item>
      </template>
      <template v-if="formData.WorkerType === 'nurse'">
        <el-form-item label="护士资格证编号：">
          <el-input
            v-model="certificates.nurseQualify_Number"
            type="text"
            class="w150"
            resize="none"
          />
        </el-form-item>
        <el-form-item label="资格证:" prop="nurseQualify_Img">
          <MultiImageUpload v-model="certificates.nurseQualify_Img" :limit="3" />
        </el-form-item>
        <el-form-item label="护士执业证编号：">
          <el-input
            v-model="certificates.nursePracticeQualify_Number"
            type="text"
            style="width: 150px"
            resize="none"
          />
        </el-form-item>
        <el-form-item label="执业证:" prop="nursePracticeQualify_Img">
          <MultiImageUpload v-model="certificates.nursePracticeQualify_Img" :limit="3" />
        </el-form-item>
      </template>
      <template v-if="formData.WorkerType !== 'other'">
        <el-form-item label="职称" prop="WorkerTitle">
          <el-select v-model="formData.WorkerTitle" placeholder="职称" style="width: 150px">
            <el-option
              v-for="(item, index) in workerTitleFilterDict"
              :key="index"
              :label="item.Key"
              :value="item.Value!"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职称证:">
          <MultiImageUpload v-model="certificates.worker_title_Img" :limit="3" />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { getDictionaryList } from "@/utils/dict";
import { PageUserCertificate } from "./types";
import { FormRules } from "element-plus";

const formData = ref<PageUserCertificate>({
  UserId: "",
  UpsertUserCertificateMode: 1,
  UserCertificates: [],
  WorkerTitle: "",
  WorkerType: "",
});
const certificates = ref<Record<string, any>>({
  // 预设所有可能的证书字段
  idCard: "",
  idCard_front: "",
  idCard_back: "",
  doctorQualify_Number: "",
  doctorQualify_Img: [],
  doctorPracticeQualify_Number: "",
  doctorPracticeQualify_Img: [],
  electronicPractice_Img: [],
  worker_title_Number: "",
  worker_title_Img: [],
  therapistQualify_Number: "",
  therapistQualify_Img: [],
  nurseQualify_Number: "",
  nurseQualify_Img: [],
  nursePracticeQualify_Number: "",
  nursePracticeQualify_Img: [],
});
const workerTitleDict = ref<ReadDict[]>([]);
const workerTypeDict = ref<ReadDict[]>([]);
const workerTitleFilterDict = ref<ReadDict[]>([]);
const formRef = useTemplateRef("formRef");

const isPreview = inject<Ref<boolean>>("isPreview", ref(false));
const doctorId = inject<Ref<string>>("doctorId", ref(""));

const validateArrayRequired = (rule: any, value: any[], callback: Function) => {
  if (!value || value.length === 0) {
    callback(new Error(rule.message));
  } else {
    callback();
  }
};

const validateWorkerTitle = (rule: any, value: string, callback: Function) => {
  if (formData.value.WorkerType !== "other" && !value) {
    callback(new Error("请选择职称"));
  } else {
    callback();
  }
};

/**
 * 创建图片验证器工厂函数
 * @param workerType 需要验证的职业类型
 * @param certificateField 证件图片字段名
 * @param errorMessage 错误提示信息
 * @returns 验证器函数
 */
const createImageValidator = (
  workerType: string,
  certificateField: string,
  errorMessage: string
) => {
  return (rule: any, value: any, callback: Function) => {
    // 检查职业类型是否匹配
    if (formData.value.WorkerType !== workerType) {
      callback();
      return;
    }

    // 验证图片数组
    const images = certificates.value[certificateField];
    if (!images || !Array.isArray(images) || images.length === 0) {
      callback(new Error(errorMessage));
      return;
    }

    // 检查是否有至少一个有效图片
    const hasValidImages = images.some(
      (img) => img && typeof img === "string" && img.trim() !== ""
    );
    if (!hasValidImages) {
      callback(new Error(errorMessage));
      return;
    }

    callback();
  };
};

const rules = reactive<FormRules>({
  WorkerType: [{ required: true, message: "请选择职业类型", trigger: "change" }],
  WorkerTitle: [{ validator: validateWorkerTitle, trigger: "change" }],

  // 使用工厂函数创建的图片验证器
  doctorQualify_Img: [
    {
      validator: createImageValidator("doctor", "doctorQualify_Img", "请上传医师资格证"),
      trigger: "change",
    },
  ],
  doctorPracticeQualify_Img: [
    {
      validator: createImageValidator("doctor", "doctorPracticeQualify_Img", "请上传医师执业证"),
      trigger: "change",
    },
  ],
  therapistQualify_Img: [
    {
      validator: createImageValidator(
        "therapist",
        "therapistQualify_Img",
        "请上传卫生专业技术资格证书"
      ),
      trigger: "change",
    },
  ],
  nurseQualify_Img: [
    {
      validator: createImageValidator("nurse", "nurseQualify_Img", "请上传护士资格证"),
      trigger: "change",
    },
  ],
  nursePracticeQualify_Img: [
    {
      validator: createImageValidator("nurse", "nursePracticeQualify_Img", "请上传护士执业证"),
      trigger: "change",
    },
  ],
});

const handleProcessingCertificateDetail = async (value: PageUserCertificate) => {
  if (!value) return;
  await handleGetWorkTitleList();
  await handleGetWorkerTypeList();
  handleChangeWorkerType(value.WorkerType);
  value.UserCertificates.forEach((s) => {
    if (s.CertificateType.includes("Img") && s.CertificateValue) {
      certificates.value[s.CertificateType] = JSON.parse(s.CertificateValue);
    } else {
      certificates.value[s.CertificateType] = s.CertificateValue;
    }
  });
  formData.value = value;
};

const handleSubmitInfo = (): Promise<PageUserCertificate[] | null> => {
  return new Promise((resolve) => {
    formRef.value?.clearValidate();
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        const UserCertificates: UserCertificate[] = [];
        Object.entries(certificates.value).forEach(([key, value]) => {
          let certValue: string = "";
          if (value === null || value === undefined) {
            certValue = "";
          } else if (typeof value === "object") {
            certValue = JSON.stringify(value);
          } else {
            certValue = String(value);
          }

          const item: UserCertificate = {
            CertificateType: key,
            CertificateValue: certValue,
          };
          UserCertificates.push(item);
        });
        const params = [
          {
            UpsertUserCertificateMode: 1,
            UserId: doctorId.value,
            WorkerTitle: formData.value.WorkerTitle,
            WorkerType: formData.value.WorkerType,
            UserCertificates,
          },
        ];
        resolve(params);
      } else {
        resolve(null);
      }
    });
  });
};

const handleGetWorkTitleList = async () => {
  const list = await getDictionaryList("WorkerTitleDict");
  workerTitleDict.value = list;
  workerTitleFilterDict.value = list;
};

const handleGetWorkerTypeList = async () => {
  const list = await getDictionaryList("WorkerTypeDict");
  workerTypeDict.value = list;
};
const handleChangeWorkerType = (e: string) => {
  const item = workerTypeDict.value.find((s) => s.Value === e);
  console.log(item);
  if (item) {
    const id = item.Id;
    formData.value.WorkerTitle = "";
    handleGetFilterWorkerTitleList(id as string);
  }
};
const handleGetFilterWorkerTitleList = (id?: string) => {
  if (!id) {
    workerTitleFilterDict.value = workerTitleDict.value;
    return;
  }
  workerTitleFilterDict.value = workerTitleDict.value.filter((s) => s.ParentId === id);
};

interface Props {
  userCertificateInfo: PageUserCertificate;
}
const props = defineProps<Props>();
watch(
  () => props.userCertificateInfo,
  (newVal) => {
    handleProcessingCertificateDetail(newVal);
  },
  { immediate: true }
);
defineExpose({
  handleSubmitInfo,
});
</script>

<style lang="scss" scoped></style>
