<template>
  <div class="h-400px">
    <el-tabs type="card">
      <el-tab-pane label="结算比例">
        <FormItem label="开方结算比例1" required>
          <el-input-number
            v-model="info.PrescriptionSettleRatio1"
            :min="0"
            :step="0.01"
            step-strictly
            :max="1"
            label="开方结算比例1"
            style="width: 150px"
            :disabled="isPreview"
          />
          <span style="color: gray; font-size: 11px">（下达方案结算比例）</span>
        </FormItem>
        <FormItem label="开方结算比例2" required>
          <el-input-number
            v-model="info.PrescriptionSettleRatio2"
            :min="0"
            :step="0.01"
            step-strictly
            :max="1"
            label="开方结算比例2"
            style="width: 150px"
            :disabled="isPreview"
          />
          <span style="color: gray; font-size: 11px">（单独下方，无指导人时结算比例）</span>
        </FormItem>
        <FormItem required label="指导结算比例">
          <el-input-number
            v-model="info.GuideSettleRatio"
            :min="0"
            :step="0.01"
            step-strictly
            :max="1"
            label="指导结算比例"
            style="width: 150px"
            :disabled="isPreview"
          />
          <span style="color: gray; font-size: 11px">（被指派时结算比例）</span>
        </FormItem>
        <FormItem label="管理结算比例(科主任)">
          <el-input-number
            v-model="info.DeptChiefSettleRatio"
            :min="0"
            :step="0.01"
            step-strictly
            :max="1"
            label="管理结算比例(科主任)"
            style="width: 150px"
            :disabled="isPreview"
          />
          <span style="color: gray; font-size: 11px">（医疗质量监管和技术指导,结算比例）</span>
        </FormItem>
        <FormItem label="管理结算比例(院长)">
          <el-input-number
            v-model="info.PresidentSettleRatio"
            :min="0"
            :step="0.01"
            step-strictly
            :max="1"
            label="管理结算比例(院长)"
            style="width: 150px"
            :disabled="isPreview"
          />
          <span style="color: gray; font-size: 11px">（医疗质量监管及科室管理，结算比例）</span>
        </FormItem>
        <FormItem label="管理结算比例(护士长)">
          <el-input-number
            v-model="info.NurseChiefSettleRatio"
            :min="0"
            :step="0.01"
            step-strictly
            :max="1"
            label="管理结算比例(护士长)"
            style="width: 150px"
            :disabled="isPreview"
          />
          <span style="color: gray; font-size: 11px">（管理管床护士,结算比例）</span>
        </FormItem>
      </el-tab-pane>
      <el-tab-pane label="绑定">
        <FormItem label="医疗质量监管(科主任)">
          <UserSelect
            v-model="info.BindDeptChiefId"
            placeholder="请选择医疗质量监管"
            style="width: 150px"
            :role-types="['doctor', 'therapist', 'nurse']"
            :disabled="isPreview"
          />
        </FormItem>
        <FormItem label="医疗质量监管(院长)">
          <UserSelect
            v-model="info.BindPresidentId"
            placeholder="医疗质量监管"
            style="width: 150px"
            :role-types="['doctor', 'therapist', 'nurse']"
            :disabled="isPreview"
          />
        </FormItem>
        <FormItem label="医疗质量监管(护士长)">
          <UserSelect
            v-model="info.BindNurseChiefId"
            placeholder="医疗质量监管"
            style="width: 150px"
            :role-types="['nurse']"
            :disabled="isPreview"
          />
        </FormItem>
      </el-tab-pane>
      <el-tab-pane label="其他绑定">
        <div class="flex">
          <FormItem label="市场开发">
            <UserSelect
              v-model="info.MarketingId"
              placeholder="市场开发"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
          <FormItem label="结算比例">
            <el-input-number
              v-model="info.MarketingSettleRatio"
              :min="0"
              :step="0.01"
              step-strictly
              :max="1"
              label="结算比例"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
        </div>
        <div class="flex">
          <FormItem label="医助管理">
            <UserSelect
              v-model="info.AssistantManagerId"
              placeholder="医助管理"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
          <FormItem label="结算比例">
            <el-input-number
              v-model="info.AssistantManagerSettleRatio"
              :min="0"
              :step="0.01"
              step-strictly
              :max="1"
              label="结算比例"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
        </div>
        <div class="flex">
          <FormItem label="上线人员">
            <UserSelect
              v-model="info.OnlineGuidanceId"
              placeholder="上线人员"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
          <FormItem label="结算比例">
            <el-input-number
              v-model="info.OnlineGuidanceSettleRatio"
              :min="0"
              :step="0.01"
              step-strictly
              :max="1"
              label="结算比例"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
        </div>
        <div class="flex">
          <FormItem label="经销商">
            <UserSelect
              v-model="info.DealerId"
              placeholder="经销商"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
          <FormItem label="结算比例">
            <el-input-number
              v-model="info.DealerSettleRatio"
              :min="0"
              :step="0.01"
              step-strictly
              :max="1"
              label="结算比例"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
        </div>
        <div class="flex">
          <FormItem label="推荐人">
            <UserSelect
              v-model="info.ReferrerId"
              placeholder="推荐人"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
          <FormItem label="结算比例">
            <el-input-number
              v-model="info.ReferrerSettleRatio"
              :min="0"
              :step="0.01"
              step-strictly
              :max="1"
              label="结算比例"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
        </div>
        <div class="flex">
          <FormItem label="其他">
            <UserSelect
              v-model="info.OtherId"
              placeholder="其他"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
          <FormItem label="结算比例">
            <el-input-number
              v-model="info.OtherSettleRatio"
              :min="0"
              :step="0.01"
              step-strictly
              :max="1"
              label="结算比例"
              style="width: 150px"
              :disabled="isPreview"
            />
          </FormItem>
        </div>
        <FormItem label="医助结算比例">
          <el-input-number
            v-model="info.AssistantSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
            :disabled="isPreview"
          />
        </FormItem>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { UserSettlement } from "@/api/consult/types";
import { SettlementInfo } from "@/api/report/types";

interface Props {
  userSettlementInfo: SettlementInfo | null;
}

const info = ref<UserSettlement>({
  PrescriptionSettleRatio1: null,
  PrescriptionSettleRatio2: null,
  GuideSettleRatio: null,
  DeptChiefSettleRatio: null,
  PresidentSettleRatio: null,
  NurseChiefSettleRatio: null,
  BindDeptChiefId: "",
  BindPresidentId: "",
  BindNurseChiefId: "",
  MarketingId: "",
  MarketingSettleRatio: null,
  AssistantManagerId: "",
  AssistantManagerSettleRatio: null,
  OnlineGuidanceId: "",
  OnlineGuidanceSettleRatio: null,
  DealerId: "",
  DealerSettleRatio: null,
  ReferrerId: "",
  ReferrerSettleRatio: null,
  OtherId: "",
  OtherSettleRatio: null,
  AssistantSettleRatio: null,
});
const isPreview = inject<Ref<boolean>>("isPreview", ref(false));

const handleSubmitInfo = (): UserSettlement | null => {
  if (
    info.value.PrescriptionSettleRatio1 === null ||
    info.value.PrescriptionSettleRatio2 === null ||
    info.value.GuideSettleRatio === null
  ) {
    ElMessage.error("结算比例存在未填写完整的数据");
    return null;
  }
  return info.value;
};

const handleProcessingSettlementDetail = (detail: SettlementInfo | null) => {
  if (!detail) return;
  Object.keys(detail).forEach(([key, value]) => {
    if (key.includes("Ratio")) {
      detail[key] = Number(value);
    }
  });
  info.value = {
    PrescriptionSettleRatio1: detail.PrescriptionSettleRatio1,
    PrescriptionSettleRatio2: detail.PrescriptionSettleRatio2,
    GuideSettleRatio: detail.GuideSettleRatio,
    DeptChiefSettleRatio: detail.DeptChiefSettleRatio,
    PresidentSettleRatio: detail.PresidentSettleRatio,
    NurseChiefSettleRatio: detail.NurseChiefSettleRatio,
    BindDeptChiefId: detail.BindDeptChiefId,
    BindPresidentId: detail.BindPresidentId,
    BindNurseChiefId: detail.BindNurseChiefId,
    MarketingId: detail.MarketingId,
    MarketingSettleRatio: detail.MarketingSettleRatio,
    AssistantManagerId: detail.AssistantManagerId,
    AssistantManagerSettleRatio: detail.AssistantManagerSettleRatio,
    OnlineGuidanceId: detail.OnlineGuidanceId,
    OnlineGuidanceSettleRatio: detail.OnlineGuidanceSettleRatio,
    DealerId: detail.DealerId,
    DealerSettleRatio: detail.DealerSettleRatio,
    ReferrerId: detail.ReferrerId,
    ReferrerSettleRatio: detail.ReferrerSettleRatio,
    OtherId: detail.OtherId,
    OtherSettleRatio: detail.OtherSettleRatio,
    AssistantSettleRatio: detail.AssistantSettleRatio,
  };
};

const props = defineProps<Props>();
watch(
  () => props.userSettlementInfo,
  (newVal) => {
    handleProcessingSettlementDetail(newVal);
  },
  { immediate: true }
);
defineExpose({
  handleSubmitInfo,
});
</script>

<style lang="scss" scoped></style>
