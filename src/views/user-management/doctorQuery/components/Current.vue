<template>
  <el-form
    ref="formRef"
    :model="formData"
    label-position="right"
    scroll-to-first-error
    :disabled="isPreview"
    @submit.prevent="handleFormSubmit"
  >
    <el-form-item label="医助" prop="doctorType">
      <UserSelect
        ref="creatorSelectRef"
        v-model="formData.AssistantFollowRecords[defaultAssistantIndex].AssistantId"
        :role-types="['assistant']"
        style="width: 180px"
        @change="(e) => onChangeAssistant(e as string)"
      />
    </el-form-item>

    <el-form-item label="患者病种">
      <div class="tag-group">
        <el-tag
          v-for="item in formData.DiagnoseNames"
          :key="item.DiagnoseName"
          size="large"
          type="info"
        >
          {{ item.DiagnoseName }} ({{ item.Count }})
        </el-tag>
      </div>
    </el-form-item>

    <el-form-item label="开方偏好">
      <div class="tag-group">
        <el-tag
          v-for="item in formData.MoItemNames"
          :key="item.MoItemName"
          size="large"
          type="info"
        >
          {{ item.MoItemName }} ({{ item.Count }})
        </el-tag>
      </div>
    </el-form-item>
    <el-form-item label="运行问题点">
      <el-checkbox-group v-model="formData.UserWork.Questions.Tag">
        <el-checkbox value="不积极">不积极</el-checkbox>
        <el-checkbox value="怕出问题">怕出问题</el-checkbox>
        <el-checkbox value="操作多点执业">操作多点执业</el-checkbox>
        <el-checkbox value="医生多点执业">医生多点执业</el-checkbox>
        <el-checkbox value="没用过的经验">没用过的经验</el-checkbox>
        <el-checkbox value="不敢自用">不敢自用</el-checkbox>
      </el-checkbox-group>
    </el-form-item>

    <el-form-item label="其他问题" prop="otherIssues">
      <el-input
        v-model="formData.UserWork.Questions.Remark"
        type="textarea"
        :rows="3"
        placeholder="请输入其他问题"
        class="custom-input"
      />
    </el-form-item>

    <el-form-item label="纠纷/突发事件" prop="incidents">
      <div class="incident-container">
        <div v-if="!isPreview" class="add-incident">
          <div class="input-group">
            <div class="input-item">
              <div class="input-row">
                <div class="label">发生时间:</div>
                <el-date-picker
                  v-model="newIncidentData.time"
                  type="datetime"
                  placeholder="选择发生时间"
                  format="YYYY.MM.DD HH:mm"
                  value-format="YYYY.MM.DD HH:mm"
                  :default-value="new Date()"
                  class="custom-input"
                />
              </div>
            </div>
          </div>

          <div class="input-item">
            <div class="input-row">
              <div class="label">事件描述:</div>
              <el-input
                v-model="newIncidentData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入事件描述"
                class="custom-input"
              />
            </div>
          </div>

          <el-button
            type="primary"
            class="submit-btn"
            :loading="confirmLoading"
            :disabled="!newIncidentData.time || !newIncidentData.description.trim()"
            @click="handleAddIncident"
          >
            添加事件记录
          </el-button>
        </div>

        <div class="incident-list">
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in displayedRecords"
              :key="index"
              :timestamp="record.EventTime"
              placement="top"
            >
              <div class="incident-item">
                <div class="incident-header">
                  <div v-if="!isPreview" class="incident-actions">
                    <el-button type="primary" link @click="handleEdit(index)">编辑</el-button>
                    <el-popconfirm title="确认删除这条事件记录吗？" @confirm="handleDelete(index)">
                      <template #reference>
                        <el-button type="danger" link>删除</el-button>
                      </template>
                    </el-popconfirm>
                  </div>
                </div>
                <div v-if="editingRecord.Id !== record.Id" class="incident-content">
                  {{ record.Remark }}
                </div>
                <div v-else class="edit-form">
                  <div class="input-group">
                    <div class="input-item">
                      <div class="input-row">
                        <div class="label">发生时间:</div>
                        <el-date-picker
                          v-model="editingRecord.EventTime"
                          type="datetime"
                          placeholder="选择发生时间"
                          format="YYYY.MM.DD HH:mm"
                          value-format="YYYY.MM.DD HH:mm"
                          class="custom-input"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="input-item">
                    <div class="input-row">
                      <div class="label">事件描述:</div>
                      <el-input
                        v-model="editingRecord.Remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入事件描述"
                        class="custom-input"
                      />
                    </div>
                  </div>
                  <div class="edit-actions">
                    <el-button @click="handleEditCancel">取消</el-button>
                    <el-button
                      type="primary"
                      :disabled="!editingRecord.EventTime || !editingRecord.Remark.trim()"
                      :loading="confirmLoading"
                      @click="handleEditConfirm"
                    >
                      确认
                    </el-button>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-form-item>
  </el-form>

  <div v-if="incidentRecords.length > defaultDisplayCount" class="expand-section">
    <div class="expand-divider">
      <span class="divider-line" />
    </div>
    <el-button class="expand-button" type="primary" link @click="toggleExpand">
      {{ isExpanded ? "收起记录" : "查看更多记录" }}
      <el-icon class="expand-icon" :class="{ 'is-expanded': isExpanded }">
        <ArrowDown />
      </el-icon>
    </el-button>
  </div>

  <el-form :model="formData" label-position="right" :disabled="isPreview">
    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="formData.AssistantFollowRecords[defaultAssistantIndex].Remark"
        type="textarea"
        :rows="3"
        placeholder="请输入备注"
        class="custom-input"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { dayjs, FormInstance } from "element-plus";
import { ref, inject, Ref, computed } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import Passport_Api from "@/api/passport";
import { UserEventItem } from "@/api/passport/types";
import UserSelect from "@/components/UserSelect/index.vue";

export interface PageCurrentData {
  AssistantFollowRecords: {
    AssistantId: string;
    AssistantName: string;
    Category: number;
    Remark: string;
  }[];
  DiagnoseNames: {
    DiagnoseName: string;
    Count: number;
  }[];
  MoItemNames: {
    MoItemName: string;
    Count: number;
  }[];
  UserWork: {
    Questions: string;
  };
}
interface PageCurrentDataExtend extends Omit<PageCurrentData, "UserWork"> {
  UserWork: {
    Questions: {
      Tag: string[];
      Remark: string;
    };
  };
}
const doctorId = inject<Ref<string>>("doctorId", ref(""));
const formRef = ref<FormInstance>();
const formData = ref<PageCurrentDataExtend>({
  AssistantFollowRecords: [
    {
      AssistantId: "",
      AssistantName: "",
      Category: 0,
      Remark: "",
    },
  ],
  DiagnoseNames: [],
  MoItemNames: [],
  UserWork: {
    Questions: {
      Tag: [],
      Remark: "",
    },
  },
});

// 事件记录数据
const incidentRecords = ref<UserEventItem[]>([]);
const confirmLoading = ref<boolean>(false);

const newIncidentData = ref({
  time: "",
  description: "",
});

const editingRecord = ref<UserEventItem>({
  Id: "",
  UserId: "",
  Remark: "",
  EventTime: "",
  DirectorId: "",
  DirectorName: "",
  Type: 1,
});

const isPreview = inject<Ref<boolean>>("isPreview", ref(false));
const creatorSelectRef = ref<InstanceType<typeof UserSelect> | null>(null);

const defaultDisplayCount = 1;
const defaultAssistantIndex = ref<number>(0);
const isExpanded = ref(false);

const displayedRecords = computed(() => {
  if (isExpanded.value || incidentRecords.value.length <= defaultDisplayCount) {
    return incidentRecords.value;
  }
  return incidentRecords.value.slice(0, defaultDisplayCount);
});

const onChangeAssistant = (value: string) => {
  const userOptions = creatorSelectRef.value?.userOptions;
  const selectUser = userOptions?.find((item) => item.Id === value);
  formData.value.AssistantFollowRecords[0].AssistantName = selectUser?.Name || "";
};

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const handleAddIncident = async () => {
  if (!newIncidentData.value.time || !newIncidentData.value.description.trim()) return;

  // 添加突发事件
  const res = await Passport_Api.insertUserEvent([
    {
      UserId: doctorId.value,
      Remark: newIncidentData.value.description,
      EventTime: newIncidentData.value.time,
      DirectorId: "",
      DirectorName: "",
      Type: 1,
    },
  ]);
  if (res.Type === 200) {
    handleGetUserEvent();
  }

  // 重置表单
  newIncidentData.value = {
    time: "",
    description: "",
  };
};

const handleEdit = async (index: number) => {
  const record = incidentRecords.value[index];
  await nextTick();
  editingRecord.value = record;
};

const handleEditConfirm = async () => {
  if (!editingRecord.value.Remark.trim()) return;

  const res = await Passport_Api.updateUserEvent([editingRecord.value]);
  if (res.Type === 200) {
    handleGetUserEvent();
  }

  handleEditCancel();
};

const handleEditCancel = () => {
  editingRecord.value = {
    Id: "",
    UserId: "",
    Remark: "",
    EventTime: "",
    DirectorId: "",
    DirectorName: "",
    Type: 0,
  };
};

const handleDelete = async (index: number) => {
  const res = await Passport_Api.deleteUserEvent({ Ids: [incidentRecords.value[index].Id!] });
  if (res.Type === 200) {
    incidentRecords.value.splice(index, 1);
  }
};

const handleFormSubmit = (): Promise<PageCurrentData | null> => {
  return new Promise<PageCurrentData | null>((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve({
          ...formData.value,
          UserWork: {
            ...formData.value.UserWork,
            Questions: JSON.stringify(formData.value.UserWork.Questions),
          },
        });
      } else {
        resolve(null);
      }
    });
  });
};
const handleGetUserEvent = async () => {
  if (!doctorId.value) return;
  const res = await Passport_Api.getUserEvent({
    UserId: doctorId.value,
    Type: 1,
    PageIndex: 1,
    PageSize: 100,
  });
  if (res.Type === 200) {
    res.Data.Rows.forEach((item) => {
      item.EventTime = dayjs(item.EventTime).format("YYYY-MM-DD HH:mm");
    });
    incidentRecords.value = res.Data.Rows;
  }
};

const handleProcessData = (data: PageCurrentData) => {
  const questions = data.UserWork.Questions
    ? JSON.parse(data.UserWork.Questions)
    : {
        Tag: [],
        Remark: "",
      };
  const obj: PageCurrentDataExtend = {
    ...data,
    UserWork: {
      Questions: questions,
    },
  };
  formData.value = obj;
  if (data.AssistantFollowRecords && data.AssistantFollowRecords.length) {
    defaultAssistantIndex.value = data.AssistantFollowRecords.findIndex((s) => s.Category === 0);
  }
};

interface Props {
  currentInfo: PageCurrentData | null;
}
const props = defineProps<Props>();
watch(
  () => props.currentInfo,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue: PageCurrentData = JSON.parse(JSON.stringify(newVal));
    handleProcessData(copyValue);
  },
  { immediate: true }
);
defineExpose({
  handleFormSubmit,
});
onMounted(() => {
  handleGetUserEvent();
});
</script>

<style lang="scss" scoped>
.el-form {
  max-width: 900px;
  margin: 0 auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.incident-container {
  width: 100%;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

.incident-item {
  background: #fff;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }

  .edit-form {
    margin-top: 12px;

    .input-group {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .input-item {
        flex: 1;
      }
    }

    .input-item {
      .input-row {
        display: flex;
        align-items: flex-start;

        .label {
          min-width: 60px;
          font-size: 13px;
          font-weight: 500;
          color: #606266;
          text-align: right;
          margin-right: 8px;
        }
      }
    }

    .custom-input {
      flex: 1;
      min-width: 0;

      :deep(.el-input__wrapper),
      :deep(.el-textarea__inner) {
        box-shadow: 0 0 0 1px #dcdfe6;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc;
        }

        &:focus-within {
          box-shadow: 0 0 0 1px #409eff;
        }
      }
    }

    .edit-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 12px;
    }
  }
}

.incident-header {
  margin-bottom: 6px;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .incident-actions {
    display: flex;
    gap: 6px;
  }
}

.incident-content {
  color: #606266;
  line-height: 1.4;
  font-size: 13px;
}

.add-incident {
  position: relative;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;

  &::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
    height: 1px;
    background: linear-gradient(90deg, transparent, #dcdfe6, transparent);
  }

  .input-group {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .input-item {
      flex: 1;
    }
  }

  .input-item {
    .input-row {
      display: flex;
      align-items: flex-start;

      .label {
        min-width: 60px;
        font-size: 13px;
        font-weight: 500;
        color: #606266;
        text-align: right;
        margin-right: 8px;
      }
    }
  }

  .custom-input {
    flex: 1;
    min-width: 0;

    :deep(.el-input__wrapper),
    :deep(.el-textarea__inner) {
      box-shadow: 0 0 0 1px #dcdfe6;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc;
      }

      &:focus-within {
        box-shadow: 0 0 0 1px #409eff;
      }
    }
  }

  .submit-btn {
    margin-top: 16px;
    margin-left: 70px;
    width: 100px;
    height: 32px;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;

    &:not(:disabled):hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }
}

.incident-list {
  margin-top: 20px;
}

.expand-section {
  position: relative;
  text-align: center;
  margin: 16px 0;

  .expand-divider {
    position: relative;
    height: 1px;
    margin-bottom: -10px;

    .divider-line {
      position: absolute;
      left: 0;
      right: 0;
      top: 50%;
      height: 1px;
      background: linear-gradient(90deg, transparent, #dcdfe6 20%, #dcdfe6 80%, transparent);
      z-index: 1;
    }
  }

  .expand-button {
    position: relative;
    padding: 6px 20px;
    font-size: 13px;
    font-weight: 500;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 12px;
    transition: all 0.3s ease;
    z-index: 2;

    &:hover {
      color: #409eff;
      border-color: #409eff;
      background: #ecf5ff;
    }

    .expand-icon {
      margin-left: 4px;
      font-size: 12px;
      transition: transform 0.3s ease;

      &.is-expanded {
        transform: rotate(180deg);
      }
    }
  }
}

// 添加列表展开/收起动画
.el-timeline-item {
  transition: all 0.3s ease-in-out;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.el-checkbox) {
  margin-right: 0;
  margin-left: 0;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  :deep(.el-tag) {
    margin: 0;
    font-size: 14px;
    border-radius: 4px;
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
  }
}
</style>
