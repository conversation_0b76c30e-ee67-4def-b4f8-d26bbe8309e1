<template>
  <el-form
    v-if="modelValue"
    ref="ruleForm"
    :model="modelValue"
    :rules="rules"
    :disabled="disabled"
    label-position="right"
    label-width="auto"
  >
    <el-form-item label="机构名称" prop="InviterName">
      <el-input v-model="modelValue.InviterName" type="text" class="w150" />
    </el-form-item>
    <el-form-item label="跳转机构" prop="OrganizationId">
      <el-select v-model="modelValue.OrganizationId" placeholder="请选择" filterable>
        <el-option
          v-for="item in baseOrganizationList"
          :key="item.Id"
          :label="item.Name"
          :value="item.Id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="备注" prop="Remark">
      <el-input v-model="modelValue.Remark" type="textarea" :rows="3" />
    </el-form-item>
    <div class="text-right">
      <el-button @click="emit('cancel')">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-form>
</template>
<script setup lang="ts">
import { type InviterAddOrUpdateInput } from "@/api/identity/types";
import useOrgList from "@/hooks/useOrgList";
import { FormInstance, FormItemRule } from "element-plus";

const modelValue = defineModel<InviterAddOrUpdateInput | null>("value");

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const rules: Record<string, FormItemRule[]> = {
  InviterName: [
    {
      type: "string",
      whitespace: true,
      required: true,
      message: "请输机构名称",
      trigger: "blur",
    },
  ],
  OrganizationId: [
    {
      type: "string",
      required: true,
      message: "请选择跳转机构",
      trigger: "blur",
    },
  ],
};
const ruleForm = useTemplateRef<FormInstance>("ruleForm");
const { baseOrganizationList, loadOrgList } = useOrgList({ hasAll: false });

onMounted(() => {
  loadOrgList();
});

const emit = defineEmits(["cancel", "submit"]);

const submit = () => {
  ruleForm.value?.validate(async (valid) => {
    if (valid) {
      emit("submit", modelValue.value);
    }
  });
};
</script>
