<template>
  <div class="debug">
    测试页面{{ counter }}
    <button @click="handleClick">点击</button>
  </div>
</template>

<script setup lang="ts">
const counter = ref<number>(0);
const obj = ref({});
const a = reactive({
  a: 1,
  b: 2,
  c: 3,
  counter,
  obj,
});
const b = reactive({
  a,
});

function handleClick() {
  counter.value++;
  console.log("counter", counter, "counter.value", counter.value);
  console.log("obj", obj, "obj.value", obj.value);
  console.log("a", a, "a.counter", a.counter, "a.obj", a.obj);
  console.log("b", b, "b.a", b.a);
}
</script>
