<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否启用">
                <el-select
                  v-model="queryParams.IsEnabled"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Key">
                <el-input
                  v-model="queryParams.Key"
                  placeholder="名称/编码/拼音码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handlePreviewOrEdit(null, true)">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Key" label="名称" align="center" />
          <el-table-column prop="Value" label="编码" align="center" />
          <el-table-column prop="PinyinCode" label="拼音码" align="center" />
          <el-table-column prop="CreatedTime" label="创建时间" width="150" align="center" />
          <el-table-column prop="enable" label="是否启用" width="80" align="center">
            <template #default="scope">
              <span>{{ scope.row.IsEnabled === false ? "否" : "是" }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, false)">
                编辑
              </el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="primary"
                @click="handlePublish(scope.row)"
              >
                发布
              </el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="primary"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="dialog.show"
      :title="dialog.title"
      width="800"
      destroy-on-close
      :close-on-click-modal="isPreview"
      :close-on-press-escape="isPreview"
      @close="dialog.show = false"
    >
      <RehabilitationForm
        ref="rehabilitationFormRef"
        :rehabilitation-detail="dialog.rehabilitationDetail"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog.show = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import Dictionary_Api from "@/api/dictionary";
import { EpPropMergeTypeWithNull } from "element-plus";
interface PageQuery {
  IsEnabled: EpPropMergeTypeWithNull<boolean>;
  Key: string;
  PageIndex: number;
  PageSize: number;
}

interface DialogParams {
  show: boolean;
  title: string;
  rehabilitationDetail: ReadDict | null;
}

const dialogConfirmLoading = ref<boolean>(false);
const rehabilitationFormRef = useTemplateRef("rehabilitationFormRef");

defineOptions({
  name: "RehabilitationClassificationManagement",
});

const queryParams = ref<PageQuery>({
  IsEnabled: null,
  Key: "",
  PageIndex: 1,
  PageSize: 10,
});

const dialog = ref<DialogParams>({
  show: false,
  title: "",
  rehabilitationDetail: null,
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<ReadDict>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: ReadDict | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  dialog.value.title = row
    ? isPreviewState
      ? "查看" + row.Key
      : "编辑" + row.Key
    : "添加康复分类";
  if (row && row.Id) {
    const res = await Dictionary_Api.readDict({
      PageCondition: {
        PageIndex: 1,
        PageSize: 1,
        SortConditions: [{ SortField: "CustomSort", ListSortDirection: 1 }],
      },
      FilterGroup: {
        Rules: [
          { Field: "DictId", Value: "39", Operate: 3 },
          { Field: "Id", Value: row.Id, Operate: 3 },
        ],
        Groups: [],
        Operate: 1,
      },
    });
    if (res.Type === 200) {
      dialog.value.rehabilitationDetail = res.Data.Rows[0];
      dialog.value.show = true;
      return;
    }
  }
  dialog.value.rehabilitationDetail = null;
  dialog.value.show = true;
};
const handleGetTableList = async () => {
  const params = handleGetParams();
  tableLoading.value = true;
  const res = await Dictionary_Api.readDict(params);
  if (res.Type === 200) {
    res.Data.Rows.forEach((item: any) => {
      item.CreatedTime = dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
    });
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};
const handleGetParams = (id?: string): DictQueryParams => {
  const params = {
    PageCondition: {
      PageIndex: queryParams.value.PageIndex,
      PageSize: queryParams.value.PageSize,
      SortConditions: [
        { SortField: "CustomSort", ListSortDirection: 1 },
        { SortField: "CreatedTime", ListSortDirection: 1 },
      ],
    },
    FilterGroup: {
      Rules: [
        { Field: "DictId", Value: "39", Operate: 3 },
        { Field: "IsEnabled", Value: queryParams.value.IsEnabled, Operate: 3 },
      ],
      Groups: [
        {
          Rules: [
            { Field: "Key", Value: queryParams.value.Key, Operate: 11 },
            { Field: "Value", Value: queryParams.value.Key, Operate: 11 },
            { Field: "PinyinCode", Value: queryParams.value.Key, Operate: 11 },
          ],
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  if (id) {
    params.FilterGroup.Rules.push({ Field: "Id", Value: id, Operate: 3 });
  }
  return params;
};
const handleSubmit = async () => {
  const params = await rehabilitationFormRef.value?.handleGetSubmitParams();
  console.log("params", params);
  if (!params) return;
  let fun = params.Id ? Dictionary_Api.updateDict : Dictionary_Api.createDict;
  dialogConfirmLoading.value = true;
  const res = await fun([params]);
  if (res.Type === 200) {
    ElNotification({
      title: "成功",
      message: params.Id ? "编辑成功" : "添加成功",
      type: "success",
    });
    dialog.value.show = false;
    handleGetTableList();
  }
  dialogConfirmLoading.value = false;
};
const handlePublish = async (row: ReadDict) => {
  ElMessageBox.confirm("确定发布吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Dictionary_Api.publishDict({ id: row.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "发布成功",
        type: "success",
      });
      handleGetTableList();
    }
  });
};
const handleDelete = async (row: ReadDict) => {
  ElMessageBox.confirm("确定删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Dictionary_Api.deleteDict({ id: row.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "删除成功",
        type: "success",
      });
      handleGetTableList();
    }
  });
};
onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
