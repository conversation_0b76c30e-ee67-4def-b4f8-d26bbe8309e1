<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-position="right"
    label-width="80px"
    :inline="true"
    :disabled="isPreview"
  >
    <el-form-item label="名称" prop="Key" maxlength="50">
      <el-input
        v-model="form.Key"
        :disabled="isPreview || (!!form.Id && form.IsPublish)"
        @blur="renderPinyin"
      />
    </el-form-item>
    <el-form-item label="拼音码" prop="PinyinCode">
      <el-input v-model="form.PinyinCode" :disabled="isPreview || (!!form.Id && form.IsPublish)" />
    </el-form-item>
    <el-form-item label="编码" prop="Value">
      <el-input v-model="form.Value" :disabled="isPreview || (!!form.Id && form.IsPublish)" />
    </el-form-item>
    <el-form-item label="备注" prop="Remark">
      <el-input
        v-model="form.Remark"
        style="width: 200px"
        type="textarea"
        :disabled="isPreview || (!!form.Id && form.IsPublish)"
      />
    </el-form-item>
    <el-form-item label="是否启用">
      <el-switch v-model="form.IsEnabled" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { DictCreateUpdateInputDTO } from "@/api/dictionary/types";
import { chineseToPinyin } from "@/utils";
import { FormInstance, FormRules } from "element-plus";
const formRef = ref<FormInstance>();

const form = ref<DictCreateUpdateInputDTO>({
  Key: "",
  PinyinCode: "",
  Value: "",
  Remark: "",
  IsEnabled: true,
  IsPublish: false,
  DictId: 5,
  CustomSort: null,
});

const rules = reactive<FormRules>({
  Key: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  PinyinCode: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  Value: [{ required: true, message: "请输入编码", trigger: "blur" }],
});
const handleSubmit = async (): Promise<DictCreateUpdateInputDTO | null> => {
  try {
    await formRef.value!.validate();
    const copyData: DictCreateUpdateInputDTO = JSON.parse(JSON.stringify(form.value));
    if (!copyData.CustomSort) copyData.CustomSort = 0;
    return copyData;
  } catch {
    return null;
  }
};

const renderPinyin = () => {
  form.value.PinyinCode = chineseToPinyin(form.value.Key!);
};

const isPreview = inject("isPreview") as Ref<boolean>;

const handleProcessingData = (info: ReadDict) => {
  form.value.Key = info.Key!;
  form.value.PinyinCode = info.PinyinCode!;
  form.value.Value = info.Value!;
  form.value.Remark = info.Remark!;
  form.value.IsEnabled = info.IsEnabled!;
  form.value.IsPublish = info.IsPublish;
  form.value.CustomSort = info.CustomSort;
  form.value.Id = info.Id!;
};

interface Props {
  item: ReadDict | null;
}
const props = defineProps<Props>();

watch(
  () => props.item,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
