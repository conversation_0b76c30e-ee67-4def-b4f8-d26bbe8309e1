<template>
  <el-container class="w-full h-full">
    <el-aside width="230px" class="p-10px">
      <el-tree
        class="w-full h-full p-10px overflow-auto"
        :data="leftTreeData"
        :loading="treeLoading"
        :props="defaultProps"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeRightClick"
      />
      <div v-show="leftMenuShow.isShow">
        <ul
          id="menu"
          class="menu"
          :style="'top:' + menuPosition.clientY + 'px;left:' + menuPosition.clientX + 'px;'"
        >
          <li
            v-if="leftMenuShow.showAdd"
            class="menu_item"
            @click="showDysfunctionTypeDialog(true)"
          >
            添加
          </li>
          <li
            v-if="leftMenuShow.showEdit"
            class="menu_item"
            @click="showDysfunctionTypeDialog(false)"
          >
            编辑
          </li>
          <li v-if="leftMenuShow.showDelete" class="menu_item" @click="handleDeleteDysfunctionType">
            删除
          </li>
        </ul>
      </div>
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <template #search>
          <TBSearchContainer>
            <template #left>
              <el-form :model="queryParams" label-position="right" :inline="true">
                <el-form-item label="是否启用">
                  <el-select
                    v-model="queryParams.IsEnabled"
                    placeholder="请选择"
                    clearable
                    :empty-values="[null, undefined, '']"
                    :value-on-clear="() => null"
                    style="width: 80px"
                  >
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </el-form-item>
                <el-form-item label="关键字" prop="Key">
                  <el-input
                    v-model="queryParams.Key"
                    placeholder="名称/编码/拼音码/备注"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
              <el-button type="primary" @click="handlePreviewOrEdit(null, true)">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <template #table>
          <el-table
            ref="tableRef"
            v-loading="tableLoading"
            :data="pageData"
            :total="total"
            border
            row-key="Id"
            :height="tableFluidHeight"
            highlight-current-row
            style="text-align: center; flex: 1"
          >
            <el-table-column prop="Key" label="名称" show-overflow-tooltip align="center" />
            <el-table-column prop="Value" label="编码" align="center" />
            <el-table-column prop="ParentId" label="分类" align="center">
              <template #default="scope">
                {{ handleGetClassificationData(scope.row.ParentId) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="Remark"
              label="备注"
              width="120"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column label="是否启用" width="100" align="center">
              <template #default="scope">
                {{ scope.row.IsEnabled ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="180" align="center">
              <template #default="scope">
                <el-button
                  link
                  size="small"
                  type="primary"
                  @click="handlePreviewOrEdit(scope.row, true)"
                >
                  查看
                </el-button>
                <el-button
                  link
                  size="small"
                  type="primary"
                  @click="handlePreviewOrEdit(scope.row, false)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="!scope.row.IsPublish"
                  link
                  type="primary"
                  @click="handlePublish(scope.row)"
                >
                  发布
                </el-button>
                <el-button
                  v-if="!scope.row.IsPublish"
                  link
                  size="small"
                  type="primary"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.PageIndex"
            v-model:limit="queryParams.PageSize"
            @pagination="handleGetTableList"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
    <el-dialog
      v-model="showDialog.diseaseType"
      :title="diseaseTypeTitle"
      width="400"
      destroy-on-close
    >
      <DiseaseTypeContent ref="diseaseTypeContentRef" :type-data="rightClickType" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.diseaseType = false">取消</el-button>
          <el-button
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleDiseaseTypeSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="showDialog.disease" :title="diseaseTitle" width="800" destroy-on-close>
      <DiseaseContent
        ref="diseaseContentRef"
        :disease-data="diseaseData"
        :parent-id="parentId"
        :is-disabled="isPreview"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.disease = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleDiseaseSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-container>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import Dictionary_Api from "@/api/dictionary";
import { useTableConfig } from "@/hooks/useTableConfig";
import { getDiseaseTypeList } from "@/utils/dict";
import { EpPropMergeTypeWithNull } from "element-plus";
import DiseaseTypeContent from "./components/DiseaseTypeContent.vue";
import DiseaseContent from "./components/DiseaseContent.vue";
import { DysfunctionTypeInputDTO } from "@/api/content/types";

interface PageQuery {
  IsEnabled: EpPropMergeTypeWithNull<boolean>;
  Key: string;
  PageIndex: number;
  PageSize: number;
  ParentId?: EpPropMergeTypeWithNull<string>;
}
interface PageDialogShow {
  disease: boolean;
  diseaseType: boolean;
}
interface PageDiseaseType extends ReadDict {
  Children?: PageDiseaseType[];
}
let leftTreeData = ref<PageDiseaseType[]>([]);
const treeLoading = ref<boolean>(false);
const leftMenuShow = ref({
  isShow: false,
  showAdd: false,
  showEdit: false,
  showDelete: false,
});
const menuPosition = ref({
  clientX: 0,
  clientY: 0,
});
const defaultProps = {
  children: "Children",
  label: "Key",
};
const showDialog = ref<PageDialogShow>({
  disease: false,
  diseaseType: false,
});
const diseaseTypeContentRef = ref<InstanceType<typeof DiseaseTypeContent> | null>(null);
const diseaseContentRef = ref<InstanceType<typeof DiseaseContent> | null>(null);

const diseaseData = ref<ReadDict | null>(null);

defineOptions({
  name: "DysfunctionManagement",
});

const queryParams = ref<PageQuery>({
  IsEnabled: null,
  Key: "",
  PageIndex: 1,
  PageSize: 10,
  ParentId: null,
});
const allTreeListData = ref<ReadDict[]>([]);
let diseaseTypeTitle: string = "";
let diseaseTitle: string = "";
let leftTypeClickId: string = "";
let leftTypeClickPartId: string = "";
const parentId = ref<string | undefined>(undefined);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);
let rightClickType = reactive<DysfunctionTypeInputDTO>({
  Key: "",
  PinyinCode: "",
  Value: "",
  DictId: "",
  IsEnabled: true,
  Remark: "",
});
const dialogConfirmLoading = ref<boolean>(false);

const superiorDysfunctionTypeList = ref<ReadDict[]>([]);
provide("superiorDysfunctionTypeList", superiorDysfunctionTypeList);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<ReadDict>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: ReadDict | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  diseaseTitle = row ? (isPreviewState ? "查看" + row.Key : "编辑" + row.Key) : "添加功能障碍";
  if (!row && (!leftTypeClickId || !leftTypeClickPartId)) {
    ElMessageBox.alert("请先选择功能障碍二级分类", "提示", {
      confirmButtonText: "确定",
    });
    return;
  }
  diseaseData.value = row;
  superiorDysfunctionTypeList.value = allTreeListData.value.filter((s) => s.ParentId && s.Id);
  if (row) {
    parentId.value = row!.ParentId;
  }
  showDialog.value.disease = true;
};
const handleNodeClick = (event: MouseEvent, data: { data: { Id: string; ParentId: string } }) => {
  leftTypeClickId = data.data.Id;
  leftTypeClickPartId = data.data.ParentId;
  queryParams.value.ParentId = data.data.Id;
  parentId.value = data.data.Id;
  handleGetTableList();
};
const handleNodeRightClick = (event: MouseEvent, data: PageDiseaseType) => {
  const copyData: PageDiseaseType = JSON.parse(JSON.stringify(data));
  menuPosition.value.clientX = event.clientX + 10;
  var clientHeight = document.body.clientHeight;
  if (clientHeight - event.clientY <= 110) {
    menuPosition.value.clientY = clientHeight - 110;
  } else {
    menuPosition.value.clientY = event.clientY;
  }
  if (copyData.Id) {
    if (copyData.ParentId) {
      leftMenuShow.value.isShow = true;
      leftMenuShow.value.showEdit = true;
      leftMenuShow.value.showAdd = false;
      leftMenuShow.value.showDelete = true;
    } else {
      leftMenuShow.value.isShow = true;
      leftMenuShow.value.showEdit = true;
      leftMenuShow.value.showAdd = true;
      leftMenuShow.value.showDelete = true;
    }
  } else {
    leftMenuShow.value.isShow = true;
    leftMenuShow.value.showAdd = true;
    leftMenuShow.value.showEdit = false;
    leftMenuShow.value.showDelete = false;
    copyData.Key = "";
  }
  delete copyData.Children;
  rightClickType = {
    Key: copyData.Key!,
    PinyinCode: copyData.PinyinCode!,
    Value: copyData.Value!,
    DictId: copyData.DictId ? String(copyData.DictId!) : "7",
    IsEnabled: copyData.IsEnabled!,
    Remark: copyData.Remark!,
    Id: copyData.Id!,
    ParentId: copyData.ParentId || "",
  };
  document.addEventListener("click", handleHideMenu);
};

// 新增辅助函数，用于初始化 superiorDysfunctionTypeList 和 ParentId
function initializeSuperiorListAndParentId(
  isAdd: boolean,
  currentRightClickType: DysfunctionTypeInputDTO,
  currentAllTreeListData: ReadDict[]
): { superiorList: ReadDict[]; parentId: string } {
  let superiorList: ReadDict[] = [
    {
      Key: "全部",
      Id: "",
    },
  ];
  let parentIdValue = currentRightClickType.ParentId || "";

  // 判断是第几级
  // level 1: currentRightClickType.Id 存在且 currentRightClickType.ParentId 不存在
  if (currentRightClickType.Id && !currentRightClickType.ParentId) {
    if (isAdd) {
      superiorList = currentAllTreeListData.filter((s) => !s.ParentId && s.Id);
      parentIdValue = currentRightClickType.Id!; //  确保 Id 存在
    } else {
      // 编辑状态下，保持 "全部" 选项，ParentId 设为空
      parentIdValue = "";
    }
  } else if (currentRightClickType.Id && currentRightClickType.ParentId) {
    // level 2: currentRightClickType.Id 和 currentRightClickType.ParentId 都存在
    superiorList = currentAllTreeListData.filter((s) => !s.ParentId && s.Id);
    // parentIdValue 保持 currentRightClickType.ParentId，在函数开始时已默认设置
  }
  // 对于其他情况 (例如，currentRightClickType.Id 不存在，即顶级添加)，
  // superiorList 保持 "全部", parentIdValue 保持 currentRightClickType.ParentId (通常为空字符串)

  return { superiorList, parentId: parentIdValue };
}

const showDysfunctionTypeDialog = (isAdd: boolean = false) => {
  diseaseTypeTitle = `${isAdd ? "添加" : "编辑"}${rightClickType.Key || ""}功能障碍类型`;

  const { superiorList, parentId: newParentId } = initializeSuperiorListAndParentId(
    isAdd,
    rightClickType, // 传递当前的 rightClickType
    allTreeListData.value
  );
  superiorDysfunctionTypeList.value = superiorList;

  if (isAdd) {
    // 当添加时，重置 rightClickType 的相关字段，并使用新的 ParentId
    rightClickType = {
      Key: "",
      PinyinCode: "",
      Value: "",
      DictId: "7", // 保持默认值
      IsEnabled: true, // 保持默认值
      Remark: "",
      ParentId: newParentId, // 使用从辅助函数获取的 parentId
      // Id 应在添加时清除，或者不包含在这个对象中，因为它会在创建时生成
      // 如果 DysfunctionTypeInputDTO 允许 Id 为可选或 null，可以设为 null 或 undefined
      Id: undefined, // 或者根据 DysfunctionTypeInputDTO 的定义处理
    };
  } else {
    // 编辑模式下，只更新 ParentId，其他字段保持不变
    rightClickType.ParentId = newParentId;
  }
  showDialog.value.diseaseType = true;
};
const handleDeleteDysfunctionType = async () => {
  if (!rightClickType.Id) return;
  const r = await Dictionary_Api.haveSonData(rightClickType.Id);
  if (r.Type !== 200) {
    ElNotification({
      title: "失败",
      message: r.Content,
      type: "error",
    });
    return;
  }
  if (r.Data) {
    ElMessageBox.confirm("该条数据有子级数据，无法删除", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    return;
  }
  ElMessageBox.confirm("确定删除该条数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Dictionary_Api.deleteDict1({ id: rightClickType.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "删除成功",
        type: "success",
      });
      handleGetLeftTreeData();
      handleGetTableList();
    } else {
      ElNotification({
        title: "失败",
        message: res.Content,
        type: "error",
      });
    }
  });
};
const handleHideMenu = () => {
  leftMenuShow.value.isShow = false;
  document.removeEventListener("click", handleHideMenu);
};

const handleGetTableList = async () => {
  console.log("handleGetTableList");
  const params = handleGetParams();
  tableLoading.value = true;
  const res = await Dictionary_Api.readDict(params);
  if (res.Type === 200) {
    res.Data.Rows.forEach((item: any) => {
      item.CreatedTime = dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
    });
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleGetParams = (id?: string): DictQueryParams => {
  const params = {
    PageCondition: {
      PageIndex: queryParams.value.PageIndex,
      PageSize: queryParams.value.PageSize,
      SortConditions: [{ SortField: "Key", ListSortDirection: 1 }],
    },
    FilterGroup: {
      Rules: [
        { Field: "DictId", Value: "18", Operate: 3 },
        { Field: "IsEnabled", Value: queryParams.value.IsEnabled, Operate: 3 },
      ],
      Groups: [
        {
          Rules: [
            { Field: "Key", Value: queryParams.value.Key, Operate: 11 },
            { Field: "Value", Value: queryParams.value.Key, Operate: 11 },
            { Field: "PinyinCode", Value: queryParams.value.Key, Operate: 11 },
            { Field: "Remark", Value: queryParams.value.Key, Operate: 11 },
          ],
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  if (queryParams.value.ParentId) {
    params.FilterGroup.Rules.push({
      Field: "ParentId",
      Value: queryParams.value.ParentId,
      Operate: 3,
    });
  }
  return params;
};
const handleGetLeftTreeData = async () => {
  const list = await getDiseaseTypeList({
    PageSize: 1000,
    Key: "",
    IsEnabled: null,
    IsPublish: null,
  });
  const treeData: PageDiseaseType[] = [
    {
      Key: "全部",
      Id: "",
      Children: [],
    },
  ];
  list.forEach((item) => {
    if (item.ParentId == null || item.ParentId === "") {
      var Children: PageDiseaseType[] = [];
      list.forEach((item1) => {
        if (item1.ParentId === item.Id) {
          if (item.Children === undefined) {
            Children.push(item1);
          }
        }
      });
      item.Children = Children;
      treeData[0].Children?.push(item);
    }
  });
  leftTreeData.value = treeData;
  allTreeListData.value = list;
};

const handleGetClassificationData = (id: string) => {
  if (!allTreeListData.value.length) return;
  var result = "";
  allTreeListData.value.forEach((item) => {
    if (id === item.Id) {
      result = item.Key!;
    }
  });
  return result;
};

const handleDiseaseTypeSubmit = async () => {
  const params = await diseaseTypeContentRef.value?.submitForm();
  if (!params) return;
  console.log("params", params);
  const fun = params.Id ? Dictionary_Api.updateDict : Dictionary_Api.createDict;
  dialogConfirmLoading.value = true;
  if (!params.Id) {
    delete params.Id;
  }
  fun([{ ...params, DictId: Number(params.DictId) }])
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success("操作成功");
        showDialog.value.diseaseType = false;
        handleGetLeftTreeData();
      } else {
        ElMessage.error(res.Content);
      }
    })
    .catch(() => {
      ElMessage.error("操作失败");
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handleDiseaseSubmit = async () => {
  const params = await diseaseContentRef.value?.handleGetSubmitParams();
  if (!params) return;
  const fun = params.Id ? Dictionary_Api.updateDict : Dictionary_Api.createDict;
  dialogConfirmLoading.value = true;
  if (!params.Id) {
    delete params.Id;
  }
  fun([params])
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success("操作成功");
        showDialog.value.disease = false;
        handleGetTableList();
      } else {
        ElMessage.error(res.Content);
      }
    })
    .catch(() => {
      ElMessage.error("操作失败");
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handlePublish = async (row: ReadDict) => {
  ElMessageBox.confirm("确定发布吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Dictionary_Api.publishDict({ id: row.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "发布成功",
        type: "success",
      });
      handleGetTableList();
    }
  });
};
const handleDelete = async (row: ReadDict) => {
  ElMessageBox.confirm("确定删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Dictionary_Api.deleteDict({ id: row.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "删除成功",
        type: "success",
      });
      handleGetTableList();
    }
  });
};

onMounted(() => {
  handleGetLeftTreeData();
  handleGetTableList();
});
onActivated(() => {});
</script>
<style scoped lang="scss"></style>
