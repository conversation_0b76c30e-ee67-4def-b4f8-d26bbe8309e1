<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      :disabled="props.disabled"
    >
      <div class="flex">
        <el-form-item class="w-1/3" label="类别" prop="Class">
          <el-select
            v-model="formData.Class"
            placeholder="请选择类别"
            clearable
            :value-on-clear="() => undefined"
            :disabled="classDisabled"
          >
            <el-option
              v-for="item in classList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用" label-width="100px">
          <el-switch v-model="formData.Enable" />
        </el-form-item>
        <el-form-item label="排序" prop="Order">
          <el-input-number v-model="formData.Order" placeholder="排序" />
        </el-form-item>
      </div>
      <el-form-item label="内容" prop="Text">
        <el-input
          v-model="formData.Text"
          maxlength="200"
          type="textarea"
          placeholder="请输入内容"
          :autosize="{ minRows: 4, maxRows: 6 }"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm(ruleFormRef)">
      确定
    </el-button>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from "element-plus";
import { classList } from "../constant";

const emit = defineEmits(["cancel", "submit"]);
const props = defineProps<{
  phrase: Phrase;
  disabled: boolean;
}>();

const ruleFormRef = ref<FormInstance>();
const formLoading = ref<boolean>(false);
const formData = reactive<Phrase>({});
const classDisabled = ref<boolean>(false);
const rules = reactive<FormRules<Phrase>>({
  Class: [{ required: true, message: "请选择类别", trigger: "change" }],
  Text: [{ required: true, message: "请输入内容", trigger: "blur" }],
});

function onSubmitForm(ruleFormRef: FormInstance | undefined) {
  if (!ruleFormRef) return;

  ruleFormRef.validate((valid) => {
    if (valid) {
      emit("submit", formData);
    } else {
      ElMessage.error("请检查输入内容");
    }
  });
}

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.phrase));
  classDisabled.value = data.Id === undefined && data.Class !== undefined;
  Object.assign(formData, data);
});
</script>

<style lang="scss" scoped></style>
