<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="80px"
    class="consumable-type-form"
    scroll-to-error
  >
    <el-form-item label="名称" prop="Key">
      <el-input v-model="formData.Key" placeholder="请输入名称" @blur="handleKeyBlur" />
    </el-form-item>
    <el-form-item label="拼音码" prop="PinyinCode">
      <el-input v-model="formData.PinyinCode" placeholder="请输入拼音码" />
    </el-form-item>
    <el-form-item label="编码" prop="Value">
      <el-input v-model="formData.Value" placeholder="请输入编码" />
    </el-form-item>
    <el-form-item label="上级分类" prop="ParentId">
      <el-select
        v-model="formData.ParentId"
        placeholder="请选择上级分类"
        :empty-values="[null, undefined]"
      >
        <el-option
          v-for="item in superiorDysfunctionTypeList"
          :key="item.Id"
          :label="item.Key"
          :value="item.Id!"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="是否启用" prop="IsEnabled">
      <el-switch v-model="formData.IsEnabled" />
    </el-form-item>
    <el-form-item label="备注" prop="Remark">
      <el-input v-model="formData.Remark" type="textarea" :rows="3" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { DysfunctionTypeInputDTO } from "@/api/content/types";
import { chineseToPinyin } from "@/utils";
import { FormInstance, FormRules } from "element-plus";

const props = defineProps<{
  typeData: DysfunctionTypeInputDTO;
}>();
const superiorDysfunctionTypeList = inject("superiorDysfunctionTypeList") as Ref<ReadDict[]>;

const formRef = ref<FormInstance>();

const formData = reactive<DysfunctionTypeInputDTO>(props.typeData);

const rules = reactive<FormRules>({
  Key: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  PinyinCode: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  Value: [{ required: true, message: "请输入编码", trigger: "blur" }],
});

const handleKeyBlur = () => {
  formData.PinyinCode = chineseToPinyin(formData.Key);
};
const submitForm = async (): Promise<DysfunctionTypeInputDTO | null> => {
  if (!formRef.value) return null;
  return new Promise((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve(formData);
      } else {
        resolve(null);
      }
    });
  });
};
defineExpose({
  submitForm,
});
</script>

<style lang="scss" scoped></style>
