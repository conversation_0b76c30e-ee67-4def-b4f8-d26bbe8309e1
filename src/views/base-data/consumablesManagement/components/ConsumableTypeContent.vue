<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="80px"
    class="consumable-type-form"
    scroll-to-error
  >
    <el-form-item label="名称" prop="Name">
      <el-input v-model="formData.Name" placeholder="请输入名称" @blur="handleNameBlur" />
    </el-form-item>
    <el-form-item label="拼音码" prop="PYM">
      <el-input v-model="formData.PYM" placeholder="请输入拼音码" />
    </el-form-item>
    <el-form-item label="编码" prop="Code">
      <el-input v-model="formData.Code" placeholder="请输入编码" />
    </el-form-item>
    <el-form-item label="是否启用" prop="IsEnable">
      <el-switch v-model="formData.IsEnable" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ConsumableTypeInputDTO } from "@/api/content/types";
import { chineseToPinyin } from "@/utils";
import type { FormInstance, FormRules } from "element-plus";

const props = defineProps<{
  typeData: ConsumableTypeInputDTO;
}>();

const formRef = ref<FormInstance>();

const formData = reactive<ConsumableTypeInputDTO>(props.typeData);

const rules = reactive<FormRules>({
  Name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  PYM: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
});

const submitForm = async (): Promise<ConsumableTypeInputDTO | null> => {
  if (!formRef.value) return null;
  return new Promise((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve(formData);
      } else {
        resolve(null);
      }
    });
  });
};

const handleNameBlur = () => {
  formData.PYM = chineseToPinyin(formData.Name);
};

defineExpose({
  submitForm,
});
</script>

<style scoped lang="scss">
.consumable-type-form {
  max-width: 600px;
  margin: 20px;
}
</style>
