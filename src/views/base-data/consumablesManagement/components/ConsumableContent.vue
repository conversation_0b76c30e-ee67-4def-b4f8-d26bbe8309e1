<template>
  <div class="consumable-content">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      class="consumable-form"
      :disabled="props.isDisabled"
    >
      <el-row :gutter="2">
        <el-col :span="8">
          <el-form-item label="分类" prop="Type">
            <el-select v-model="formData.Type" placeholder="请选择分类">
              <el-option
                v-for="item in typeOptions"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="名称" prop="Name">
            <el-input v-model="formData.Name" placeholder="请输入名称" @blur="handleNameBlur" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="拼音码" prop="PYM">
            <el-input v-model="formData.PYM" placeholder="请输入拼音码" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编码" prop="Code">
            <el-input v-model="formData.Code" placeholder="请输入编码" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="规格" prop="Spec">
            <el-input v-model="formData.Spec" placeholder="请输入规格" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="包装单位" prop="PackUnit">
            <el-input v-model="formData.PackUnit" placeholder="请输入包装单位" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="包装比例" prop="PackRatio">
            <el-input-number
              v-model="formData.PackRatio"
              :min="0"
              :precision="1"
              placeholder="请输入包装比例"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否启用" prop="IsEnable">
            <el-switch v-model="formData.IsEnable" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="图片" prop="Urls">
            <MultiImageUpload v-model="formData.Urls!" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ConsumableInputDTO } from "@/api/content/types";
import MultiImageUpload from "@/components/Upload/MultiImageUpload.vue";
import { chineseToPinyin } from "@/utils";

const props = defineProps<{
  classification: ConsumablesType[];
  consumablesData: ConsumableInputDTO;
  isDisabled: boolean;
}>();
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<ConsumableInputDTO>(props.consumablesData);

// 分类选项，需要根据实际业务补充
const typeOptions = ref<ConsumablesType[]>(props.classification);

// 表单验证规则
const rules = reactive<FormRules>({
  Type: [{ required: true, message: "请选择分类", trigger: "change" }],
  Name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { max: 25, message: "名称不能超过25个字符", trigger: "blur" },
  ],
  PYM: [
    { required: true, message: "请输入拼音码", trigger: "blur" },
    { max: 25, message: "拼音码不能超过25个字符", trigger: "blur" },
  ],
  Code: [
    { required: true, message: "请输入编码", trigger: "blur" },
    { max: 25, message: "编码不能超过25个字符", trigger: "blur" },
  ],
  PackUnit: [{ required: true, message: "请输入包装单位", trigger: "blur" }],
  PackRatio: [{ required: true, message: "请输入包装比例", trigger: "change" }],
});

const handleNameBlur = () => {
  if (!formData.Name) return;
  formData.PYM = chineseToPinyin(formData.Name);
  formData.Code = chineseToPinyin(formData.Name);
};

// 提交表单
const handleSubmit = async (): Promise<ConsumableInputDTO | null> => {
  if (!formRef.value) return null;
  return new Promise((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve(formData);
      } else {
        resolve(null);
      }
    });
  });
};
defineExpose({
  handleSubmit,
});
</script>

<style scoped>
.consumable-content {
  padding: 20px;
}
.consumable-form {
  max-width: 1000px;
}
.el-select {
  width: 100%;
}
</style>
