<template>
  <div class="p-20px overflow-y-auto h-650px">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" label-width="100px">
      <!-- 题目名称 -->
      <el-form-item label="题目名称" prop="Text">
        <el-input v-model="formData.Text" placeholder="请输入题目名称" />
      </el-form-item>
      <!-- 默认值 -->
      <el-form-item label="默认值" prop="Default">
        <el-input v-model="formData.Default" class="flex-1 mr-10px" placeholder="请输入Default" />
        <el-popover placement="bottom" :width="200" content="【Default】默认值，JSON字符串">
          <template #reference>
            <el-icon><QuestionFilled /></el-icon>
          </template>
        </el-popover>
      </el-form-item>
      <el-row>
        <!-- 问题计算规则 -->
        <el-form-item class="flex-1" label="Rule" prop="Rule">
          <el-input v-model="formData.Rule" class="mr-10px flex-1" placeholder="请输入Rule" />
          <el-popover
            placement="bottom"
            :width="200"
            content="计算规则，映射： Map:xxx；求和：Sum；求平均：Avg；如果不需要则不填"
          >
            <template #reference>
              <el-icon><QuestionFilled /></el-icon>
            </template>
          </el-popover>
        </el-form-item>
        <!-- 分组名 -->
        <el-form-item class="flex-1" label="分组名" prop="Group" label-width="80px">
          <el-input-tag
            v-model="formData.Group"
            tag-type="primary"
            class="flex-1 mr-10px"
            placeholder="请输入Group"
          />
          <el-popover
            placement="bottom"
            :width="200"
            content="【Group】分组名，表示问题是否分组, null表示不分组，其他值为分组名"
          >
            <template #reference>
              <el-icon><QuestionFilled /></el-icon>
            </template>
          </el-popover>
        </el-form-item>
      </el-row>
      <el-row v-if="isExtend">
        <!-- 拓展属性 - Name -->
        <el-form-item class="flex-1" label="报告中的名称" prop="Ext.Name">
          <el-input
            v-model="formData.Ext!.Name"
            class="flex-1 mr-10px"
            placeholder="请输入报告中显示的名称"
          />
          <el-popover
            placement="bottom"
            :width="200"
            content="【Ext.Name】拓展属性中的Name，用于报告中显示的名称"
          >
            <template #reference>
              <el-icon><QuestionFilled /></el-icon>
            </template>
          </el-popover>
        </el-form-item>
        <!-- 拓展属性 - Sort -->
        <el-form-item class="flex-1" label="报告中的排序" prop="Ext.Sort" label-width="122px">
          <el-input-number v-model="formData.Ext!.Sort" class="mr-10px" placeholder="排序数字" />
          <el-popover
            placement="bottom"
            :width="200"
            content="【Ext.Sort】拓展属性中的Sort，用于报告中的顺序"
          >
            <template #reference>
              <el-icon><QuestionFilled /></el-icon>
            </template>
          </el-popover>
        </el-form-item>
      </el-row>
      <el-row>
        <!-- 是否必填 -->
        <el-form-item label="是否必填" prop="Required" required>
          <el-switch v-model="formData.Required" />
        </el-form-item>
        <!-- 题目类型 -->
        <el-form-item
          v-if="formData.Type === 0 || formData.Type === 1"
          label="题目类型"
          prop="Type"
          required
        >
          <el-radio-group v-model="formData.Type">
            <el-radio :value="0">单选</el-radio>
            <el-radio :value="1">多选</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 是否拓展属性 -->
        <el-form-item label="开启拓展属性">
          <el-switch v-model="isExtend" />
        </el-form-item>
        <!-- 开启 List 配置 -->
        <el-form-item v-if="formData.Type === 0 || formData.Type === 1" label="开启 List 配置">
          <el-switch v-model="enableList" class="mr-10px" />
          <el-popover placement="bottom" :width="250">
            按 list 内的数值以及单位 unit 构造选项。
            <br />
            例如：list=[6,200,1] unit='cmH2O'
            <template #reference>
              <el-icon><QuestionFilled /></el-icon>
            </template>
          </el-popover>
        </el-form-item>
      </el-row>
      <!-- 选择题，选项配置 -->
      <template v-if="(formData.Type === 0 || formData.Type === 1) && !enableList">
        <el-card
          v-for="(option, index) in formData.Options"
          :key="'option-' + index"
          shadow="never"
          class="item-box"
        >
          <el-form-item
            label="选项描述"
            label-width="80px"
            :prop="'Options.' + index + '.Text'"
            :rules="{
              required: true,
              message: '请输入选项描述',
              trigger: 'blur',
            }"
          >
            <el-input v-model="option.Text" placeholder="请输入选项描述" />
          </el-form-item>
          <div class="flex">
            <el-form-item
              label="选项值"
              label-width="80px"
              :prop="'Options.' + index + '.Value'"
              :rules="{
                required: optionValueIsRequired,
                message: '请输入选项值',
                trigger: 'blur',
              }"
            >
              <el-input-number v-model="option.Value" placeholder="选项值" />
            </el-form-item>
            <el-form-item
              v-if="formData.Type === 0"
              label="指定题的下标"
              :prop="'Options.' + index + '.Jump'"
            >
              <el-input-number
                v-model="option.Jump"
                class="mr-10px"
                :min="1"
                placeholder="题的下标"
              />
              <el-popover
                placement="bottom"
                :width="200"
                content="单选题情况下，选择本项后，跳至下一题Key；null 表示下一题"
              >
                <template #reference>
                  <el-icon><QuestionFilled /></el-icon>
                </template>
              </el-popover>
            </el-form-item>
            <el-form-item label="是最后一题" :prop="'Options.' + index + '.IsDone'">
              <el-switch v-model="option.IsDone" />
            </el-form-item>
          </div>
          <el-button type="danger" @click="onRemoveOption(index, option)">删除</el-button>
        </el-card>
        <el-form-item label-width="0">
          <el-button type="primary" @click="onAddOption">增加单个选项</el-button>
        </el-form-item>
      </template>
      <!-- 选择题，List 配置 -->
      <el-row v-if="(formData.Type === 0 || formData.Type === 1) && enableList" align="middle">
        <el-form-item
          label="List 配置"
          label-width="80px"
          prop="StartValue"
          :rules="{
            required: true,
            message: '请输入起始值',
            trigger: 'blur',
          }"
        >
          <el-input-number
            v-model="formData.StartValue"
            :min="0"
            class="max-w-130px"
            placeholder="起始值"
          />
        </el-form-item>
        <el-form-item
          label-width="0"
          prop="EndValue"
          :rules="{
            required: true,
            message: '请输入终点值',
            trigger: 'blur',
          }"
        >
          <el-input-number
            v-model="formData.EndValue"
            class="max-w-130px"
            :min="0"
            placeholder="终点值"
          />
        </el-form-item>
        <el-form-item
          label-width="0"
          prop="SpaceValue"
          :rules="{
            required: true,
            message: '请输入间隔',
            trigger: 'blur',
          }"
        >
          <el-input-number
            v-model="formData.SpaceValue"
            class="max-w-130px"
            :min="0"
            placeholder="间隔"
          />
        </el-form-item>
        <el-form-item
          label-width="0"
          prop="Unit"
          :rules="{ required: true, message: '请输入选项单位', trigger: 'blur' }"
        >
          <el-input v-model="formData.Unit" placeholder="选项单位，unit" />
        </el-form-item>
      </el-row>
      <!-- 多选题，跳转规则 -->
      <template v-if="formData.Type === 1">
        <el-divider />
        <el-row v-for="(jump, index) in formData.Jumps" :key="'jump-' + index" class="mb-10px">
          <el-form-item class="flex-1" label="选项值组" :prop="'Jumps.' + index + '.Values'">
            <el-input v-model="jump.ValuesStr" placeholder="请输入选项值，用逗号分隔" />
          </el-form-item>
          <el-form-item class="mr-40px!" label="指定题的下标" :prop="'Jumps.' + index + '.Jump'">
            <el-input-number v-model="jump.Jump" :min="1" placeholder="题的下标" />
          </el-form-item>
          <el-button type="danger" @click="onRemoveJumpRule(index, jump)">删除</el-button>
        </el-row>
        <el-button type="primary" @click="onAddJumpRule">增加跳转规则</el-button>
      </template>
      <!-- 文本/数值题，跳转规则 -->
      <template v-if="formData.Type === 2 || formData.Type === 3">
        <el-divider />
        <el-row
          v-for="(condition, index) in formData.Conditions"
          :key="'condition-' + index"
          class="mt-10px"
        >
          <el-form-item label="匹配值范围" :prop="'Conditions.' + index + '.StartValue'">
            <el-input-number v-model="condition.StartValue" class="w-180px!" placeholder="最小值" />
          </el-form-item>
          <el-form-item label-width="0" :prop="'Conditions.' + index + '.EndValue'">
            <el-input-number v-model="condition.EndValue" class="w-180px!" placeholder="最大值" />
          </el-form-item>
          <el-form-item label-width="20px" :prop="'Conditions.' + index + '.Jump'">
            <el-input-number
              v-model="condition.Jump"
              class="w-200px!"
              :min="1"
              placeholder="跳转题的下标"
            />
          </el-form-item>
          <el-button class="ml-20px" type="danger" @click="onRemoveInputJumpRule(index, condition)">
            删除
          </el-button>
        </el-row>
        <el-form-item label-width="0">
          <el-button class="mr-10px" type="primary" @click="onAddInputJumpRule">
            增加跳转规则
          </el-button>
          <el-popover placement="bottom" :width="250">
            <p>
              左开右闭，null表示无穷小/大。
              <br />
              <br />
              示例：
              <br />
              [1,2] 表示1 < x <= 2；
              <br />
              [1, null] 表示 >1；
              <br />
              [null,2] 表示 <=2；
            </p>
            <template #reference>
              <el-icon><QuestionFilled /></el-icon>
            </template>
          </el-popover>
        </el-form-item>
      </template>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button type="primary" @click="onSubmitForm(ruleFormRef)">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
const kEnableDebug = true;

interface RiskWarningGaugeQuestionShow
  extends Omit<RiskWarningGaugeQuestion, "Jumps" | "Conditions"> {
  Jumps?: RiskWarningQuestionJumpShow[];
  Conditions?: RiskWarningQuestionConditionShow[];
  /**
   * 使用List时，起始值 List[0]
   */
  StartValue?: number;

  /**
   * 使用List时，终点值 List[1]
   */
  EndValue?: number;

  /**
   * 使用List时，间隔 List[2]
   */
  SpaceValue?: number;
}

interface RiskWarningQuestionJumpShow extends RiskWarningQuestionJump {
  /**
   * 选项值字符串，通过逗号分隔，对应 Values
   */
  ValuesStr?: string;
}

interface RiskWarningQuestionConditionShow extends RiskWarningQuestionCondition {
  /**
   * 起始匹配值，对应 Range[0]
   */
  StartValue?: number;

  /**
   * 终点匹配值，对应 Range[1]
   */
  EndValue?: number;
}

const props = defineProps<{
  question: RiskWarningGaugeQuestion;
}>();
const emit = defineEmits(["submit", "cancel"]);

// eslint-disable-next-line complexity
onMounted(() => {
  kEnableDebug && console.log("onMounted props", props);
  const data: RiskWarningGaugeQuestion = JSON.parse(JSON.stringify(props.question));
  Object.assign(formData, data);
  if (data.List && data.List.length > 2) {
    formData.StartValue = data.List[0];
    formData.EndValue = data.List[1];
    formData.SpaceValue = data.List[2];
  }
  formData.Jumps = data?.Jumps?.map((jump) => ({
    ...jump,
    ValuesStr: jump.Values?.join(","),
  }));
  formData.Conditions = data.Conditions?.map((condition) => ({
    ...condition,
    StartValue: condition.Range?.[0],
    EndValue: condition.Range?.[1],
  }));

  enableList.value = (data?.List?.length ?? 0) > 2;
  isExtend.value = Object.keys(data?.Ext ?? {}).length > 0;
});

// 表单实例
const ruleFormRef = ref<FormInstance>();
// 表单验证规则
const rules = reactive<FormRules<RiskWarningGaugeQuestionShow>>({
  Text: [{ required: true, message: "请输入题目名称", trigger: "blur" }],
});
// 表单数据
const formData = reactive<RiskWarningGaugeQuestionShow>({});
// 选择题，是否开启List配置选择项
const enableList = ref(false);
// 是否拓展属性
const isExtend = ref(false);
watch(isExtend, (val) => {
  if (val) {
    formData.Ext ??= {};
  } else {
    formData.Ext = undefined;
  }
});
const optionValueIsRequired = computed(() => {
  return ["Sum", "Avg"].includes(formData.Rule ?? "");
});

/** 添加选项 */
function onAddOption() {
  if (!formData.Options) {
    formData.Options = [];
  }
  formData.Options.push({});
}
/** 删除选项 */
function onRemoveOption(index: number, option: RiskWarningQuestionOption) {
  kEnableDebug && console.log("onRemoveOption", index, option);
  formData.Options = formData.Options!.filter((item, i) => i !== index);
}

/** 添加跳转规则 */
function onAddJumpRule() {
  if (!formData.Jumps) {
    formData.Jumps = [];
  }
  formData.Jumps.push({});
}
/** 删除跳转规则 */
function onRemoveJumpRule(index: number, jump: RiskWarningQuestionJump) {
  kEnableDebug && console.log("onRemoveJumpRule", index, jump);
  formData.Jumps = formData.Jumps!.filter((item, i) => i !== index);
}

/** 添加文本/数值题跳转规则 */
function onAddInputJumpRule() {
  if (!formData.Conditions) {
    formData.Conditions = [];
  }
  formData.Conditions.push({});
}
/** 删除文本/数值题跳转规则 */
function onRemoveInputJumpRule(index: number, condition: RiskWarningQuestionCondition) {
  kEnableDebug && console.log("onRemoveInputJumpRule", index, condition);
  formData.Conditions = formData.Conditions!.filter((item, i) => i !== index);
}

/** 提交表单 */
function onSubmitForm(ruleFormRef: FormInstance | undefined) {
  if (!ruleFormRef) return;
  if (
    !enableList.value &&
    (formData.Type === 0 || formData.Type === 1) &&
    !formData.Options?.length
  ) {
    ElMessage.warning("请至少添加一个选项");
    return;
  }

  ruleFormRef.validate((valid, fields) => {
    if (valid) {
      const data = combinedData();
      kEnableDebug && console.debug("题目 - 提交成功", data);
      emit("submit", data);
    } else {
      kEnableDebug && console.debug("题目 - 提交失败", fields);
    }
  });
}

/** 组合数据 */
function combinedData() {
  const data = {
    ...formData,
    Jumps: formData.Jumps?.map((e) => {
      const jump = {
        ...e,
        Values: e.ValuesStr?.split(",").map(Number),
      };
      delete jump.ValuesStr;
      return jump;
    }),
    Conditions: formData.Conditions?.map((e) => {
      const condition = {
        ...e,
      };
      if (condition.StartValue !== undefined || condition.EndValue !== undefined) {
        condition.Range = [condition.StartValue, condition.EndValue];
      }
      delete condition.StartValue;
      delete condition.EndValue;
      return condition;
    }),
  };
  if (
    formData.StartValue !== undefined &&
    formData.EndValue !== undefined &&
    formData.SpaceValue !== undefined
  ) {
    data.List = [formData.StartValue, formData.EndValue, formData.SpaceValue];
  }
  delete data.StartValue;
  delete data.EndValue;
  delete data.SpaceValue;

  return data;
}
</script>

<style lang="scss" scoped>
.item-box {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: stretch;
  margin-bottom: 15px;
}
</style>
