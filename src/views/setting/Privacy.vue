<template>
  <!-- <layout-box>
    <template #header>
      <div class="layout-toolbar">
        <div></div>
        <el-button type="primary" @click="add">新增</el-button>
      </div>
    </template>

    <grid-table v-loading="listLoading" :data="tableData" :page="{}">
      <el-table-column label="发布位置">
        <template slot-scope="{ row }">
          <span>{{ convertReportPosition(row.ReleasePosition) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上传时间">
        <template slot-scope="{ row }">
          <span>{{ row.CreatedTime | format }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上传人">
        <template slot-scope="{ row }">
          <span>{{ row.CreatorName }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <div class="text-center">
            <el-button type="text" @click="view(scope.row, 0)">查看</el-button>
            <el-button type="text" @click="edit(scope.row, 1)">编辑</el-button>
            <el-button type="text" @click="history(scope.row, 1)">历史版本</el-button>
          </div>
        </template>
      </el-table-column>
    </grid-table>
    <popover-form
      :visible.sync="show"
      :title="operateTitle[operateType]"
      destroy-on-close
      @close="close(1)"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        style="padding: 10px"
        :disabled="operateType === 'view'"
      >
        <el-form-item label="发布位置" prop="ReleasePosition">
          <el-select v-model="form.ReleasePosition" :disabled="operateType === 'edit'">
            <el-option
              v-for="item in clientLists"
              :key="item.Id"
              :value="item.Id"
              :label="item.Key"
              :disabled="item.isDisabled"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容" prop="Text">
          <tinymce
            v-if="show && operateType !== 'view'"
            v-model="form.Text"
            style="width: 100%; height: 500px"
          />
          <span v-if="operateType === 'view'" class="preview-repost" v-html="form.Text"></span>
        </el-form-item>
      </el-form>
      <span v-if="operateType !== 'view'" slot="footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">确认</el-button>
      </span>
    </popover-form>
    <popover-form
      :visible.sync="historyShow"
      title="历史版本"
      destroy-on-close
      @close="historyClose"
    >
      <article>
        <aside>
          <ul>
            <li
              v-for="(item, index) in historyList"
              :key="index"
              :class="{ active: historySelect === item.Version }"
              @click="asideMenuClick(item)"
            >
              {{ joinItemName(item.CreatorName, item.CreatedTime) }}
            </li>
          </ul>
        </aside>
        <section>
          <span>内容</span>
          <div class="h-content" v-html="historyContent"></div>
          <div class="btn-field">
            <el-button @click="closeHistory">关闭</el-button>
          </div>
        </section>
      </article>
    </popover-form>
  </layout-box> -->
</template>
<script>
// import {
//   GetProtocolHistory,
//   GetProtocolByType,
//   UpdateProtocol,
//   CreateProtocol,
//   GetProtocolById,
// } from "@/api/setting";
// import moment from "moment";
// const INIT_FORM = {
//   ProtocolType: 0,
//   Text: "",
//   ReleasePosition: "",
// };
// export default {
//   data() {
//     return {
//       listLoading: false,
//       tableData: [],
//       clientLists: [
//         {
//           Key: "医生端APP",
//           Id: 0,
//           isDisabled: false,
//         },
//         {
//           Key: "医生端PC",
//           Id: 1,
//           isDisabled: false,
//         },
//         {
//           Key: "患者端APP",
//           Id: 2,
//           isDisabled: false,
//         },
//         {
//           Key: "康复行医疗医生APP",
//           Id: 10,
//           isDisabled: false,
//         },
//         {
//           Key: "康复行医疗患者端",
//           Id: 12,
//           isDisabled: false,
//         },
//       ],
//       show: false,
//       operateTitle: {
//         add: "新增",
//         view: "查看",
//         edit: "编辑",
//       },
//       operateType: "add",
//       form: INIT_FORM,
//       rules: {
//         ReleasePosition: [
//           {
//             required: true,
//             message: "必须选择发布的位置",
//           },
//         ],
//         Text: [
//           {
//             required: true,
//             message: "必须填写隐私内容",
//           },
//         ],
//       },
//       historyShow: false,
//       historyContent: "",
//       historySelect: 0,
//       historyList: [],
//     };
//   },
//   created() {
//     this.search();
//   },
//   methods: {
//     convertReportPosition(id) {
//       const findVal = this.clientLists.find((val) => val.Id === id);
//       return findVal ? findVal.Key : "";
//     },
//     search() {
//       this.listLoading = true;
//       GetProtocolByType(INIT_FORM.ProtocolType).then((res) => {
//         if (res.Type === 200) {
//           res.Data.forEach((el) => {
//             this.clientLists.forEach((item) => {
//               if (el.ReleasePosition === item.Id) {
//                 item.isDisabled = true;
//               }
//             });
//           });
//           this.tableData = res.Data;
//         }
//         this.listLoading = false;
//       });
//     },
//     add() {
//       this.show = true;
//       this.operateType = "add";
//       this.form = { ...INIT_FORM };
//       this.form.Text = "";
//       delete this.form.id;
//     },
//     view(row) {
//       this.show = true;
//       this.form.Id = row.ContentId;
//       this.operateType = "view";
//       GetProtocolById(row.ContentId).then((data) => {
//         this.form = data.Data;
//         this.show = true;
//       });
//     },
//     edit(row) {
//       this.form.Id = row.ContentId;
//       this.operateType = "edit";
//       GetProtocolById(row.ContentId).then((data) => {
//         this.form = data.Data;
//         this.show = true;
//       });
//     },
//     close() {
//       this.form = INIT_FORM;
//       this.$refs.form.resetFields();
//     },
//     cancel() {
//       this.show = false;
//       this.form = INIT_FORM;
//       this.$refs.form.resetFields();
//     },
//     onSubmit() {
//       this.$refs.form.validate((valid) => {
//         if (valid) {
//           this.operateOrg();
//         } else {
//           return false;
//         }
//       });
//     },
//     operateOrg() {
//       // 非编辑和新增不执行
//       if (this.operateType !== "add" && this.operateType !== "edit") {
//         return false;
//       }
//       let requestUrl;
//       let reqParams;
//       let callMsg;
//       if (this.operateType === "add") {
//         console.log(this.form.ReleasePosition);
//         requestUrl = CreateProtocol;
//         reqParams = {
//           ReleasePosition: this.form.ReleasePosition,
//           ProtocolType: INIT_FORM.ProtocolType,
//           Text: this.form.Text,
//         };
//         callMsg = "新增成功";
//       } else {
//         requestUrl = UpdateProtocol;
//         reqParams = {
//           ContentId: this.form.ContentId,
//           Text: this.form.Text,
//         };
//         callMsg = "编辑成功";
//       }

//       requestUrl(reqParams).then((data) => {
//         if (data.Type === 200) {
//           this.$message({
//             type: "success",
//             message: callMsg,
//           });
//           this.search();
//           this.show = false;
//         } else {
//           this.$message({
//             type: "warning",
//             message: data.Content,
//           });
//         }
//       });
//     },
//     history(row) {
//       this.historyShow = true;
//       GetProtocolHistory(row.ContentId).then((res) => {
//         if (res.Type === 200) {
//           this.historyList = res.Data;
//         } else {
//           this.$alert(res.Content, "系统提示", {
//             confirmButtonText: "确定",
//             type: "warning",
//           });
//         }
//       });
//     },
//     historyClose() {
//       this.historyList = [];
//       this.historyContent = "";
//     },
//     closeHistory() {
//       this.historyList = [];
//       this.historyContent = "";
//       this.historyShow = false;
//     },
//     asideMenuClick(item) {
//       this.historySelect = item.Version;
//       this.historyContent = item.Text;
//     },
//     // 拼接历史列表名称数据
//     joinItemName(name, date) {
//       return `${name}(${moment(date).format("YYYY-MM-DD")})`;
//     },
//   },
// };
</script>
<style lang="scss" scoped>
article {
  display: flex;
  height: 500px;
  padding: 10px;
  aside {
    width: 260px;
    height: 100%;
    border: 1px solid #ddd;
    overflow: hidden;
    overflow-y: auto;
    ul {
      list-style: none;
      padding: 0;
    }
    li {
      width: 100%;
      height: 40px;
      line-height: 40px;
      padding-left: 18px;
      border-bottom: 1px solid #ddd;
      list-style: none;
      cursor: pointer;
      &:hover {
        background: #e9f9f1;
      }
      &.active {
        background: #f9f9f9;
      }
    }
  }
  section {
    width: calc(100% - 270px);
    padding: 20px;
    .h-content {
      height: calc(100% - 60px);
      margin: 10px;
      padding: 20px;
      overflow: scroll;
      border: 1px solid #ddd;
    }
    .btn-field {
      margin-top: 10px;
      padding: 0px 10px;
      text-align: right;
    }
  }
}
.preview-repost {
  display: block;
  height: 500px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
