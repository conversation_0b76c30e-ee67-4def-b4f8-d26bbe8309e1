<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <div class="flex justify-end p-10px">
          <el-button type="primary" @click="add">新增</el-button>
        </div>
      </template>
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          stripe
          border
          :height="tableFluidHeight"
          label-width="auto"
        >
          <el-table-column
            label="发布位置"
            align="center"
            prop="ReleasePosition"
            :formatter="mapPosition"
          />
          <el-table-column
            label="创建时间"
            align="center"
            prop="CreatedTime"
            :formatter="formatDate"
          />
          <el-table-column label="创建人" align="center" prop="CreatorName" />
          <el-table-column
            label="更新时间"
            align="center"
            prop="UpdatedTime"
            :formatter="formatDate"
          />
          <el-table-column fixed="right" label="操作" width="300px" align="center">
            <template #default="{ row }">
              <el-button link type="primary" @click="preview(row)">查看</el-button>
              <el-button link type="primary" @click="edit(row)">编辑</el-button>
              <el-button link type="primary" @click="history(row)">历史版本</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 新增/编辑 -->
  <el-dialog
    v-model="show"
    :title="operateTitleMap[operateType]"
    :close-on-click-modal="!historyDialogFooter"
    :close-on-press-escape="!historyDialogFooter"
    :show-close="!historyDialogFooter"
    align-center
    destroy-on-close
    class="min-w-1200px"
  >
    <el-form
      v-if="form"
      ref="formRef"
      :model="form"
      :rules="rules"
      :disabled="operateType === 'view'"
      label-position="top"
      label-width="auto"
    >
      <el-form-item label="发布位置" prop="ReleasePosition" label-position="left">
        <el-select v-model="form.ReleasePosition">
          <el-option
            v-for="item in clientList"
            :key="item.code"
            :value="item.code"
            :label="item.name"
            :disabled="item.isDisabled"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="内容" prop="Text">
        <WangEditor
          v-if="show && operateType !== 'view'"
          v-model="form.Text"
          height="500px"
          class="w-full"
        />
        <div v-if="operateType === 'view'" class="preview-content" v-html="form.Text" />
      </el-form-item>
    </el-form>
    <template v-if="historyDialogFooter" #footer class="text-right">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">确认</el-button>
    </template>
  </el-dialog>

  <!-- 历史版本 -->
  <el-dialog
    v-model="historyShow"
    class="min-w-800px"
    title="历史版本"
    fullscreen
    destroy-on-close
    @close="onHistoryDialogClose"
  >
    <article>
      <aside>
        <ul>
          <li
            v-for="(item, index) in historyList"
            :key="index"
            :class="{ active: historySelectIndex === index }"
            @click="handleHistoryItemClick(index)"
          >
            {{ formatDate(item, null, item.UpdatedTime, 0) }}
          </li>
        </ul>
      </aside>
      <section>
        <div v-html="historyContent" />
      </section>
    </article>
  </el-dialog>
</template>
<script lang="ts">
import Content_Api from "@/api/content";
import { BaseProtocol } from "@/api/content/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import dayjs from "dayjs";
import { FormItemRule } from "element-plus";

/** 协议类型
 * 1:用户协议
 */
const PROTOCOL_TYPE = 1;
const INIT_FORM: Partial<BaseProtocol> = {
  Text: "",
  ReleasePosition: undefined,
};

const clientList = [
  {
    name: "医生端APP",
    code: 0,
    isDisabled: false,
  },
  {
    name: "康复行医疗医生APP",
    code: 10,
    isDisabled: false,
  },
  {
    name: "医生端PC",
    code: 1,
    isDisabled: false,
  },
  {
    name: "患者端APP",
    code: 2,
    isDisabled: false,
  },
  {
    name: "康复行医疗患者端",
    code: 12,
    isDisabled: false,
  },
  {
    name: "互联网医疗执业协议",
    code: 3,
    isDisabled: false,
  },
];

export default defineComponent({
  setup() {
    const { pageData, tableLoading, tableFluidHeight, tableResize } =
      useTableConfig<BaseProtocol>();
    return {
      pageData,
      tableLoading,
      tableFluidHeight,
      tableResize,
    };
  },
  data() {
    return {
      clientList,
      show: false,
      operateTitleMap: {
        add: "新增",
        view: "查看",
        edit: "编辑",
      },
      operateType: "add" as "add" | "view" | "edit",
      form: null as Partial<BaseProtocol> | null,
      rules: {
        ReleasePosition: [
          {
            type: "number",
            required: true,
            message: "必须选择发布的位置",
          },
        ],
        Text: [
          {
            required: true,
            message: "必须填写协议内容",
          },
        ],
      } as Record<string, FormItemRule[]>,
      historyShow: false,
      historySelectIndex: null as number | null,
      historyList: [] as BaseProtocol[],
    };
  },
  computed: {
    historyDialogFooter() {
      return this.operateType !== "view";
    },
    historyContent() {
      if (this.historySelectIndex === null) {
        return "";
      }
      return this.historyList[this.historySelectIndex].Text;
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    formatDate(row: BaseProtocol, column: any, cellValue: any, index: number) {
      return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
    },
    mapPosition(row: BaseProtocol, column: any, cellValue: any, index: number) {
      const findVal = this.clientList.find((val) => val.code === cellValue);
      return findVal ? findVal.name : "";
    },
    async loadData() {
      this.tableLoading = true;
      const res = await Content_Api.getProtocolByType(PROTOCOL_TYPE);
      this.tableLoading = false;

      if (res.Type === 200) {
        const releasePositionList = new Set(res.Data.map((el) => el.ReleasePosition));
        // 禁用已发布的发布位置
        this.clientList.forEach((item) => {
          if (releasePositionList.has(item.code)) {
            item.isDisabled = true;
          }
        });
        this.pageData = res.Data;
      }
    },
    add() {
      this.operateType = "add";
      this.form = { ...INIT_FORM };
      this.form.Text = "";
      this.show = true;
    },
    async preview(row: BaseProtocol) {
      this.operateType = "view";
      const res = await Content_Api.getProtocolById(row.ContentId);
      if (res.Type != 200) {
        ElMessage.warning(res.Content);
        return;
      }
      this.form = res.Data;
      this.show = true;
    },
    async edit(row: BaseProtocol) {
      this.operateType = "edit";

      const res = await Content_Api.getProtocolById(row.ContentId);
      if (res.Type != 200) {
        ElMessage.warning(res.Content);
        return;
      }
      this.form = res.Data;
      this.show = true;
    },

    cancel() {
      this.show = false;
      this.form = null;
    },
    /**
     * 提交表单数据
     * @description 处理新增和编辑协议内容的提交操作
     */
    async submit() {
      // 获取表单引用的类型
      const form = this.$refs.formRef as any;

      // 表单验证
      const valid = await form.validate().catch((err: Record<string, FormItemRule[]>) => {
        const key = Object.keys(err)[0];
        const rule = err[key][0];
        ElMessage.warning(typeof rule.message === "function" ? rule.message() : rule.message);
        return false;
      });
      if (!valid) return;

      // 检查操作类型
      if (!["add", "edit"].includes(this.operateType)) return;

      // 构建请求参数
      const isAdd = this.operateType === "add";
      const reqParams = isAdd
        ? {
            ReleasePosition: this.form!.ReleasePosition,
            ProtocolType: PROTOCOL_TYPE,
            Text: this.form!.Text,
          }
        : {
            ContentId: this.form!.ContentId,
            Text: this.form!.Text,
          };

      // 发送请求
      const requestApi = isAdd ? Content_Api.createProtocol : Content_Api.updateProtocol;
      const res = await requestApi(reqParams);

      // 处理响应
      if (res.Type === 200) {
        this.show = false;
        ElMessage.success(isAdd ? "新增成功" : "编辑成功");
        this.loadData();
      } else {
        ElMessage.warning(res.Content);
      }
    },

    async history(row: BaseProtocol) {
      const res = await Content_Api.getProtocolHistory(row.ContentId);
      if (res.Type === 200) {
        this.historyList = res.Data;
        if (res.Data.length > 0) {
          this.historySelectIndex = 0;
        }
        this.historyShow = true;
      } else {
        this.$alert(res.Content, "系统提示", {
          confirmButtonText: "确定",
          type: "warning",
        });
      }
    },
    onHistoryDialogClose() {
      this.historyList = [];
      this.historySelectIndex = null;
    },
    handleHistoryItemClick(index: number) {
      this.historySelectIndex = index;
    },
  },
});
</script>
<style lang="scss" scoped>
.preview-content {
  height: 500px;
  width: 100%;
  overflow-y: auto;
  background-color: var(--el-fill-color-light);
  padding: 10px;
  border-radius: 4px;
}
article {
  display: flex;

  aside {
    width: 260px;
    height: 100%;
    border: 1px solid #ddd;
    overflow: hidden;
    overflow-y: auto;
    ul {
      list-style: none;
      padding: 0;
    }
    li {
      width: 100%;
      height: 40px;
      line-height: 40px;
      padding-left: 18px;
      border-bottom: 1px solid #ddd;
      list-style: none;
      cursor: pointer;
      &:hover {
        background: #ddd;
      }
      &.active {
        background: var(--el-color-primary-light-7);
      }
    }
  }
  section {
    flex: 1;
    padding: 10px;
  }
}
</style>
