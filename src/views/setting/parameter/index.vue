<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          :height="tableFluidHeight"
          stripe
          border
          row-key="Code"
        >
          <el-table-column prop="Label" label="参数名称" width="240" />
          <el-table-column prop="ShowCode" label="参数编码" width="120" align="center" />
          <el-table-column label="参数值" align="center">
            <template #default="{ row }">
              <span v-for="(item, index) in row.Payload" :key="index" class="scope-span">
                {{ row.Payload.length > 1 ? item.UIHint.Label + ":" : "" }}
                {{ item.UIHint.CharacterType === "bool" ? (item.Value ? "是" : "否") : item.Value }}
              </span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="120" align="center">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleActionClick(row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>

    <el-dialog
      v-model="dialogVisible"
      title="参数设置"
      :close-on-click-modal="false"
      destroy-on-close
      align-center
      class="min-w-500px"
    >
      <Modify
        v-if="dialogVisible"
        ref="modify"
        @success="
          () => {
            loadData();
            closeDialog();
          }
        "
        @cancel="closeDialog"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Modify from "./Modify.vue";
import Bff_Api from "@/api/bff";
import { useTableConfig } from "@/hooks/useTableConfig";
import { Setting } from "./config";

const { pageData, total, kTableRef, tableLoading, tableFluidHeight, tableResize } =
  useTableConfig<Setting>();
const modifyRef = useTemplateRef("modify");

async function loadData() {
  tableLoading.value = true;

  const res = await Bff_Api.getSettingList({
    Filter: {
      Enabled: true,
    },
  });

  tableLoading.value = false;

  if (res.Type === 200) {
    pageData.value = res.Data;
    total.value = res.Data.length;
  }
}

onMounted(() => {
  loadData();
});

const dialogVisible = ref(false);

function handleActionClick(row: Setting) {
  dialogVisible.value = true;
  setTimeout(() => {
    modifyRef.value?.initData(row);
  }, 10);
}
// 关闭弹出框
function closeDialog() {
  dialogVisible.value = false;
}
</script>
