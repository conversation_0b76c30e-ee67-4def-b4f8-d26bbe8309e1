<template>
  <el-form v-if="info" ref="ruleForm" :model="info" label-width="auto" :disabled="isOper">
    <el-form-item label="参数编码：">
      {{ info.ShowCode }}
    </el-form-item>
    <el-form-item label="参数名称：">
      {{ info.Label }}
    </el-form-item>
    <el-form-item v-for="(item, index) in payload" :key="index" :label="item.UIHint.Label + '：'">
      <el-input
        v-if="item.UIHint.Type === 'input'"
        v-model="item.Value as string"
        type="text"
        :placeholder="item.UIHint.Placeholder"
      />
      <el-input
        v-else-if="item.UIHint.Type === 'textarea'"
        v-model="item.Value as string"
        type="textarea"
        :rows="2"
        :placeholder="item.UIHint.Placeholder"
      />
      <el-input
        v-else-if="item.UIHint.Type === 'textarea-min'"
        v-model="item.Value as string"
        type="textarea"
        :rows="2"
        :placeholder="item.UIHint.Placeholder"
      />
      <el-switch v-else-if="item.UIHint.Type === 'switch'" v-model="item.Value as boolean" />
    </el-form-item>
    <el-form-item v-if="info.Remark" label="描述：">
      {{ info.Remark }}
    </el-form-item>
  </el-form>
  <div class="text-right">
    <el-button size="small" @click="emit('cancel')">取 消</el-button>
    <el-button :loading="isOper" size="small" type="primary" @click="save">确 定</el-button>
  </div>
</template>
<script setup lang="ts">
import Bff_Api from "@/api/bff";
import { Setting, SettingPayload, RegexData } from "./config";
import { FormInstance } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";

const emit = defineEmits(["cancel", "success"]);

const info = ref<Setting | null>(null);
const payload = ref<SettingPayload[]>([]);
// 显示弹出框
const initData = (row: Setting) => {
  if (row) {
    info.value = { ...row };
    const payLoad: SettingPayload[] = JSON.parse(JSON.stringify(info.value.Payload));
    payLoad.forEach((item) => {
      if (item.UIHint.CharacterType === "int") {
        item.Value = item.Value + "";
      }
    });
    payload.value = payLoad;
  }
};

const isOper = ref(false);
const ruleForm = useTemplateRef<FormInstance>("ruleForm");

// 保存
const save = async () => {
  isOper.value = true;
  // 验证参数值
  const validatePayload = (payload: SettingPayload) => {
    // 如果是布尔类型则跳过验证
    if (payload.UIHint.CharacterType === "bool") {
      return { success: true, msg: "" };
    }

    // 验证必填
    if (!payload.Value) {
      return { success: false, msg: `请填写【${payload.UIHint.Label}】` };
    }

    // 验证正则规则
    if (payload.UIHint.Regex && RegexData[payload.UIHint.Regex]) {
      const rules = RegexData[payload.UIHint.Regex];
      return rules.some((rule) => rule.pattern.test(`${payload.Value}`))
        ? { success: true, msg: "" }
        : { success: false, msg: `【${payload.UIHint.Label}】${rules[0].message}` };
    }

    return { success: true, msg: "" };
  };

  // 获取第一个验证失败的参数
  const failedPayload = payload.value
    .map((item) => validatePayload(item))
    .find((item) => !item.success);

  if (failedPayload) {
    // 显示错误提示
    await ElMessageBox.alert(failedPayload.msg, "系统提示", {
      confirmButtonText: "确定",
      type: "warning",
    });
    isOper.value = false;
    return;
  }

  const params: Setting = { ...info.value! };
  payload.value.forEach((item) => {
    if (item.UIHint.CharacterType === "int") {
      item.Value = +item.Value;
    }
  });
  params.Payload = payload.value;
  const res = await Bff_Api.modifySetting(params);

  isOper.value = false;
  if (res.Type != 200) {
    ElMessageBox.alert(res.Content, "系统提示", {
      confirmButtonText: "确定",
      type: "warning",
    });
    return;
  }

  ElMessage.success("成功");
  emit("success");
};

defineExpose({
  initData,
});
</script>
