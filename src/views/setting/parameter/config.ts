export type RegexType = "RegexNum" | "RegexNum1" | "RegexNum2";
export type UIHintType = "input" | "textarea" | "textarea-min" | "switch";

export interface SettingPayload {
  Key: string;
  Value: string | number | boolean;
  UIHint: {
    Type: UIHintType;
    CharacterType: string;
    Label: string;
    Placeholder: string;
    Regex: RegexType | null;
  };
}

export interface Setting {
  Code: string;
  Label: string;
  ShowCode: string;
  Remark?: string;
  Payload: SettingPayload[];
}

export interface RegexRule {
  type: string;
  required: boolean;
  pattern: RegExp;
  message: string;
  trigger: string;
}

export const RegexData: Record<RegexType, RegexRule[]> = {
  RegexNum: [
    {
      type: "string",
      required: true,
      // pattern: /^\d+\.?\d*$/,
      pattern: /^[1-9]\d*$/,
      message: "范围为正整数",
      trigger: "blur",
    },
  ],
  RegexNum1: [
    {
      type: "string",
      required: true,
      // pattern: /^\d+\.?\d*$/,
      pattern: /^(([1-9](\.\d{2})?)|10|10.0|10.00)$/,
      message: "范围为1-10可带两位小数",
      trigger: "blur",
    },
  ],
  RegexNum2: [
    {
      type: "string",
      required: true,
      pattern: /^([3-9]|10)$/,
      message: "范围为3-10的正整数",
      trigger: "blur",
    },
  ],
};
