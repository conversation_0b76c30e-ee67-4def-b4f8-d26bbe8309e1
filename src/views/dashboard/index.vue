<template>
  <div class="dashboard-container">
    <!-- 顶部概览卡片 -->
    <div class="overview-section">
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yiyuan.png" alt="医院" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <el-badge
                :value="info.NewOrganizationCount"
                :hidden="!Number(info.NewOrganizationCount)"
                class="badge"
              >
                <span class="number">{{ info.OrganizationCount }}</span>
              </el-badge>
            </div>
            <div class="card-label">医院数量</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yisheng.png" alt="医生" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.AuthDoctorCount }}</span>
            </div>
            <div class="card-label">累计认证医生</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yisheng_1.png" alt="治疗师" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.AuthTherapistCount }}</span>
            </div>
            <div class="card-label">累计认证治疗师</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yisheng_1.png" alt="护士" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.AuthNurseCount }}</span>
            </div>
            <div class="card-label">累计认证护士</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yonghu.png" alt="用户" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.NormalUserCount }}</span>
            </div>
            <div class="card-label">普通用户总数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和待办事项区域 -->
    <div class="charts-todo-section">
      <!-- 图表区域 -->
      <div class="charts-section">
        <div class="chart-row">
          <div ref="chart1Container" class="chart-card chart-card-top" />
          <div ref="chart2Container" class="chart-card chart-card-top" />
        </div>
      </div>

      <!-- 待办事项卡片 -->
      <div class="todo-section">
        <div class="todo-card">
          <div class="todo-header">
            <h3>待办事项</h3>
          </div>
          <div class="todo-content">
            <div class="todo-item" @click="handleToUrl('DD')">
              <span class="todo-label">待发货订单</span>
              <span class="todo-count">{{ info.WaitSendGoodsOrderCount }}</span>
            </div>
            <div class="todo-item" @click="handleToUrl('YJ')">
              <span class="todo-label">待退还押金</span>
              <span class="todo-count">{{ info.WaitRefundedDeviceBond }}</span>
            </div>
            <div class="todo-item" @click="handleToUrl('YS')">
              <span class="todo-label">待审核医生</span>
              <span class="todo-count">{{ info.WaitAuthDoctorCount }}</span>
            </div>
            <div class="todo-item" @click="handleToUrl('FK')">
              <span class="todo-label">用户反馈</span>
              <span class="todo-count">{{ info.UnReadUserFeedback }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计数据区域 -->
    <div class="stats-section">
      <div class="stats-header">
        <h3>统计数据</h3>
        <div class="stats-filters">
          <div label="医院">
            <HospitalSelect v-model="query1.parameters.orgId" />
          </div>
          <div label="数据视图">
            <el-radio-group v-model="radio" @input="radioChange">
              <el-radio value="Day">日</el-radio>
              <el-radio value="Week">周</el-radio>
              <el-radio value="Month">月</el-radio>
            </el-radio-group>
          </div>
          <div label="时间">
            <el-date-picker
              v-model="query1.parameters.timeRange.start"
              type="date"
              placeholder="开始日期"
              :picker-options="maxDateOptions"
              class="date-picker"
              :clearable="false"
            />
            -
            <el-date-picker
              v-model="query1.parameters.timeRange.end"
              type="date"
              placeholder="结束日期"
              :picker-options="maxDateOptions"
              class="date-picker"
              :clearable="false"
            />
            <el-button type="primary" class="confirm-btn" @click="handleRefreshData">
              确认
            </el-button>
          </div>
        </div>
      </div>

      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>医生/治疗师/护士</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorCount || 0 }} / {{ baseData.TherapistCount || 0 }} /
                {{ baseData.NurseCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorCount || 0 }} /
                {{ baseData.YesterdayTherapistCount || 0 }} /
                {{ baseData.YesterdayNurseCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorCount || 0 }} / {{ baseData.DailyTherapistCount || 0 }} /
                {{ baseData.DailyNurseCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>就诊数(医/治/护)</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.ConsultCount || 0 }} / {{ baseData.TreatmentCount || 0 }}/
                {{ baseData.NurseConsultCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayConsultCount || 0 }} /
                {{ baseData.YesterdayTreatmentCount || 0 }} /
                {{ baseData.YesterdayNurseConsultCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyConsultCount || 0 }} / {{ baseData.DailyTreatmentCount || 0 }} /
                {{ baseData.DailyNurseConsultCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>就诊收入(医/治/护)</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorConsultAmount || 0 }} /
                {{ baseData.TherapistConsultAmount || 0 }} / {{ baseData.NurseConsultAmount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorConsultAmount || 0 }} /
                {{ baseData.YesterdayTherapistConsultAmount || 0 }} /
                {{ baseData.YesterdayTherapistConsultAmount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorConsultAmount || 0 }} /
                {{ baseData.DailyTherapistConsultAmount || 0 }} /
                {{ baseData.DailyNurseConsultAmount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>主动开方（医/治/护）</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorDirectPrescriptionCount || 0 }} /
                {{ baseData.TherapistDirectPrescriptionCount || 0 }} /
                {{ baseData.NurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorDirectPrescriptionCount || 0 }} /
                {{ baseData.YesterdayTherapistDirectPrescriptionCount || 0 }} /
                {{ baseData.YesterdayNurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorDirectPrescriptionCount || 0 }} /
                {{ baseData.DailyTherapistDirectPrescriptionCount || 0 }} /
                {{ baseData.DailyNurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>快速开方（医/治/护）</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorQuickPrescriptionCount || 0 }} /
                {{ baseData.TherapistQuickPrescriptionCount || 0 }} /
                {{ baseData.NurseQuickPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorQuickPrescriptionCount || 0 }} /
                {{ baseData.YesterdayTherapistQuickPrescriptionCount || 0 }} /
                {{ baseData.YesterdayNurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorQuickPrescriptionCount || 0 }} /
                {{ baseData.DailyTherapistQuickPrescriptionCount || 0 }} /
                {{ baseData.DailyNurseQuickPrescriptionCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>就诊开方(医/治/护)</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorConsultPrescriptionCount || 0 }} /
                {{ baseData.TherapistConsultPrescriptionCount || 0 }} /
                {{ baseData.NurseConsultPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorConsultPrescriptionCount || 0 }} /
                {{ baseData.YesterdayTherapistConsultPrescriptionCount || 0 }} /
                {{ baseData.YesterdayNurseConsultPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorConsultPrescriptionCount || 0 }} /
                {{ baseData.DailyTherapistConsultPrescriptionCount || 0 }} /
                {{ baseData.DailyNurseConsultPrescriptionCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>治疗收入</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">￥{{ baseData.VisitAmount || 0 }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">￥{{ baseData.YesterdayVisitAmount || 0 }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">￥{{ baseData.DailyVisitAmount || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部图表区域 -->
    <div class="bottom-charts-section">
      <div class="chart-grid">
        <div ref="chart3Container" class="chart-card" />
        <div ref="chart4Container" class="chart-card" />
        <div ref="chart5Container" class="chart-card" />
        <div ref="chart6Container" class="chart-card" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Report_Api from "@/api/report";
import {
  PlatformHomePageChart,
  PlatformHomePageStatistics,
  PlatformHomePageSummary,
} from "@/api/report/types";
import { useRouter } from "vue-router";
import * as echarts from "echarts";
import type { EChartsType } from "echarts";
import { calculateAmount } from "@/utils";
import dayjs from "dayjs";

// 添加工具函数用于防抖处理
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null;
  return function (this: any, ...args: any[]) {
    if (timer) {
      clearTimeout(timer);
    }
    timer = window.setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay);
  };
};

const router = useRouter();
const info = ref<PlatformHomePageSummary>({} as PlatformHomePageSummary);
const baseData = ref<PlatformHomePageStatistics>({} as PlatformHomePageStatistics);
// 将Chart1-6改为普通变量定义
let Chart1: EChartsType | undefined;
let Chart2: EChartsType | undefined;
let Chart3: EChartsType | undefined;
let Chart4: EChartsType | undefined;
let Chart5: EChartsType | undefined;
let Chart6: EChartsType | undefined;
const chart1Container = ref<HTMLDivElement>();
const chart2Container = ref<HTMLDivElement>();
const chart3Container = ref<HTMLDivElement>();
const chart4Container = ref<HTMLDivElement>();
const chart5Container = ref<HTMLDivElement>();
const chart6Container = ref<HTMLDivElement>();
const maxDateOptions = {
  disabledDate(time: any) {
    return time.getTime() > new Date(); // 如果没有后面的-8.64e7就是不可以选择今天的
  },
};
const query1 = ref({
  queryName: "Report_PlatformHomePageStatistics",
  parameters: {
    orgId: null,
    timeRange: {
      start: dayjs(new Date()).format("YYYY-MM-01") + " 00:00:00",
      end: dayjs(new Date()).format("YYYY-MM-DD") + " 23:59:59",
    },
  },
  maxAge: 0,
  JobWaitingMs: 30000,
  pageIndex: 1,
  pageSize: 9999,
});
const radio = ref<string>("Day");
const disabledNextCount = ref<boolean>(false);
const handleToUrl = (type: string) => {
  switch (type) {
    case "DD":
      router.push({
        path: "/order-management/therapyOrder",
        query: {
          states: 1,
        },
      });
      break;
    case "YJ":
      // router.push({
      //   path: "/orderMar/orderInstru",
      //   query: {
      //     deviceState: 4,
      //     limitRemainMaxDays: 3,
      //   },
      // });
      break;
    case "YS":
      // router.push({ path: "/user-manage/doctor-verify" });
      break;
    case "FK":
      // router.push({ path: "/feedbackIndex/feedback" });
      break;
    default:
      break;
  }
};

const handleGetTopDataInfo = async () => {
  const res = await Report_Api.getRedashList<PlatformHomePageSummary>({
    queryName: "Report_PlatformHomePageSummary",
    parameters: {},
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 999,
  });
  if (res.Type === 200) {
    info.value = res.Data.Data[0];
    initChart1(res.Data.Data[0]);
    initChart2(res.Data.Data[0]);
  }
};

const initCharts = () => {
  // 使用辅助函数初始化每个图表
  Chart1 = initSingleChart(Chart1, chart1Container, "Chart1");
  Chart2 = initSingleChart(Chart2, chart2Container, "Chart2");
  Chart3 = initSingleChart(Chart3, chart3Container, "Chart3");
  Chart4 = initSingleChart(Chart4, chart4Container, "Chart4");
  Chart5 = initSingleChart(Chart5, chart5Container, "Chart5");
  Chart6 = initSingleChart(Chart6, chart6Container, "Chart6");
};

// 辅助函数用于初始化单个图表
const initSingleChart = (
  chartInstance: EChartsType | undefined,
  containerRef: Ref<HTMLDivElement | undefined>,
  chartName: string
) => {
  if (containerRef.value && !chartInstance) {
    if (containerRef.value.offsetWidth > 0 && containerRef.value.offsetHeight > 0) {
      try {
        return echarts.init(containerRef.value);
      } catch (error) {
        console.error(`Failed to initialize ${chartName}:`, error);
      }
    }
  }
  return chartInstance;
};

const checkAndInitChart = (
  chartInstance: EChartsType | undefined,
  containerRef: Ref<HTMLDivElement | undefined>
): [boolean, EChartsType?] => {
  if (!chartInstance && containerRef.value) {
    try {
      if (containerRef.value.offsetWidth > 0 && containerRef.value.offsetHeight > 0) {
        const newChart = echarts.init(containerRef.value);
        return [true, newChart];
      }
    } catch (error) {
      console.error("Failed to initialize chart:", error);
    }
  }
  return [!!chartInstance, undefined];
};

const initChart1 = (obj: PlatformHomePageSummary) => {
  const [success, newChart] = checkAndInitChart(Chart1, chart1Container);
  if (!success) return;
  if (newChart) Chart1 = newChart;

  const doctorConsult = calculateAmount([obj.DoctorConsultCount], "+");
  const therapistConsult = calculateAmount([obj.TherapistConsultCount], "+");
  const nurseConsult = calculateAmount([obj.NurseConsultCount], "+");
  const doctorAmount = calculateAmount([obj.DoctorConsultAmount], "+");
  const therapistAmount = calculateAmount([obj.TherapistConsultAmount], "+");
  const nurseAmount = calculateAmount([obj.NurseConsultAmount], "+");

  try {
    Chart1!.setOption({
      title: {
        text: "就诊数据统计",
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        formatter: function (params: any[]) {
          // 获取当前选中的是哪一行（就诊数量或就诊收入）
          const categoryName = params[0].name;

          // 只保留当前行相关的数据
          const filteredParams = params.filter((param) => {
            // 对于"就诊数量"行，只保留xAxisIndex为0的系列
            if (categoryName === "就诊数量" && param.seriesIndex < 3) {
              return true;
            }
            // 对于"就诊收入"行，只保留xAxisIndex为1的系列
            if (categoryName === "就诊收入" && param.seriesIndex >= 3) {
              return true;
            }
            return false;
          });

          // 如果没有数据，返回空
          if (filteredParams.length === 0) {
            return "";
          }

          // 构建提示文本
          let result = `${categoryName}<br/>`;
          let unit = categoryName === "就诊数量" ? "次" : "元";
          filteredParams.forEach((param) => {
            // 使用彩色标记和名称
            result += `${param.marker} ${param.seriesName}: ${param.value}${unit}<br/>`;
          });

          return result;
        },
      },
      legend: {
        left: "right",
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: [
        {
          type: "value",
          min: 0,
          minInterval: 100, // 最小单位为100次
          position: "bottom",
          axisLabel: {
            formatter: "{value}次",
          },
        },
        {
          type: "value",
          min: 0,
          minInterval: 100, // 最小单位为100元
          position: "top",
          axisLabel: {
            formatter: "{value}元",
          },
        },
      ],
      yAxis: {
        type: "category",
        data: ["就诊数量", "就诊收入"],
        axisPointer: {
          type: "shadow",
        },
      },
      series: [
        // 就诊数量系列 - 只使用下方X轴(xAxisIndex: 0)
        {
          name: "医生问诊",
          type: "bar",
          barWidth: 25,
          stack: "图表",
          label: {
            show: true,
            position: "inside",
          },
          xAxisIndex: 0,
          data: [doctorConsult, "-"], // 第一个位置是"就诊数量"行
          itemStyle: {
            color: "#52c41a", // 医生颜色
          },
        },
        {
          name: "治疗师咨询",
          type: "bar",
          stack: "图表",
          xAxisIndex: 0,
          label: {
            show: true,
            position: "inside",
          },
          data: [therapistConsult, "-"], // 第一个位置是"就诊数量"行
          itemStyle: {
            color: "#faad14", // 治疗师颜色
          },
        },
        {
          name: "护士咨询",
          type: "bar",
          stack: "图表",
          xAxisIndex: 0,
          label: {
            show: true,
            position: "inside",
          },
          data: [nurseConsult, "-"], // 第一个位置是"就诊数量"行
          itemStyle: {
            color: "#2ed1d1", // 护士颜色
          },
        },
        // 就诊收入系列 - 只使用上方X轴(xAxisIndex: 1)
        {
          name: "医生问诊",
          type: "bar",
          barWidth: 25,
          stack: "图表",
          xAxisIndex: 1,
          label: {
            show: true,
            position: "inside",
          },
          data: ["-", doctorAmount], // 第二个位置是"就诊收入"行
          itemStyle: {
            color: "#52c41a", // 医生颜色
          },
        },
        {
          name: "治疗师咨询",
          type: "bar",
          stack: "图表",
          xAxisIndex: 1,
          label: {
            show: true,
            position: "inside",
          },
          data: ["-", therapistAmount], // 第二个位置是"就诊收入"行
          itemStyle: {
            color: "#faad14", // 治疗师颜色
          },
        },
        {
          name: "护士咨询",
          type: "bar",
          stack: "图表",
          xAxisIndex: 1,
          label: {
            show: true,
            position: "inside",
          },
          data: ["-", nurseAmount], // 第二个位置是"就诊收入"行
          itemStyle: {
            color: "#2ed1d1", // 护士颜色
          },
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart1:", error);
  }
};

const initChart2 = (obj: PlatformHomePageSummary) => {
  const [success, newChart] = checkAndInitChart(Chart2, chart2Container);
  if (!success) return;
  if (newChart) Chart2 = newChart;

  const doctorPrescription = calculateAmount([obj.DoctorPrescriptionCount], "+");
  const therapistPrescription = calculateAmount([obj.TherapistPrescriptionCount], "+");
  const nursePrescription = calculateAmount([obj.NursePrescriptionCount], "+");
  const doctorPrescriptionAmount = calculateAmount([obj.DoctorPrescriptionAmount], "+");
  const therapistPrescriptionAmount = calculateAmount([obj.TherapistPrescriptionAmount], "+");
  const nursePrescriptionAmount = calculateAmount([obj.NursePrescriptionAmount], "+");

  try {
    Chart2!.setOption({
      title: {
        text: "方案数据统计",
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        formatter: function (params: any[]) {
          // 获取当前选中的是哪一行（方案数量或方案收入）
          const categoryName = params[0].name;

          // 只保留当前行相关的数据
          const filteredParams = params.filter((param) => {
            // 对于"方案数量"行，只保留xAxisIndex为0的系列
            if (categoryName === "方案数量" && param.seriesIndex < 3) {
              return true;
            }
            // 对于"方案收入"行，只保留xAxisIndex为1的系列
            if (categoryName === "方案收入" && param.seriesIndex >= 3) {
              return true;
            }
            return false;
          });

          // 如果没有数据，返回空
          if (filteredParams.length === 0) {
            return "";
          }

          // 构建提示文本
          let result = `${categoryName}<br/>`;
          let unit = categoryName === "方案数量" ? "次" : "元";
          filteredParams.forEach((param) => {
            // 使用彩色标记和名称
            result += `${param.marker} ${param.seriesName}: ${param.value}${unit}<br/>`;
          });

          return result;
        },
      },
      legend: { left: "right" },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: [
        {
          type: "value",
          min: 0,
          minInterval: 100, // 最小单位为100次
          position: "bottom",
          axisLabel: {
            formatter: "{value}次",
          },
        },
        {
          type: "value",
          min: 0,
          minInterval: 100, // 最小单位为100元
          position: "top",
          axisLabel: {
            formatter: "{value}元",
          },
        },
      ],
      yAxis: {
        type: "category",
        data: ["方案数量", "方案收入"],
        axisPointer: {
          type: "shadow",
        },
      },
      series: [
        // 方案数量系列 - 只使用下方X轴(xAxisIndex: 0)
        {
          name: "医生开方",
          type: "bar",
          label: {
            show: true,
            position: "inside",
          },
          barWidth: 25,
          stack: "图表",
          xAxisIndex: 0,
          data: [doctorPrescription, "-"], // 第一个位置是"方案数量"行
          itemStyle: {
            color: "#52c41a", // 医生颜色
          },
        },
        {
          name: "治疗师开方",
          type: "bar",
          stack: "图表",
          xAxisIndex: 0,
          label: {
            show: true,
            position: "inside",
          },
          data: [therapistPrescription, "-"], // 第一个位置是"方案数量"行
          itemStyle: {
            color: "#faad14", // 治疗师颜色
          },
        },
        {
          name: "护士开方",
          type: "bar",
          stack: "图表",
          xAxisIndex: 0,
          label: {
            show: true,
            position: "inside",
          },
          data: [nursePrescription, "-"], // 第一个位置是"方案数量"行
          itemStyle: {
            color: "#2ed1d1", // 护士颜色
          },
        },
        // 方案收入系列 - 只使用上方X轴(xAxisIndex: 1)
        {
          name: "医生开方",
          type: "bar",
          barWidth: 25,
          stack: "图表",
          xAxisIndex: 1,
          label: {
            show: true,
            position: "inside",
          },
          data: ["-", doctorPrescriptionAmount], // 第二个位置是"方案收入"行
          itemStyle: {
            color: "#52c41a", // 医生颜色
          },
        },
        {
          name: "治疗师开方",
          type: "bar",
          stack: "图表",
          xAxisIndex: 1,
          label: {
            show: true,
            position: "inside",
          },
          data: ["-", therapistPrescriptionAmount], // 第二个位置是"方案收入"行
          itemStyle: {
            color: "#faad14", // 治疗师颜色
          },
        },
        {
          name: "护士开方",
          type: "bar",
          stack: "图表",
          xAxisIndex: 1,
          label: {
            show: true,
            position: "inside",
          },
          data: ["-", nursePrescriptionAmount], // 第二个位置是"方案收入"行
          itemStyle: {
            color: "#2ed1d1", // 护士颜色
          },
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart2:", error);
  }
};

const radioChange = (e: string) => {
  if (e !== "Day") {
    query1.value.pageSize = 999;
  } else {
    query1.value.pageSize = 10;
  }
  query1.value.pageIndex = 1;
};

const handleRefreshData = () => {
  initDataCount();
  initDataCountCharts();
};

const handleGetParams = () => {
  const params = JSON.parse(JSON.stringify(query1.value));
  params.parameters.timeRange.start = dayjs(params.parameters.timeRange.start).format(
    "YYYY-MM-DD 00:00:00"
  );
  params.parameters.timeRange.end = dayjs(params.parameters.timeRange.end).format(
    "YYYY-MM-DD 23:59:59"
  );
  params.parameters.orgId = params.parameters.orgId || "*";
  return params;
};

const initDataCount = async () => {
  const params = handleGetParams();
  const res = await Report_Api.getRedashList<PlatformHomePageStatistics>(params);
  if (res.Type === 200) {
    baseData.value = res.Data.Data[0];
  }
};

const initDataCountCharts = async () => {
  const params = handleGetParams();
  params.queryName = "Report_PlatformHomePageChart";
  const res = await Report_Api.getRedashList<PlatformHomePageChart>(params);
  if (res.Type === 200) {
    if (res.Data.Data.length === 0) return;
    let X: string[] = [];
    let data = [];
    data = res.Data.Data;
    if (res.Data.Data.length < query1.value.pageSize) {
      disabledNextCount.value = true;
    } else {
      disabledNextCount.value = false;
    }
    if (radio.value === "Day") {
      X = res.Data.Data.map((v) => v.Date);
    } else if (radio.value === "Week") {
      var chunks = [];
      for (var i = 0; i < res.Data.Data.length; i += 7) {
        var chunk = res.Data.Data.slice(i, i + 7);
        chunks.push(chunk);
      }
      chunks.forEach((v) => {
        X.push(v[0].Date + "至" + v[v.length - 1].Date);
      });
      data = chunks;
    } else if (radio.value === "Month") {
      const groupedData = res.Data.Data.reduce((result: Record<string, any[]>, item) => {
        const [year, month] = item.Date.split("-");
        const key = `${year}-${month}`;
        if (!result[key]) {
          result[key] = [];
        }
        result[key].push(item);
        return result;
      }, {});
      const groupedArray = Object.values(groupedData);
      groupedArray.forEach((v) => {
        X.push(v[0].Date.split("-")[0] + "-" + v[0].Date.split("-")[1]);
      });
      data = groupedArray;
    }
    initChart3(X, data, radio.value);
    initChart4(X, data, radio.value);
    initChart5(X, data, radio.value);
    initChart6(X, data, radio.value);
  }
};

const initChart3 = (X: string[], List: any[], radio: string) => {
  const [success, newChart] = checkAndInitChart(Chart3, chart3Container);
  if (!success) return;
  if (newChart) Chart3 = newChart;

  let titleText = "普通用户";
  let legendData = ["患者数"];
  if (query1.value.parameters.orgId) {
    titleText = "患者";
    legendData = ["新增获客", "新增患者"];
  } else {
    titleText = "普通用户";
    legendData = ["患者数"];
  }
  let Y = [];
  let HKY = [];
  let HZY = [];
  if (radio === "Day") {
    Y = List.map((v) => v.UserCount);
    HKY = List.map((v) => v.inviteCount);
    HZY = List.map((v) => v.PatientCount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce((total: number, item: any) => total + parseInt(item.UserCount), 0);
      Y.push(sum);
      const sum1 = o.reduce((total: number, item: any) => total + parseInt(item.inviteCount), 0);
      HKY.push(sum1);
      const sum2 = o.reduce((total: number, item: any) => total + parseInt(item.inviteCount), 0);
      HZY.push(sum2);
    });
  }
  const data = {
    title: {
      text: titleText,
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: legendData,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    dataZoom: [
      {
        type: "slider",
        show: true,
        xAxisIndex: [0],
        start: Math.max(0, ((X.length - 20) / X.length) * 100),
        end: 100,
        bottom: 10,
      },
    ],
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: X,
      axisLabel: {
        rotate: 45,
        interval: 0,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
    },
    series: [
      {
        name: "普通患者",
        type: "line",
        data: Y,
      },
    ],
  };
  if (query1.value.parameters.orgId) {
    data.series = [
      {
        name: "新增获客",
        type: "line",
        data: HKY,
      },
      {
        name: "新增患者",
        type: "line",
        data: HZY,
      },
    ];
  } else {
    data.series = [
      {
        name: "普通患者",
        type: "line",
        data: Y,
      },
    ];
  }

  try {
    Chart3!.setOption(data, false, false);
  } catch (error) {
    console.error("Error setting options for Chart3:", error);
  }
};

const initChart4 = (X: string[], List: any[], radio: string) => {
  const [success, newChart] = checkAndInitChart(Chart4, chart4Container);
  if (!success) return;
  if (newChart) Chart4 = newChart;

  let Total = [];
  let WZTotal = [];
  let ZXTotal = [];
  let HLTotal = [];
  if (radio === "Day") {
    Total = List.map((v) => v.VisitCount);
    WZTotal = List.map((v) => v.ConsultCount);
    ZXTotal = List.map((v) => v.TreatmentCount);
    HLTotal = List.map((v) => v.NurseCount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce((total: number, item: any) => total + parseInt(item.VisitCount), 0);
      Total.push(sum);
      const sum1 = o.reduce((total: number, item: any) => total + parseInt(item.ConsultCount), 0);
      WZTotal.push(sum1);
      const sum2 = o.reduce((total: number, item: any) => total + parseInt(item.TreatmentCount), 0);
      ZXTotal.push(sum2);
      const sum3 = o.reduce((total: number, item: any) => total + parseInt(item.NurseCount), 0);
      HLTotal.push(sum3);
    });
  }

  try {
    Chart4!.setOption({
      title: {
        text: "就诊",
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["总数", "问诊", "咨询", "护理"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      dataZoom: [
        {
          type: "slider",
          show: true,
          xAxisIndex: [0],
          start: Math.max(0, ((X.length - 20) / X.length) * 100),
          end: 100,
          bottom: 10,
        },
      ],
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: X,
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
        minInterval: 1,
      },
      series: [
        {
          name: "总数",
          type: "line",
          data: Total,
        },
        {
          name: "问诊",
          type: "line",
          data: WZTotal,
        },
        {
          name: "咨询",
          type: "line",
          data: ZXTotal,
        },
        {
          name: "护理",
          type: "line",
          data: HLTotal,
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart4:", error);
  }
};

const initChart5 = (X: string[], List: any[], radio: string) => {
  const [success, newChart] = checkAndInitChart(Chart5, chart5Container);
  if (!success) return;
  if (newChart) Chart5 = newChart;

  let Total = [];
  let ExecutedTotal = [];
  if (radio === "Day") {
    Total = List.map((v) => v.PrescriptionCount);
    ExecutedTotal = List.map((v) => v.ExecutePrescriptionCount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce(
        (total: number, item: any) => total + parseInt(item.PrescriptionCount),
        0
      );
      Total.push(sum);
      const sum1 = o.reduce(
        (total: number, item: any) => total + parseInt(item.ExecutePrescriptionCount),
        0
      );
      ExecutedTotal.push(sum1);
    });
  }

  try {
    Chart5!.setOption({
      title: {
        text: "方案数量",
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["总数", "已执行"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      dataZoom: [
        {
          type: "slider",
          show: true,
          xAxisIndex: [0],
          start: Math.max(0, ((X.length - 20) / X.length) * 100),
          end: 100,
          bottom: 10,
        },
      ],
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: X,
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
        minInterval: 1,
      },
      series: [
        {
          name: "总数",
          type: "line",
          data: Total,
        },
        {
          name: "已执行",
          type: "line",
          data: ExecutedTotal,
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart5:", error);
  }
};

const initChart6 = (X: string[], List: any[], radio: string) => {
  const [success, newChart] = checkAndInitChart(Chart6, chart6Container);
  if (!success) return;
  if (newChart) Chart6 = newChart;

  let WZ = [];
  let ZX = [];
  let ZL = [];
  if (radio === "Day") {
    WZ = List.map((v) => v.RegisterDoctorConsultAmount);
    ZX = List.map((v) => v.RegisterTherapistConsultAmount);
    ZL = List.map((v) => v.TreatmentAmount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce(
        (total: number, item: any) => total + parseInt(item.RegisterDoctorConsultAmount),
        0
      );
      WZ.push(sum);
      const sum1 = o.reduce(
        (total: number, item: any) => total + parseInt(item.RegisterTherapistConsultAmount),
        0
      );
      ZX.push(sum1);
      const sum2 = o.reduce(
        (total: number, item: any) => total + parseInt(item.TreatmentAmount),
        0
      );
      ZL.push(sum2);
    });
  }

  try {
    Chart6!.setOption({
      title: {
        text: "收入",
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["问诊", "咨询", "治疗"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      dataZoom: [
        {
          type: "slider",
          show: true,
          xAxisIndex: [0],
          start: Math.max(0, ((X.length - 20) / X.length) * 100),
          end: 100,
          bottom: 10,
        },
      ],
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: X,
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          name: "问诊",
          type: "line",
          data: WZ,
        },
        {
          name: "咨询",
          type: "line",
          data: ZX,
        },
        {
          name: "治疗",
          type: "line",
          data: ZL,
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart6:", error);
  }
};

onMounted(async () => {
  await nextTick();
  initCharts();
  await handleGetTopDataInfo();
  await handleRefreshData();

  // 使用防抖处理的 resize 事件监听器，合并了两个 resize 处理函数
  const handleResizeWithDebounce = debounce(() => {
    resizeAllCharts();
  }, 100);

  window.addEventListener("resize", handleResizeWithDebounce);
});

// 修改resizeAllCharts函数以正确处理非响应式变量
const resizeAllCharts = () => {
  // 调整已存在的图表大小
  resizeSingleChart(Chart1, "Chart1");
  resizeSingleChart(Chart2, "Chart2");
  resizeSingleChart(Chart3, "Chart3");
  resizeSingleChart(Chart4, "Chart4");
  resizeSingleChart(Chart5, "Chart5");
  resizeSingleChart(Chart6, "Chart6");

  // 对于尚未初始化的图表，检查容器并初始化
  nextTick(() => {
    // 初始化尚未创建的图表
    if (!Chart1 && Object.keys(info.value).length > 0) {
      Chart1 = initSingleChart(Chart1, chart1Container, "Chart1");
      if (Chart1) initChart1(info.value);
    }

    if (!Chart2 && Object.keys(info.value).length > 0) {
      Chart2 = initSingleChart(Chart2, chart2Container, "Chart2");
      if (Chart2) initChart2(info.value);
    }

    Chart3 = initSingleChart(Chart3, chart3Container, "Chart3");
    Chart4 = initSingleChart(Chart4, chart4Container, "Chart4");
    Chart5 = initSingleChart(Chart5, chart5Container, "Chart5");
    Chart6 = initSingleChart(Chart6, chart6Container, "Chart6");

    // 如果已有数据，重绘底部图表
    if (Object.keys(baseData.value).length > 0) {
      initDataCountCharts();
    }
  });
};

// 辅助函数用于调整单个图表大小
const resizeSingleChart = (chart: EChartsType | undefined, chartName: string) => {
  if (chart) {
    try {
      chart.resize({
        animation: { duration: 300 },
      });
    } catch (error) {
      console.error(`Error resizing ${chartName}:`, error);
    }
  }
};

onUnmounted(() => {
  // 组件卸载时清理所有图表实例
  if (Chart1) Chart1.dispose();
  if (Chart2) Chart2.dispose();
  if (Chart3) Chart3.dispose();
  if (Chart4) Chart4.dispose();
  if (Chart5) Chart5.dispose();
  if (Chart6) Chart6.dispose();

  // 确保移除所有事件监听器，避免内存泄漏
  window.removeEventListener("resize", resizeAllCharts);

  // 清空引用
  Chart1 = undefined;
  Chart2 = undefined;
  Chart3 = undefined;
  Chart4 = undefined;
  Chart5 = undefined;
  Chart6 = undefined;
});
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .overview-section {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;

    .overview-cards {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .overview-card {
      background: white;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      align-items: center;
      gap: 12px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;
      border: 1px solid rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      .card-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        overflow: hidden;
        padding: 10px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      &:nth-child(1) .card-icon {
        background: #1890ff;
      }

      &:nth-child(2) .card-icon {
        background: #52c41a;
      }

      &:nth-child(3) .card-icon {
        background: #faad14;
      }

      &:nth-child(4) .card-icon {
        background: #2ed1d1;
      }

      &:nth-child(5) .card-icon {
        background: #ff4d4f;
      }

      .card-content {
        flex: 1;
        min-width: 0;

        .card-value {
          font-size: 20px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 2px;
          line-height: 1.2;

          .number {
            color: #31ccb8;
          }
        }

        .card-label {
          font-size: 13px;
          color: #666;
          line-height: 1.2;
        }
      }
    }
  }

  .charts-todo-section {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    .charts-section {
      flex: 1;
      min-width: 0;

      .chart-row {
        display: flex;
        justify-content: space-between;
      }
    }

    .todo-section {
      width: 320px;
      flex-shrink: 0;

      .todo-card {
        background: white;
        border-radius: 8px;
        padding: 0 16px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        height: 100%;
        display: flex;
        flex-direction: column;

        .todo-header {
          border-bottom: 1px solid #f0f0f0;

          h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            margin: 10px 0 !important;
          }
        }

        .todo-content {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
          padding: 8px 0;
          flex: 1;
          .todo-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            text-align: center;
            border-radius: 6px;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            &:hover {
              background: #f0f7ff;
              color: #409eff;
            }

            .todo-label {
              margin-bottom: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 16px;
            }

            .todo-count {
              color: #ff4d4f;
              font-weight: 500;
              background: #fff1f0;
              padding: 2px 8px;
              border-radius: 10px;
              font-size: 12px;
              min-width: 24px;
              text-align: center;
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .stats-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .stats-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #1a1a1a;
      }

      .stats-filters {
        display: flex;
        gap: 16px;
        align-items: center;

        .date-picker {
          width: 160px;
        }

        .confirm-btn {
          margin-left: 16px;
        }
      }
    }

    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;

      .stat-card {
        border-radius: 8px;
        padding: 16px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:nth-child(1) {
          background: linear-gradient(135deg, #e6f7ff 0%, #f0f7ff 100%);
          border: 1px solid rgba(24, 144, 255, 0.1);
        }

        &:nth-child(2) {
          background: linear-gradient(135deg, #f6ffed 0%, #f0fff0 100%);
          border: 1px solid rgba(82, 196, 26, 0.1);
        }

        &:nth-child(3) {
          background: linear-gradient(135deg, #fff7e6 0%, #fff9f0 100%);
          border: 1px solid rgba(250, 173, 20, 0.1);
        }

        &:nth-child(4) {
          background: linear-gradient(135deg, #f9f0ff 0%, #f5f0ff 100%);
          border: 1px solid rgba(114, 46, 209, 0.1);
        }

        &:nth-child(5) {
          background: linear-gradient(135deg, #fff1f0 0%, #fff5f5 100%);
          border: 1px solid rgba(255, 77, 79, 0.1);
        }

        &:nth-child(6) {
          background: linear-gradient(135deg, #f0f5ff 0%, #f5f7ff 100%);
          border: 1px solid rgba(47, 84, 235, 0.1);
        }

        &:nth-child(7) {
          background: linear-gradient(135deg, #e6fffb 0%, #f0ffff 100%);
          border: 1px solid rgba(24, 144, 255, 0.1);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .stat-card-header {
          position: relative;
          z-index: 1;
          text-align: center;

          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
          }
        }

        .stat-card-content {
          position: relative;
          z-index: 1;
          text-align: center;

          .stat-row {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 4px 0;

            &:last-child {
              margin-bottom: 0;
            }

            .stat-label {
              color: #666;
              font-size: 14px;
            }

            .stat-value {
              font-weight: 500;
              color: #1a1a1a;
              font-size: 14px;
              margin-left: 12px;
            }

            // 总数行样式
            &.total-row {
              border-radius: 8px;
              justify-content: center;
              flex-direction: column;

              .stat-label {
                color: #333;
                font-weight: 600;
                font-size: 20px;
              }

              .stat-value {
                font-weight: 800;
                color: #1a1a1a;
                font-size: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                letter-spacing: 0.5px;
                line-height: 1.2;
              }
            }
          }
        }

        &::before {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          width: 100px;
          height: 100px;
          background: radial-gradient(
            circle at top right,
            rgba(255, 255, 255, 0.8),
            transparent 70%
          );
          opacity: 0.5;
        }
      }
    }
  }

  .bottom-charts-section {
    .chart-grid {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
  }

  .chart-card {
    width: 48%;
    background: white;
    border-radius: 12px;
    padding: 24px;
    height: 500px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
  }
  .chart-card-top {
    height: 300px;
    margin-bottom: 0 !important;
  }
}
</style>
