export interface GetVisitsByUserIdParams {
  dateSerachType?: number;
  type?: number;
  userId: string;
  pageIndex: number;
  pageSize: number;
  organizationId?: string;
}

export interface UserArchive {
  Id?: string;
  UserId?: string;
  Address?: string;
  ContactPerson?: string;
  ContactPhone?: string;
  ContactRelation?: string;
  Height?: string;
  Weight?: string;
  Blood?: string;
  Marital?: string;
  Give?: string;
  Abstract?: string;
  IsDisability?: boolean;
  DisabilityType?: string;
  DisabilityLevel?: string;

  /**
   * 过敏史，JSON解析后是 TagsItem 类型
   */
  PreviousHistory?: string;
  /**
   * 过敏史，JSON解析后是 TagsItem 类型
   */
  AllergicHistory?: string;
  FamilyHistory?: string;
  DietaryHabit?: string;
  DietaryHabit1?: string;
  Smoking?: string;
  StartSmokingAge?: string;
  QuitSmokingAge?: string;
  DaySmoking?: string;
  Drink?: string;
  DayDrink?: string;
  IsAbstinence?: boolean;
  AbstinenceAge?: string;
  StartDrinkAge?: string;
  IsYearIntemperance?: boolean;
  WineType?: string;
  CreatedTime?: string;
  Name?: string;
  HeadImg?: string;
  Sex?: string;
  Birthday?: string;
  Age?: string;
  IdCard?: string;
  Nation?: string;
  NativePlace?: string;
  Education?: string;
  WorkState?: string;
  EconomicIncome?: string;
  Professional?: string;
  WorkUnit?: string;
}
