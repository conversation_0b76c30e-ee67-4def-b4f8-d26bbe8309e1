import request from "@/utils/request";
import { type InviterAddOrUpdateInput, type InviterDTO } from "./types";

const IDENTITY = "/identity/api/v1";
const IDENTITY_INVITER = `${IDENTITY}/Inviter`;

/** 获取全部机构推广码 */
const getInviterList = (params: PageParams) => {
  return request.get<ListDataTotalCount<InviterDTO>>(`${IDENTITY_INVITER}/GetList`, {
    params,
  });
};

/** 添加或者编辑机构推广码 */
export function addOrUpdateInviter(data: InviterAddOrUpdateInput, type: "Add" | "Update") {
  return request.post(`${IDENTITY_INVITER}/${type}`, data);
}

const Identity_Api = {
  getInviterList,
  addOrUpdateInviter,
};

export default Identity_Api;
