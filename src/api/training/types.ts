import { type EpPropMergeTypeWithNull } from "element-plus";

export interface GetEvaluateGaugeInputDTO {
  IsEnble?: boolean;
  Keyword?: string;
  OrganizationId?: string;
  PageIndex: number;
  PageSize: number;
  /** 0: 评定 1: 随访 */
  Type?: number;
  IsDefaultPush?: boolean;
  /** 康复分类 */
  DictType?: string;
  /** 疾病id */
  DiseaId?: string;
}

export interface TrainingListInputDTO {
  OrgId: EpPropMergeTypeWithNull<string[]>;
  StartTime: string;
  EndTime: string;
  DoctorId: EpPropMergeTypeWithNull<string>;
  TherapistId: EpPropMergeTypeWithNull<string>;
  NurseId: EpPropMergeTypeWithNull<string>;
  State: EpPropMergeTypeWithNull<number>;
  KeyWords: string;
  PageIndex: number;
  PageSize: number;
  IsTest: EpPropMergeTypeWithNull<number>;
  AliasName: EpPropMergeTypeWithNull<string[]>;
}
export interface TrainingScheme {
  Id: string;
  Name: string;
  StartTime: string;
  ActualFinishedTime: string;
  PerformDays: number;
  FinishedTime: string;
  OrganizationId: string;
  OrganizationName: string;
  DoctorName: string;
  TherapistName?: string;
  AssistantName?: string;
  NurseName?: string;
  VisitNo: string;
  ActionName: string;
  State: number;
  PatientId: string;
  PatientName: string;
  PatientPhone: string;
  Age: string;
  Sex: string;
  FirstDayRate: number;
  IsTest: number;
  FinishCount: number;
  ShouldCount: number;
  FinishRate: number;
  DiagnosisName: string;
  CreatorId: string;
  RoomId: string;
}
export interface TrainingProgram {
  Id: string;
  TrainingActionId: string;
  Name: string;
  Source: number;
  StartTime: string; // 可以考虑使用 Date 类型
  Remark: string;
  State: number;
  PerformDays: number;
  FinishedTime: string; // 可以考虑使用 Date 类型
  ActualFinishedTime: string; // 可以考虑使用 Date 类型
  VisitId: string;
  PatientId: string;
  OrganizationId: string;
  DeptId: string;
  DeptName: string;
  CreatorId: string;
  CreatorName: string;
  CreatedTime: string; // 可以考虑使用 Date 类型
  FinishCount: number;
  ShouldCount: number;
  FinishRate: number;
  TodayFinishCount: number;
  TodayShouldCount: number;
  OrganizationName: string;
  RemainingLife: number;
  ClockInOnTodayDaily: boolean;
  AdjustSourceId?: string;
  IsAdjustStop: boolean;
  LastUpdateId?: string;
  LastUpdatedTime: string; // 可以考虑使用 Date 类型
  LastUpdateName?: string;
  DiagnosisCode: string;
  RoomId: string;
  NotFinishedDay: number;
  IsPay: boolean;
  ContinuePrescriptionState: number;
  Group: number;
  ActionUnitImgURL: string | null; // 允许为 null
  TreatType: number;
  IsHaveConsultServer: boolean;
  LastMessageSendTime: string; // 可以考虑使用 Date 类型
  IsTest: boolean;
  IsExistCommunity: boolean;
  TrainingActions: TrainingAction[];
  TrainingHomeDoctorTeams: TrainingHomeDoctorTeam[];
  TrainingActionAnswers?: unknown[]; // 根据实际情况定义
  TrainingMoItems: TrainingMoItem[];
  AssistUserInfos?: unknown[]; // 根据实际情况定义
  CommunityMoItems?: unknown;
  Assist?: unknown;
}
export interface TrainingAction {
  Id: string;
  TrainingProgramId: string;
  ContentId: string;
  Name: string;
  Code: string;
  PinyinCode: string;
  Enable: boolean;
  Type: number;
  Freq: number;
  GroupCount: number;
  EachGroupCount: number;
  Duration?: number;
  DurationUnit?: number;
  Instrument?: string;
  MediaType: number;
  Media: { Url: string }[];
  ActionUnitImgURL: string;
  ActionInfo: string;
  Notes: string;
  FinishCount: number;
  TodayFinishCount: number;
  ShouldCount: number;
  TodayShouldCount: number;
  FinishRate: number;
  IsReportReturn: number;
  InstrumentParameter: string | null; // 允许为 null
  MoItemId: string;
  MoItemName: string;
  ActionTime?: string;
  ActionStrength?: string;
  PackId: string;
  Manufacturer?: string;
  MFType?: string;
  Sort: number;
  Group: string;
  TrainingActionDetailPlans: TrainingActionDetailPlan[];
  TrainingActionExecutes: unknown[]; // 根据实际情况定义
  TrainingActionDates: unknown[]; // 根据实际情况定义
}
export interface ActionExecute {
  Id: string; // 动作细节 ID
  TrainingActionid: string; // 训练动作 ID
  Remark: string; // 备注，允许为 null
  ZhRemark: string | null; // 中文备注，允许为 null
  Media: string | null; // 媒体，允许为 null，具体类型根据实际情况定义
  CreatorId: string; // 创建者 ID
  CreatorName: string | null; // 创建者姓名，允许为 null
  CreatedTime: string; // 创建时间
}
export interface TrainingActionDetailPlan {
  Id: string;
  TrainingActionId: string;
  Freq: number;
  GroupCount: number;
  EachGroupCount: number;
  Duration?: number;
  DurationUnit?: number;
  Week: number;
}
export interface TrainingHomeDoctorTeam {
  Id: string;
  VisitId: string;
  ProgramId: string;
  HomeDoctorId: string;
  HomeDoctorName: string;
  IsLook: boolean;
  CreatedTime: string; // 可以考虑使用 Date 类型
  Version: number;
  Role: string;
  DeletedTime?: string;
  PhoneNumber?: string;
  WorkerTitle?: string;
  WorkerType: string;
}
export interface TrainingMoItem {
  Id: string;
  ProgramId: string;
  MoItemId: string;
  MoItemName: string;
  MoItemMethod: number;
  CreatedTime: string; // 可以考虑使用 Date 类型
  FreqDay: number;
  IsSpecialFreq: boolean;
  Freq: number;
  Day: number;
  TotalCount: number;
  BaseInfo: string; // JSON 字符串
  PackId: string;
  TrainingMoItemDetails: TrainingMoItemDetail[];
  RecoveryMissionRelations?: unknown;
}
export interface TrainingMoItemDetail {
  Id: string;
  TrainingMoItemId: string;
  RlationId: string;
  Name: string;
  Count: number;
  DeletedTime?: string;
  CreatedTime: string; // 可以考虑使用 Date 类型
  BaseInfo?: string;
}
export interface PatientInfoByProgramId {
  UserInfo?: BaseUserProfile;
  VisitInfo?: VisitInfo | null;
  PatGauge: PatGauge[] | null;
  TrainingProgram: TrainingProgram | null;
}
export interface PatGauge {
  Name: string;
  EvaluateTime: string;
  SumPoint: number;
  Remark?: string;
  EvaluateGaugeId: string;
  BaseEvaluateGaugeId: string;
  IsEvaluate: boolean;
  SuccessEvaluateContent: string;
  RelatedId: string;
  DctSendSign: string;
  Source: number;
}
export interface VisitInfo {
  Id: string; // 患者记录 ID
  MisId: string | null; // 允许为 null
  PatientId: string; // 患者 ID
  OthersId: string | null; // 允许为 null
  Name: string; // 患者姓名
  Age: string; // 患者年龄
  Sex: string; // 患者性别
  VisitNo: string; // 就诊号
  Source: string; // 来源
  DepartmentName: string; // 科室名称
  BedNo: string | null; // 允许为 null
  DoctorName: string; // 医生姓名
  DoctorId: string; // 医生 ID
  InDate: string; // 入院日期
  OutDate: string | null; // 允许为 null
  PatientNo: string | null; // 允许为 null
  CardNumber: string | null; // 允许为 null
  OrganizationId: string | null; // 允许为 null
  OrganizationName: string | null; // 允许为 null
  Days: number; // 住院天数
  PrePayment: number; // 预付款
  AllCost: number; // 总费用
  Balance: number; // 余额
  GlobalId: string | null; // 允许为 null
  DoctorGlobalId: string | null; // 允许为 null
  TreatmentProgres: string | null; // 允许为 null
  WorkerTitle: string; // 医生职称
  Describing: string; // 症状描述
  OfflineDate: string; // 离线日期
  Complain: string; // 主诉
  PresentIllness: string; // 现病史
  HistoryIllness: string; // 既往史
  CostState: number; // 费用状态
  RxUrl: string | null; // 允许为 null
  ConsultRoomId: string | null; // 允许为 null
  ConsultWay: number; // 咨询方式
  Disposal: string; // 处理方案
  AuxiliaryDiagnosis: string; // 辅助诊断
  Urls: string[]; // 相关 URL 数组
  Assists: unknown[]; // 辅助信息数组，根据实际情况定义
  DoctorTitle: string | null; // 允许为 null
  Diagnoses: BaseDiagnosis[]; // 诊断数组
  LatestVisitedTime: string | null; // 允许为 null
}
export interface RoomMsgByProgram {
  Msg?: RoomMsg[];
  RoomInfo?: RoomInfo;
  Users: BaseUserProfile[];
}
export interface TrainingActionDetailByDate {
  Time: string; // 方案时间
  Name: string; // 方案名称
  TrainingActionDetailByDateType0s: TrainingActionDetailByDateType[]; // 类型 0 的训练动作细节
  TrainingActionDetailByDateType1s: TrainingActionDetailByDateType[]; // 类型 1 的训练动作细节
}
export interface TrainingActionDetailByDateType {
  Id: string; // 动作 ID
  Name: string; // 动作名称
  MediaType: number; // 媒体类型
  TrainingActionDetailId: string; // 训练动作细节 ID
  ActionUnitImgURL: string; // 动作单位图片 URL
  Media: string[]; // 媒体数组
  Manufacturer: number; // 制造商
  MFType: number; // 制造商类型
  ActionDetailID: string; // 动作细节 ID
  TrainingActionDetailPlan: TrainingActionDetailPlan; // 训练动作细节计划
  TrainingActionDetailDate: string; // 训练动作细节日期，允许为 null
  TrainingActionDetailExecutes: ActionExecute[] | null;
}

export interface BindingDeviceInputDTO {
  DeviceCode: string;
  DeviceTypeCode: string;
  DeviceFactory: string;
  /** 操作人 */
  UserId: string;
  /** 绑定人 */
  MenberId: string;
  UserName?: string;
  Birthday?: string;
  Sex?: string;
}
