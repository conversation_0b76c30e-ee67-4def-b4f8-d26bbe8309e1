import request from "@/utils/request";
import { type CreateRecoveryDeviceInputDTO, type OutGoodsItem } from "./types";

const Supplier_Jiandao_Info = "/supplier/jiandao/Info";
const Supplier_Jiandao_Api = {
  // ------------ Supplier_Jiandao_Info ------------
  checkComplete(params: { orderNo: string }): Promise<ServerResult<boolean>> {
    return request.get(`${Supplier_Jiandao_Info}/CheckComplete`, { params });
  },
  /** 根据订单号获取发货单信息 */
  getOutGoodsByOrderNo(params: { orderNo: string }): Promise<ServerResult<OutGoodsItem>> {
    return request.get(`${Supplier_Jiandao_Info}/GetOutGoodsByOrderNo`, { params });
  },
  /** 获取回收仓 */
  getWarehouses(): Promise<ServerResult<WarehouseItem[]>> {
    return request.get(`${Supplier_Jiandao_Info}/GetWarehouses`);
  },
  /** 创建回收单 */
  createRecoveryDeviceForm(data: CreateRecoveryDeviceInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Supplier_Jiandao_Info}/CreateRecoveryDeviceForm`, data);
  },
};

export default Supplier_Jiandao_Api;
