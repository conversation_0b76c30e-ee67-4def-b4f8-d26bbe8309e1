export interface OutGoodsItem {
  Id: string;
  OrderNo: string;
  UserName: string;
  Phone: number;
  Org: string;
  CreatorName: string;
  Diagnosis: string;
  Instrument: string;
  TakeDelivery: string;
  InstrumentParameter: string;
  Consumables: string;
  ConsigneeName: string;
  ConsigneePhone: number;
  ConsigneeAddress: string;
  TaotalAmount: number;
  Market: string;
  UseDay: number;
  DeliveryMethod: string;
  Remark: string;
  DeviceDetails: {
    DeviceType: string;
    DeviceCode: string;
  }[];
  ConsumablesDetails: any[];
  TrackingNumber: string;
  AppId: string;
  EntryId: string;
  ReceivedTime?: any;
  Shipment?: any;
  FlowState: number;
}
export interface CreateRecoveryDeviceInputDTO {
  OrderNo: string;
  UserName: string;
  TakeDelivery: string;
  Market: string;
  DeviceTypeName: string;
  DeviceTypeId: string;
  Note: string;
  RecyclingTime: string;
  PickUpMethod: string;
  ReturnToWarehouse: string;
  WarehouseProbablyAddress: string;
  WarehouseDetailAddress: string;
  Remark: string;
  DeviceId: string;
  PrescriptionId: string;
  Org: string;
  RecyclingAddress: string;
  OrderDetailId: string;
  DeviceDetails: {
    DeviceType: string;
    DeviceCode: string;
  }[];
}
