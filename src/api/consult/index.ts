import request from "@/utils/request";
import {
  type PlatformRxTempItem,
  type PlatformRxTempInputDTO,
  type GetDocsInputDTO,
  type GetPrescriptonExecutInputDTO,
  type PrescriptonExecut,
  type SaveQRCodeInputDTO,
  type RxTemplateInputDTO,
  type RxTemplateDetail,
  type DoctorInfo,
  type GetOrderManageInputDTO,
  type TherapyOrder,
  type TreatOrderDetail,
  type PrescriptionDetail,
  type ReturnVisitInputDTO,
  type ReturnVisitItem,
  type GetCardOrdersListParams,
  type SetSettlementInputDTO,
  type GetCardsListParams,
  type DeviceManageInputDTO,
  type DeviceManageItem,
  type OrderDeviceBizStorageInputDTO,
  type ReturnOrderDetailInputDTO,
  type GetFollowUpPlanPageParams,
  type PlatformAddFollowUpPlanParams,
  type GetFollowUpPlanDetailPageParams,
  type GetConsultRecordsInputDTO,
} from "./types";
import { type RoomMsgByProgram } from "../training/types";

const Consult_Prescription = "/consult/api/prescription";
const Consult_Consult = "/consult/api/consult";
const Consult_Aggregate = "/consult/api/aggregate";
const Consult_Doctor = "/consult/api/doctor";
const Consult_ReturnVisit = "/consult/api/returnVisit";
const Consult_Card = "/consult/api/card";
const Consult_FollowUp = "/consult/api/followUp";
const Consult_Supervision = "/consult/api/Supervision";

const Consult_Api = {
  // ---------------- Consult_Prescription ----------------

  /** 获取常用方案 */
  getPlatformRxTemp(
    data: PlatformRxTempInputDTO
  ): Promise<ServerResult<ListDataTotalCount<PlatformRxTempItem>>> {
    return request.post(`${Consult_Prescription}/GetPlatformRxTemp`, data);
  },
  /** 编辑常用方案 */
  updateRxTemplate(data: RxTemplateInputDTO): Promise<ServerResult<string>> {
    return request.post(`${Consult_Prescription}/UpdateRxTemplate`, data);
  },
  /** 新增常用方案 */
  saveRxTemplate(data: RxTemplateInputDTO): Promise<ServerResult<string>> {
    return request.post(`${Consult_Prescription}/SavRxTemplate`, data);
  },
  /** 删除常用方案 */
  deleteRxTemplate(data: { TemplateId: string }): Promise<ServerResult<null>> {
    return request.post(`${Consult_Prescription}/DeleteRxTemplate`, data);
  },
  /** 获取常用方案详情 */
  getRxTemplateById(params: { templateId: string }): Promise<ServerResult<RxTemplateDetail>> {
    return request.get(`${Consult_Prescription}/GetRxTemplateById`, { params });
  },
  /** 批量更新常用方案启用状态 */
  batchOperationRxTemp(data: { Ids: string[]; IsEnable: boolean }): Promise<ServerResult<null>> {
    return request.post(`${Consult_Prescription}/BatchOperationRxTemp`, data);
  },
  /** 保存二维码 */
  saveQRCode(data: SaveQRCodeInputDTO): Promise<ServerResult<string>> {
    return request.post(`${Consult_Prescription}/SaveQRCode`, data);
  },
  /** 更新常用方案启用状态 */
  updateRxTemplateEnable(data: { Id: string; IsEnable: boolean }): Promise<ServerResult<null>> {
    return request.post(`${Consult_Prescription}/UpdateRxTemplateEnable`, data);
  },
  /** 修改治疗方案查询的是否自主开方 */
  batchOperaPre(data: { PreIds: string[]; SelfReliance: boolean }): Promise<ServerResult<null>> {
    return request.post(`${Consult_Prescription}/BatchOperaPre`, data);
  },

  /** 获取治疗方案查询 */
  getPrescriptonExecut(
    data: GetPrescriptonExecutInputDTO
  ): Promise<ServerResult<ListDataTotalCount<PrescriptonExecut>>> {
    return request.post(`${Consult_Prescription}/GetPrescriptonExecut`, data);
  },
  /** 设置治疗订单备注 */
  setOrderGift(data: {
    PrescriptionId: string;
    Gift: string;
    Mark: string;
  }): Promise<ServerResult<null>> {
    return request.post(`${Consult_Prescription}/SetOrderGift`, data);
  },
  /** 获取治疗订单列表数据 */
  getOrderManage(
    data: GetOrderManageInputDTO
  ): Promise<ServerResult<ListDataTotalCount<TherapyOrder>>> {
    return request.post(`${Consult_Prescription}/GetOrderManage`, data);
  },
  /** 获取治疗订单详情 */
  getTreatOrderInfo(params: { prescriptionId: string }): Promise<ServerResult<TreatOrderDetail>> {
    return request.get(`${Consult_Prescription}/GetTreatOrderInfo`, { params });
  },
  /** 获取处方详情 */
  getPrescriptionInfo(params: {
    PrescriptionId: string;
  }): Promise<ServerResult<PrescriptionDetail>> {
    return request.get(`${Consult_Prescription}/GetPrescriptionInfo`, { params });
  },
  /** 执行订单 */
  toExecute(data: {
    PrescriptionId: string;
    PayCompany: string;
    TreatPayCompany: string;
    IsChecked: boolean;
  }): Promise<ServerResult<null>> {
    return request.post(`${Consult_Prescription}/ToExecute`, data);
  },
  /**
   * 修改医生的结算比例数据
   */
  setSettlements(data: SetSettlementInputDTO): Promise<ServerResult<number>> {
    return request.post(`${Consult_Prescription}/SetSettlements`, data);
  },
  /** 获取设备押金列表 */
  deviceManage(
    data: DeviceManageInputDTO
  ): Promise<ServerResult<ListDataTotalCount<DeviceManageItem>>> {
    return request.post(`${Consult_Prescription}/DeviceManage`, data);
  },
  // ---------------- Consult_Consult ----------------

  /** 获取医生列表 */
  getDocs(data: GetDocsInputDTO): Promise<ServerResult<ListRowsTotal<BaseDoctorItem>>> {
    return request.post(`${Consult_Consult}/GetDocs`, data);
  },
  /** 修改医生的是否推荐  是否自运行 */
  setUserConsult(data: {
    UserId: string;
    Organization: string;
    OnlyFillNotNull: number;
    Recommend?: boolean;
    SelfReliance?: boolean;
  }): Promise<ServerResult<null>> {
    return request.post(`${Consult_Consult}/SetUserConsult`, data);
  },
  /** 设备入库 */
  setOrderDeviceBizStorage(data: OrderDeviceBizStorageInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Consult_Consult}/SetOrderDeviceBizStorage`, data);
  },
  /** 退还设备--根据订单明细 */
  returnByOrderDetail(data: ReturnOrderDetailInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Consult_Consult}/ReturnByOrderDetail`, data);
  },

  /** 获取就诊记录列表 */
  getConsultRecords(
    data: GetConsultRecordsInputDTO
  ): Promise<ServerResult<ListRowsTotal<ConsultRecord>>> {
    return request.post(`${Consult_Consult}/GetConsultRecords`, data);
  },

  /** 获取就诊记录详情 */
  getConsultRecordInfo(consultId: string): Promise<ServerResult<ConsultRecordInfo>> {
    return request.get(`${Consult_Consult}/GetConsultRecordInfo`, { params: { id: consultId } });
  },

  // ---------------- Consult_Aggregate ----------------

  /** 修改是否测试数据 */
  switchTestStatus(data: string[]): Promise<ServerResult<null>> {
    return request.post(`${Consult_Aggregate}/SwitchTestStatus`, data);
  },

  // ---------------- Consult_Doctor ----------------

  /** 获取医生详情 */
  getDoctorInfo(data: { DoctorId: string }): Promise<ServerResult<DoctorInfo>> {
    return request.post(`${Consult_Doctor}/GetDoctorInfo`, data);
  },
  /** 修改医生详情 */
  setDoctorInfo(data: DoctorInfo): Promise<ServerResult<null>> {
    return request.post(`${Consult_Doctor}/SetDoctorInfo`, data);
  },

  // ---------------- Consult_ReturnVisit ----------------

  /** 获取回访列表 */
  getPreReturnVisitPage(
    data: ReturnVisitInputDTO
  ): Promise<ServerResult<ListDataTotalCount<ReturnVisitItem>>> {
    return request.post(`${Consult_ReturnVisit}/GetPreReturnVisitPage`, data);
  },

  /** 获取回访列表 */
  getReturnVisitRecords(
    data: ReturnVisitInputDTO
  ): Promise<ServerResult<ListDataTotal<ReturnVisitItem>>> {
    return request.post(`${Consult_ReturnVisit}/GetReturnVisitRecords`, data);
  },

  getVisitRecordStatistics<T>(data: any): Promise<ServerResult<T>> {
    return request.post(`${Consult_ReturnVisit}/GetVisitRecordStatistics`, data);
  },
  // ---------------- Consult_Card ----------------

  /** 获取台卡订单列表 */
  getCardOrdersList(
    params: GetCardOrdersListParams
  ): Promise<ServerResult<ListDataTotalCount<TableCardDetail>>> {
    const path = `${Consult_Card}/GetCardOrdersList`;
    return request.post(path, params);
  },

  /** 获取台卡列表 */
  getCardsList(
    params: GetCardsListParams
  ): Promise<ServerResult<ListDataTotalCount<TableCardDetail>>> {
    const path = `${Consult_Card}/GetCardsList`;
    return request.post(path, params);
  },

  // ---------------- Consult_FollowUp ----------------

  /** 获取随访列表 */
  getFollowUpPlanPage(
    params: GetFollowUpPlanPageParams
  ): Promise<ServerResult<ListDataTotalCount<FollowUpPlan>>> {
    return request.post(`${Consult_FollowUp}/GetFollowUpPlanPage`, params);
  },

  /** 获取随访明细 */
  getFollowUpPlanDetail(planId: string): Promise<ServerResult<FollowUpPlan>> {
    return request.get(`${Consult_FollowUp}/GetFollowUpPlanDetail`, { params: { planId } });
  },

  /** 添加随访计划 */
  platformAddFollowUpPlan(params: PlatformAddFollowUpPlanParams): Promise<ServerResult> {
    return request.post(`${Consult_FollowUp}/PlatformAddFollowUpPlan`, params);
  },

  /** 获取随访结果列表 */
  getFollowUpPlanDetailPage(
    params: GetFollowUpPlanDetailPageParams
  ): Promise<ServerResult<ListDataTotalCount<FollowUpPlan>>> {
    return request.post(`${Consult_FollowUp}/GetFollowUpPlanDetailPage`, params);
  },

  // ---------------- Consult_Supervision ----------------

  /** 根据问诊id获取会话消息 */
  getRoomMsgs(consultId: string): Promise<ServerResult<RoomMsgByProgram>> {
    return request.get(`${Consult_Supervision}/GetRoomMsgs`, { params: { id: consultId } });
  },
};

export default Consult_Api;
