import { type EpPropMergeTypeWithNull } from "element-plus";
import { type MoItemAction, type PackScaleItem } from "../content/types";

export interface PlatformRxTempInputDTO {
  OrgIds: EpPropMergeTypeWithNull<string[]>;
  DeptIds: EpPropMergeTypeWithNull<string[]>;
  CreatorIds: EpPropMergeTypeWithNull<string[]>;
  MoItemName: EpPropMergeTypeWithNull<string>;
  PageIndex: number;
  PageSize: number;
  KeyWord: string;
  IsEnable: EpPropMergeTypeWithNull<boolean>;
  TreatType: EpPropMergeTypeWithNull<number>;
  Scopeable: number;
}
export interface PlatformRxTempItem {
  Id: string;
  PrescriptionName: string;
  SendTime: string; // ISO 8601 格式的日期字符串
  MoNames: string;
  MaxDay: number;
  ExecutDay: number;
  DoctorId: string;
  DoctorName: string;
  CreatorDptName: string;
  CreatorWorkTitle: string;
  TherapistId?: string;
  TherapistName?: string;
  TherapistRemark?: string;
  TotalAmount: number;
  Type: number;
  TreatType: number;
  NurseId?: string;
  NurseName?: string;
  CreatorId: string;
  CreatorName: string;
  Role?: string;
  OrganizationId: string;
  OrgName: string;
  WorkerTitle?: string;
  WorkerType?: string;
  DeptId?: string;
  DeptName?: string;
  DiagnoseName: string;
  Detail: string;
  QuickPrescriptionId?: string;
  IsEnable: boolean;
  DeletedTime?: string;
  Mentor?: string;
  DoctorVisibility: boolean;
  TherapistVisibility: boolean;
  TherapistByDoctorVisibility: boolean;
  NurseVisibility: boolean;
  NurseByDoctorVisibility: boolean;
  RxDetailTemplateOutputDtos?: unknown;
  RxTempDiagnosisOutputDtos?: unknown;
  RxTempBackRemind?: unknown;
}
export interface GetDocsInputDTO {
  pageAble: boolean;
  roleTypes?: EpPropMergeTypeWithNull<string[]>;
  keyword?: EpPropMergeTypeWithNull<string>;
  isConsultEnable?: EpPropMergeTypeWithNull<boolean>;
  assistantId?: EpPropMergeTypeWithNull<string>;
  isBindAssistant?: EpPropMergeTypeWithNull<boolean>;
  departmentId?: EpPropMergeTypeWithNull<string>;
  pageIndex: number;
  pageSize: number;
  scopeable: boolean;
  isSelfReliance?: EpPropMergeTypeWithNull<boolean>;
  organizationIds?: EpPropMergeTypeWithNull<string[]>;
  organizationId?: EpPropMergeTypeWithNull<string>;
}
export interface GetPrescriptonExecutInputDTO {
  States?: EpPropMergeTypeWithNull<number[]>;
  TimeType: number;
  BeginTime: string;
  EndTime: string;
  IsTest: EpPropMergeTypeWithNull<boolean>;
  IsContinue: EpPropMergeTypeWithNull<number>;
  SelfReliance: EpPropMergeTypeWithNull<boolean>;
  IsExecut: EpPropMergeTypeWithNull<boolean>;
  OrgIds: EpPropMergeTypeWithNull<string[]>;
  DeptIds: EpPropMergeTypeWithNull<string[]>;
  Sources: EpPropMergeTypeWithNull<number[]>;
  Role: EpPropMergeTypeWithNull<string>;
  CreatorIds?: EpPropMergeTypeWithNull<string[]>;
  AssistantId?: EpPropMergeTypeWithNull<string>;
  MoName?: EpPropMergeTypeWithNull<string>;
  StartAmount: EpPropMergeTypeWithNull<number>;
  EndAmount: EpPropMergeTypeWithNull<number>;
  Keyword?: EpPropMergeTypeWithNull<string>;
  PageIndex: number;
  PageSize: number;
}
export interface PrescriptonExecut {
  ConsultNo: string;
  Id: number;
  PrescriptionId: string;
  UserName: string;
  UserId: string;
  Sex: string;
  Age: string;
  IsContinue: number;
  ContinueId?: string;
  SendTime: string; // ISO 8601 格式的日期字符串
  DoctorId: string;
  DoctorName: string;
  ExecutState: number;
  IsExecut: number;
  MoNames: string;
  PayTime: string; // ISO 8601 格式的日期字符串
  Detail: string;
  Url?: string;
  OrganizationName: string;
  DeptName: string;
  TotalAmount: number;
  OrderNo: string;
  OrderNoOfNum: number;
  Source: number;
  Role: string;
  PatCheckTime: string; // ISO 8601 格式的日期字符串
  IsTest: number;
  DiagnoseName: string;
  PhoneNumber: string;
  NurseId?: string;
  NurseName?: string;
  TherapistId?: string;
  TherapistName?: string;
  Mentor: string;
  CreatorId: string;
  CreatorName: string;
  SelfReliance: boolean;
}
export interface SaveQRCodeInputDTO {
  Type: number;
  RelatedType: number;
  RelatedId: string;
  CreatorId: string;
  IsMain: boolean;
  ExtendUserId?: string;
}
export interface RxTemplateInputDTO {
  IsTemp: boolean;
  PrescriptionInput: RxTemplateMoItemInputDTO;
  RxTempBackRemind: unknown[];
  RxTempDiagnoses: BaseDiagnosis[];
}
export interface RxTemplateMoItemInputDTO {
  CreatorId: string;
  ExecutDay: number;
  DoctorId: string;
  NurseId: string;
  TherapistId: string;
  OrganizationId: string;
  PrescriptionDetailInputs: RxTemplateMoItemDetailInputDTO[];
  PrescriptionName: string;
  Role: string;
  RxTempId?: string;
  Id?: string;
  TherapistRemark: string;
  TreatType: number;
  Part: string[];
}
export interface RxTemplateMoItemDetailInputDTO {
  MoItemId: string;
  MoItemUseScope: number;
  MoItemMethod: number;
  MoDay: number;
  Freq: number;
  TotalCount: number;
  Price: number;
  FreqDay: number;
  IsSpecialFreq: boolean;
  MoName: string;
  MoRemark: string;
  LogisticsDay: number;
  MoItemChargeMode?: number;
  ChargeMode?: number;
  Part?: number;
  ChargeItem?: ChargeItem;
  TotalPrice?: number;
  MaxDay?: number;
  MinDay?: number;
  PrescriptionConsumableInputDtos?: RxTempConsumableItem[];
  Consumables?: string[];
  ScaleInputDtos?: PackScaleItem[];
  ActionInputDtos?: MoItemAction[];
  AcupointInputDtos?: AcuPointInfo[];
  RecoveryMissionRelations?: RxTemplateMissionItem[];
  Manufacturer?: number;
  MoMonth?: number;
  PackId?: string;
  DefaultMoDay?: number;
}
export interface RxTemplateDetail {
  Id: string;
  PrescriptionName: string;
  SendTime: string;
  MoNames?: string;
  MaxDay: number;
  ExecutDay: number;
  DoctorId: string;
  DoctorName: string;
  CreatorDptName?: string;
  CreatorWorkTitle?: string;
  TherapistId: string;
  TherapistName?: string;
  TherapistRemark: string;
  TotalAmount: number;
  Type: number;
  TreatType: number;
  NurseId: string;
  NurseName: string;
  CreatorId: string;
  CreatorName: string;
  Role: string;
  OrganizationId: string;
  OrgName: string;
  WorkerTitle?: string;
  WorkerType?: string;
  DeptId?: string;
  DeptName?: string;
  DiagnoseName: string;
  Detail: string;
  QuickPrescriptionId?: string;
  IsEnable: boolean;
  DeletedTime?: string;
  Mentor?: string;
  DoctorVisibility: boolean;
  TherapistVisibility: boolean;
  TherapistByDoctorVisibility: boolean;
  NurseVisibility: boolean;
  NurseByDoctorVisibility: boolean;
  Part: string[];
  RxDetailTemplateOutputDtos: RxDetailAdviceItem[];
  RxTempDiagnosisOutputDtos: BaseDiagnosis[];
  RxTempBackRemind: RxTempBackRemindItem[];
}
// 宣教
export interface RxTemplateMissionItem {
  Id: string;
  RecoveryMissionId: string;
}
export interface RxDetailAdviceItem {
  MoItemId: string;
  MoItemUseScope: number;
  MoItemMethod: number;
  MoDay: number;
  Freq: number;
  TotalCount: number;
  Price: number;
  FreqDay: number;
  IsSpecialFreq: boolean;
  MoName: string;
  MoRemark: string;
  LogisticsDay: number;
  MoItemChargeMode: number;
  ChargeMode?: number;
  Part: number;
  TotalPrice?: number;
  MaxDay: number;
  MinDay: number;
  Consumables: string[];
  Scales: PackScaleItem[];
  RxActionTemplateOutputDtos: MoItemAction[];
  AcuPoints: AcuPointInfo[];
  RecoveryMissionRelations: RxTemplateMissionItem[];
  Manufacturer: number;
  MoMonth: number;
  RxTempConsumableOutputDtos: RxTempConsumableItem[];
  PackId?: string;
}
export interface RxTempConsumableItem {
  Id?: string;
  RxTempId?: string;
  RxTempDetailId?: string;
  ConsumableId: string;
  Name?: string;
  Spec?: string;
  MoCount?: number;
  Freq?: number;
  FreqDay?: number;
  MoDay?: number;
  CreatedTime?: string;
}
export interface RxTempBackRemindItem {
  Day: number;
  DctId: string;
  DeptId: string;
  Remark: string;
  Sort?: number;
  Type: number;
  Id?: string;
  RxTemplateId?: string;
  Name?: string;
}
export interface DoctorInfo {
  DoctorId: string;
  Name: string;
  HeadImg: string;
  OrgId: string;
  OrgName: string;
  DeptName: string;
  PhoneNumber: string;
  WorkerTitle: string;
  DoctorFirstAuthTime: string;
  UserConsultSet: {
    UserId?: EpPropMergeTypeWithNull<string>;
    VideoCost: number;
    RichtextCost: number;
    Organization?: EpPropMergeTypeWithNull<string>;
    IsEnable: boolean;
    ShowCost: number;
    UploadToSupervision: boolean;
    IsTechExchange: boolean;
    Recommend: boolean;
    SelfReliance: boolean;
  };
  DiagnoseNames?: {
    DiagnoseName: string;
    Count: number;
  }[];
  MoItemNames?: {
    MoItemName: string;
    Count: number;
  }[];
  UserWork?: UserWork;
  UserBill?: {
    Id: string;
    CardNumber: string;
    BankName: string;
    BankCode: string;
    SubBranch: string;
    SubBranchCode: string;
    Province: string;
    ProvinceCode: string | number;
    City: string;
    CityCode: string;
  };
  Settlement?: {
    MarketingId?: EpPropMergeTypeWithNull<string>;
    SettlementMode?: EpPropMergeTypeWithNull<number>;
    PrescriptionSettleRatio1: number;
    PrescriptionSettleRatio2: number;
    GuideSettleRatio: number;
  };
  UserClaims?: UserClaim[];
  AssistantFollowRecords?: {
    AssistantId: string;
    AssistantName: string;
    Category: number;
    Remark: EpPropMergeTypeWithNull<string>;
  }[];
}
export interface GetOrderManageInputDTO {
  PageIndex: number;
  PageSize: number;
  BeginTime: string;
  EndTime: string;
  States: EpPropMergeTypeWithNull<number[]>;
  PayType: EpPropMergeTypeWithNull<number>;
  keyword: string;
  OrgIds: EpPropMergeTypeWithNull<string[]>;
  DoctorIds: EpPropMergeTypeWithNull<string[]>;
  IsTest: EpPropMergeTypeWithNull<number>;
  InstrumentIds: EpPropMergeTypeWithNull<string[]>;
  Consumables: EpPropMergeTypeWithNull<string[]>;
  MinTotalAmount: EpPropMergeTypeWithNull<number>;
  MaxTotalAmount: EpPropMergeTypeWithNull<number>;
  DeptIds: EpPropMergeTypeWithNull<string[]>;
  Scopeable: boolean;
}
export interface TherapyOrder {
  ConsultId: string;
  PrescriptionId: string;
  PrescriptionIdStr: string;
  PayTime: string; // ISO 8601
  UserName: string;
  OrderId: string;
  CreatedTime: string; // ISO 8601
  UserId: string;
  TreatAmount: number;
  TreatRefundAmount: number;
  OrderNo: string;
  TreatOrderNo: string;
  TreatOrder: TreatOrder;
  DeviceOrderNo: string;
  DeviceOrder?: TherapyDeviceOrder;
  DeviceAmount?: number;
  DeviceRefundAmount?: number;
  PatUser: BaseUserProfile;
  PayType: number;
  PayTypeAlias: string;
  Url?: string;
  Consum: string;
  Consumables: {
    Name: string;
    Count: number;
    Spec: string;
  }[];
  TherapistRemark: string;
  ICD: BaseDiagnosis[];
  OrganizationId: string;
  OrganizationName: string;
  DptName: string;
  CityName: string;
  DoctorId: string;
  DoctorName: string;
  RootConsultId?: string;
  PrescriptionAmount: number;
  DepositAmount: number;
  IsTest: string;
  Gift?: string;
  GiftMark?: string;
  AssistantId: string;
  AssistantName: string;
  InstrumentPara: unknown;
  ICDDictArray: BaseDiagnosis[];
}

export interface TherapyDeviceOrder {
  OrderDetails: TherapyDeviceOrderDetail[];
}
export interface TherapyDeviceOrderDetail {
  Id: string;
  Title: string;
  Price: number;
  TotalPrice: number;
  OrderAftersales: unknown[];
  GoodsType: string;
  RelationId: string;
}

export interface OrderDetailItem {
  Id: string;
  Title: string;
  Price: number;
  TotalPrice: number;
  OrderAftersales: unknown[];
  GoodsType: string;
  RelationId: string;
}

export interface TreatOrderDetail {
  OrganizationName: string;
  CreatorName: string;
  PatUser: BaseUserProfile;
  ICD: BaseDiagnosis[];
  OrderAddresss: OrderAddress[];
  TotalAmount: number;
  TreatOrderMoOutputDtos: {
    PrescriptionId?: string;
    MoItemId: string;
    MoName: string;
    MoRemark: string;
    MoDay: number;
    MoMonth: number;
    Freq: number;
    Part: number;
    TotalCount: number;
    FreqDay: number;
    IsSpecialFreq: boolean;
    Price: number;
    ShowPrice: number;
    MoItemMethod: number;
    MoItemChargeMode: number;
  }[];
  RentDataMoney: number;
  RentDataInfos: {
    RentDataName: string;
    RentDataAmount: number;
  }[];
  TreatOrder: TreatOrder;
  CreatedTime: string;
  PayTime: string;
  PayType: number;
  OrderGift: {
    PrescriptionId: string;
    Gift: string;
    Mark: string;
    UserId: string;
    CreatedTime: string;
  };
  TreatOrderNo: string;
  DeliveryTime?: string;
  CollectTime?: string;
  CancelTime?: string;
}
export interface PrescriptionDetail {
  ConsultId: string;
  Id: string;
  OrganizationName: string;
  CreatorName: string;
  UserName: string;
  Sex: string;
  Age: string;
  PrescriptionDetails: {
    MoItemChargeMode: number;
    MoName: string;
    FreqDay: number;
    Freq: number;
    MoDay: number;
    MoMonth: number;
    TotalCount: number;
    Part: number;
    Price: number;
    ShowPrice?: number;
    CreatorId: string;
  }[];
  State: number;
  UserId: string;
}
export interface ReturnVisitInputDTO {
  OrgIds?: EpPropMergeTypeWithNull<string[]>;
  DeptIds?: EpPropMergeTypeWithNull<string[]>;
  AssistantIds?: EpPropMergeTypeWithNull<string[]>;
  PreCreatorIds?: EpPropMergeTypeWithNull<string[]>;
  Keywords?: EpPropMergeTypeWithNull<string>;
  IsTest?: EpPropMergeTypeWithNull<number>;
  IsLoadCompleted?: EpPropMergeTypeWithNull<boolean>;
  DtoName: string;
  QueryStartDate: string;
  QueryEndDate: string;
  Order: string;
  Scopeable: boolean;
  PageIndex: number;
  PageSize: number;
}
export interface ReturnVisitItem {
  /**
   * 回访结果，JSON解析后对应模型 ReturnVisitContentDTO
   */
  Content?: string;
  MoName?: string;
  PreCreatedTime?: string;
  ReturnVisitEvaInputDtos?: string;
  Id: number;
  IdStr: string;
  PrescriptionId: string;
  PrescriptionName: string;
  OrganizationName: string;
  DiagnosisName: string;
  PrescriptionExecDay: number;
  AssistantId: string;
  Day: number;
  ReturnVisitDate: string; // ISO 8601 date string
  PatId: string;
  PatName: string;
  PatPhone: string;
  PatImg: string;
  PatSex: string;
  Age: number;
  Type: number;
  ShowExternal: boolean;
  ReturnVisit: boolean;
  ContentTime?: string;
  CreatedTime: string; // ISO 8601 date string
  PreCreatorId: string;
  PreTherapistId?: string;
  DoctorId: string;
  NurseId?: string;
  AssistantName: string;
  PreCreatorName: string;
  PreTherapistName?: string;
  DoctorName: string;
  NurseName?: string;
  ProgramId: string;
  IsTest: string; // Assuming this is a string representation of a boolean
  LeaveHospital?: string;
  Exec?: string;
  ProEnd: string;
  Myd: number;
  Zlxg: number;
}

export interface ReturnVisitContentDTO {
  Content?: string;
  Imgs?: string[];
}

export interface GetCardOrdersListParams {
  OrgId?: string;
  DeptId?: string;
  UserId?: string;
  OrderStates?: number[];
  BeginTime: string; //"2020-03-05 00:00:00"
  EndTime: string; //"2025-04-23 23:59:59"
  PageSize: number;
  PageIndex: number;
}

export interface GetCardsListParams {
  OrgId?: string;
  DeptId?: string;
  UserId?: string;
  IsDeleted?: boolean;
  Application?: boolean;
  PageSize: number;
  PageIndex: number;
}

export interface SetSettlementInputDTO {
  Mode: number;
  Settlements: UserSettlement[];
}

export interface UserSettlement {
  PrescriptionSettleRatio1: EpPropMergeTypeWithNull<number>;
  PrescriptionSettleRatio2: EpPropMergeTypeWithNull<number>;
  GuideSettleRatio: EpPropMergeTypeWithNull<number>;
  DeptChiefSettleRatio: EpPropMergeTypeWithNull<number>;
  PresidentSettleRatio: EpPropMergeTypeWithNull<number>;
  NurseChiefSettleRatio: EpPropMergeTypeWithNull<number>;
  BindDeptChiefId: string;
  BindPresidentId: string;
  BindNurseChiefId: string;
  MarketingId: string;
  MarketingSettleRatio: EpPropMergeTypeWithNull<number>;
  AssistantManagerId: string;
  AssistantManagerSettleRatio: EpPropMergeTypeWithNull<number>;
  OnlineGuidanceId: string;
  OnlineGuidanceSettleRatio: EpPropMergeTypeWithNull<number>;
  DealerId: string;
  DealerSettleRatio: EpPropMergeTypeWithNull<number>;
  ReferrerId: string;
  ReferrerSettleRatio: EpPropMergeTypeWithNull<number>;
  OtherId: string;
  OtherSettleRatio: EpPropMergeTypeWithNull<number>;
  AssistantSettleRatio: EpPropMergeTypeWithNull<number>;
  UserId?: string;
}

export interface DeviceManageInputDTO {
  PageIndex: number;
  PageSize: number;
  Keyword: string;
  DeviceBizState: EpPropMergeTypeWithNull<number>;
  InstrumentId: EpPropMergeTypeWithNull<string>;
  BeginTime: string;
  EndTime: string;
  LimitRemainMaxDays?: unknown;
}
export interface DeviceManageItem {
  PrescriptionId: string;
  CreatedTime: string;
  DeviceName: string;
  PayTime: string;
  PayType: number;
  UserId: string;
  PatUser: BaseUserProfile;
  OrderAddresssName: string;
  Amount: number;
  OrderState: number;
  DeviceState: number;
  DeviceBizState: number;
  UserName: string;
  DeviceId: string;
  Url?: any;
  OrderNo: string;
  OrganizationName: string;
  OrganizationId: string;
  DeviceOrderNo: string;
  PlanDays: number;
  ActualDays: number;
  CollectTime?: any;
  ReturnTime?: any;
  StorageTime?: any;
  StorageOptUserId?: any;
  StorageOptUserName?: any;
  RemainDays?: any;
  DeviceOrderInfo: DeviceManageOrderInfo;
}
export interface DeviceManageOrderInfo {
  OrderDetails: DeviceManageOrderInfoDetail[];
  PayType: number;
  OrderExpresses: OrderExpress[];
}
export interface DeviceManageOrderInfoDetail {
  Id: string;
  Title: string;
  Price: number;
  TotalPrice: number;
  OrderAftersales: DeviceManageAftersale[];
  GoodsType: string;
  RelationId: string;
}
export interface DeviceManageAftersale {
  Id: string;
  Status: number;
  Price: number;
  OrderDetailId: string;
  RefundOptUserId: string;
  RefundOptUserName: string;
  RefundTime: string;
  Reason?: string;
}
export interface OrderDeviceBizStorageInputDTO {
  OrderNo: string;
  OrderDetailId: string;
  OptUserId: string;
}
export interface ReturnOrderDetailInputDTO {
  ExpressName: string;
  ExpressNumber: string;
  ExpressCode: string;
  Phone: string;
  ApplyReason: string;
  OrderDetailId: string;
}

export interface GetFollowUpPlanPageParams {
  PageIndex: number;
  PageSize: number;
  KeyWords?: string;
  State?: number;
  Type?: number;
  StartTime: string;
  EndTime: string;
  LoginUserId: string;
  OrgId?: string;
}

export interface PlatformAddFollowUpPlanParams {
  PatName: string;
  Sex: string;
  Age: number;
  PatNo?: string;
  Name: string;
  ExecName?: string;
  StartTime: string;
  OrgId: string;
  OrgName: string;
  CreatorId: string;
  CreatorName: string;
  Type: number;
  Sort: number;
  Remark?: string;
  FollowUpPlanDetailInputDtos: FollowUpPlanDetailInputDTO[];
  PatId?: string;
}

export interface FollowUpPlanDetailInputDTO {
  RelatedId: string;
  RelatedName?: string;
  Type: number;
}

export interface GetFollowUpPlanDetailPageParams {
  StartTime: string;
  EndTime: string;
  KeyWords?: string;
  PlanType?: number[];
  PageIndex: number;
  PageSize: number;
  State?: number;
  RelatedId?: string;
  DetailType?: number;
  OrgId?: string;
}

export interface GetConsultRecordsInputDTO {
  pageIndex: number;
  pageSize: number;
  Keyword?: string;
  StartDate: string;
  EndDate: string;
  OrganizationIds?: string[];
  DepartmentIds?: string[];
  UserIds?: string[];
  /** 就诊状态 */
  States?: number[];
  /** 服务类型 */
  ConsultWays?: number[];
  /** 方案状态 */
  PreState?: number;
  /** 来源 */
  Source?: number;
  /** 是否测试数据 */
  IsTest?: boolean;
}
