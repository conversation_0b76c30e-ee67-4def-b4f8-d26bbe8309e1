import { setRefreshToken, setToken } from "@/utils/auth";
import { CUSTOM_HEADERS } from "@/utils/constants";
import { httpClient } from "@/utils/request";

let refreshTokenRequest: Promise<string> | null = null;

const AuthAPI = {
  /** 刷新 token 接口*/
  async refreshToken(refresh_token: string): Promise<string> {
    if (refreshTokenRequest) return refreshTokenRequest;

    let formData = new URLSearchParams();
    formData.append("grant_type", "refresh_token");
    formData.append("refresh_token", refresh_token);
    formData.append("client_id", import.meta.env.VITE_APP_CLIENT_ID);
    formData.append("client_secret", import.meta.env.VITE_APP_CLIENT_SECRET);
    refreshTokenRequest = httpClient
      .post("/passport/connect/token", formData, {
        headers: { ...CUSTOM_HEADERS, Authorization: "no-auth" },
      })
      .then((data) => {
        console.log("refresh_token", data);
        if (data.status != 200) throw new Error("refresh_token error");
        setToken(data.data.access_token);
        setRefreshToken(data.data.refresh_token);
        // store.commit("user/SET_TOKEN", data.data.access_token);
        return data.data.access_token;
      })
      .finally(() => {
        refreshTokenRequest = null;
      });
    return refreshTokenRequest;
  },
};

export default AuthAPI;
