import { type EpPropMergeTypeWithNull } from "element-plus";

export interface DictCreateUpdateInputDTO {
  Key: string;
  Value?: string;
  OrgId?: string;
  IsPublish?: boolean;
  PinyinCode: string;
  Remark: string;
  IsEnabled: boolean;
  DictId: number;
  Id?: string;
  DictItemRelateds?: DictItemRelated[];
  OrderNumber?: number;
  CustomSort?: EpPropMergeTypeWithNull<number>;
  ParentId?: EpPropMergeTypeWithNull<string>;
}
