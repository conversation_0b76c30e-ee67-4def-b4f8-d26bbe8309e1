import request from "@/utils/request";
import { type RefundExecInputDTO, type CommunityMoItem } from "./types";
const community_MoItemExecute = "/community/api/MoItemExecute";

const Community_Api = {
  /** 获取社区项目列表 */
  getMoItemByPrescriptionId(params: { orderNo: string }): Promise<ServerResult<CommunityMoItem[]>> {
    return request.get(`${community_MoItemExecute}/GetMoItemByPrescriptionId`, { params });
  },
  /** 社区线下退款 */
  refundExecCount(data: RefundExecInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${community_MoItemExecute}/RefundExecCount`, data);
  },
};

export default Community_Api;
