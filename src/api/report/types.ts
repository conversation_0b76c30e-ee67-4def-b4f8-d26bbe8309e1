import { type EpPropMergeTypeWithNull } from "element-plus";

export interface ExportTaskInputDTO {
  ServiceExportCode: string; //后端
  ExportWay: number; // O:Redash, 1：后端
  ExecutingParams: unknown;
  FileName: string;
}
export interface ExportTaskRedashDTO {
  Cols?: string;
  ExecutingParams: any;
  ExportWay: number;
  FileName: string;
  JobWaitingMs?: number;
  QueryResultId?: number;
  Split?: string;
  MaxAge?: number;
  PageIndex?: number;
  PageSize?: number;
  QueryName?: string;
  ServiceExportCode?: string;
}
export interface GetDoctorRedashParamsInputDTO {
  RoleCode: EpPropMergeTypeWithNull<string>;
  Keyword: EpPropMergeTypeWithNull<string>;
  IsEnableConsult: EpPropMergeTypeWithNull<number>;
  AssistantId: EpPropMergeTypeWithNull<string>;
  HasAssistant: EpPropMergeTypeWithNull<number>;
  DeptId: EpPropMergeTypeWithNull<string>;
  PageIndex: number;
  PageSize: number;
  IsSelfReliance: EpPropMergeTypeWithNull<boolean>;
  OrgId: EpPropMergeTypeWithNull<string>;
  Liveness: EpPropMergeTypeWithNull<string>;
  OrderMode: string;
  IsBankInfoComplete: EpPropMergeTypeWithNull<string>;
  LoginUserId: EpPropMergeTypeWithNull<string>;
}
export interface DoctorRedashItem {
  DoctorName: string;
  OrgName: string;
  PracticeOrganizationName: string;
  DeptName: string;
  WorkerTitle: string;
  RoleName: string;
  PhoneNumber: string;
  AssistantName: string;
  AssistantId: string;
  DoctorFirstAuthTime: string;
  SelfReliance: string;
  Liveness: string;
  PrescriptionFrequency: string;
  PayPrescriptionTotalCount: string;
  RecentlyExcuteTime: string;
  Remark: string;
  DoctorId: string;
  OrgId: string;
}
export interface PlatformHomePageSummary {
  AuthDoctorCount: string;
  AuthTherapistCount: string;
  AuthNurseCount: string;
  NormalUserCount: string;
  OrganizationCount: string;
  NewOrganizationCount: string;
  WaitSendGoodsOrderCount: string;
  WaitRefundedDeviceBond: string;
  WaitAuthDoctorCount: string;
  UnReadUserFeedback: string;
  DoctorConsultCount: string;
  TherapistConsultCount: string;
  NurseConsultCount: string;
  DoctorConsultAmount: string;
  TherapistConsultAmount: string;
  NurseConsultAmount: string;
  DoctorPrescriptionCount: string;
  TherapistPrescriptionCount: string;
  NursePrescriptionCount: string;
  DoctorPrescriptionAmount: string;
  TherapistPrescriptionAmount: string;
  NursePrescriptionAmount: string;
}
export interface PlatformHomePageStatistics {
  DoctorCount: string;
  DailyDoctorCount: string;
  YesterdayDoctorCount: string;
  TherapistCount: string;
  DailyTherapistCount: string;
  YesterdayTherapistCount: string;
  NurseCount: string;
  DailyNurseCount: string;
  YesterdayNurseCount: string;
  ConsultCount: string;
  DailyConsultCount: string;
  YesterdayConsultCount: string;
  TreatmentCount: string;
  DailyTreatmentCount: string;
  YesterdayTreatmentCount: string;
  NurseConsultCount: string;
  DailyNurseConsultCount: string;
  YesterdayNurseConsultCount: string;
  DoctorConsultAmount: string;
  DailyDoctorConsultAmount: string;
  YesterdayDoctorConsultAmount: string;
  TherapistConsultAmount: string;
  DailyTherapistConsultAmount: string;
  YesterdayTherapistConsultAmount: string;
  NurseConsultAmount: string;
  DailyNurseConsultAmount: string;
  YesterdayNurseConsultAmount: string;
  DoctorQuickPrescriptionCount: string;
  DailyDoctorQuickPrescriptionCount: string;
  YesterdayDoctorQuickPrescriptionCount: string;
  TherapistQuickPrescriptionCount: string;
  DailyTherapistQuickPrescriptionCount: string;
  YesterdayTherapistQuickPrescriptionCount: string;
  NurseQuickPrescriptionCount: string;
  DailyNurseQuickPrescriptionCount: string;
  YesterdayNurseQuickPrescriptionCount: string;
  DoctorDirectPrescriptionCount: string;
  DailyDoctorDirectPrescriptionCount: string;
  YesterdayDoctorDirectPrescriptionCount: string;
  TherapistDirectPrescriptionCount: string;
  DailyTherapistDirectPrescriptionCount: string;
  YesterdayTherapistDirectPrescriptionCount: string;
  NurseDirectPrescriptionCount: string;
  DailyNurseDirectPrescriptionCount: string;
  YesterdayNurseDirectPrescriptionCount: string;
  DoctorConsultPrescriptionCount: string;
  DailyDoctorConsultPrescriptionCount: string;
  YesterdayDoctorConsultPrescriptionCount: string;
  TherapistConsultPrescriptionCount: string;
  DailyTherapistConsultPrescriptionCount: string;
  YesterdayTherapistConsultPrescriptionCount: string;
  NurseConsultPrescriptionCount: string;
  DailyNurseConsultPrescriptionCount: string;
  YesterdayNurseConsultPrescriptionCount: string;
  VisitAmount: string;
  DailyVisitAmount: string;
  YesterdayVisitAmount: string;
}
export interface PlatformHomePageChart {
  Date: string;
  UserCount: string;
  inviteCount: string;
  PatientCount: string;
  VisitCount: string;
  ConsultCount: string;
  TreatmentCount: string;
  NurseCount: string;
  PrescriptionCount: string;
  ExecutePrescriptionCount: string;
  RegisterDoctorConsultAmount: string;
  RegisterTherapistConsultAmount: string;
  TreatmentAmount: string;
}
export interface PatientList {
  Id: string;
  PhoneNumber: string;
  NickName: string;
  UserName: string;
  HeadImg: string;
  IsSystem: boolean;
  CreatedTime: string;
  Name: string;
  Code?: string;
  Birthday?: string;
  Sex?: string;
  IsEnabled?: boolean;
  UserNameUpdateTime?: string;
  ChangePasswordTime?: string;
  Organization?: string;
  Department?: string;
  Consortiums?: string;
  Roles?: string[];
  UserExternalIdentify?: {
    WeChatQrCode?: string;
  };
}
export interface BusinessGoalCompletionSummary {
  Month: string;
  TargetAmount: string;
  Amount: string;
  TargetAmountCompletionRate: string;
  TargetSelfRelianceCount: string;
  SelfRelianceCount: string;
  SelfRelianceCompletionRate: string;
}
export interface BusinessGoalCompletionDetail {
  Id?: string;
  Name?: string;
  GroupName?: string;
  IncreaseAmount: string;
  MonthAmount: string;
  PreviousMonthAmount: string;
  AmountGrowthRate: string;
  PrescriptionCount: string;
  PreviousPrescriptionCount: string;
  PrescriptionCountGrowthRate: string;
  TargetAmount: string;
  MonthTargetAmountCompletionRate: string;
  CustomerUnitPrice: string;
  ReturnVisitCount: string;
  IncreaseSelfReliancePrescriptionCount: string;
  MonthSelfReliancePrescriptionCount: string;
  PreviousMonthSelfReliancePrescriptionCount: string;
  SelfRelianceGrowthRate: string;
  TargetSelfRelianceCount: string;
  MonthTargetSelfRelianceCompletionRate: string;
  SelfRelianceRate: string;
  PreviousSelfRelianceRate: string;
  PrescriptionContinueAmount: string;
  PrescriptionContinueCount: string;
}
export interface PrescriptionSummaryInputDTO {
  DisplayEmptyData: string;
  EndTimeDt: string;
  StartTimeDt: string;
  LoginUserId: string;
  PageIndex: number;
  PageSize: number;
  orgIds: EpPropMergeTypeWithNull<string>;
}
export interface PrescriptionSummaryItem {
  Name: string;
  CityName: string;
  DailyConsultCount: string;
  DailyAdvisoryCount: string;
  DailyDoctorQuickConsultCount: string;
  DailyDoctorPositiveCount: string;
  DailyThePositiveCount: string;
  DailyPaitentConsultCount: string;
  DailyPaitentAdvisoryCount: string;
  DailyYunDongCount: string;
  DailyGeWuCount: string;
  DailyMaiZhenCount: string;
  DailyCiReCount: string;
  DailyKangFuPingDingCount: string;
  DailyZeroAmountCount: string;
  DailyNonZeroAmountCount: string;
  DailyExecutedContainAmountCount: string;
  DailyExecutedAmount: string;
  DailyExecutedCount: string;
  DailyPrescriptionCount: string;
  TotalConsultCount: string;
  TotalAdvisoryCount: string;
  TotalYunDongCount: string;
  TotalGeWuCount: string;
  TotalMaiZhenCount: string;
  TotalCiReCount: string;
  TotalKangFuPingDingCount: string;
  TotalExecutedContainAmountCount: string;
  TotalExecutedAmount: string;
  TotalExecutedCount: string;
  TotalAmountZeroAmountCount: string;
  TotalNonZeroAmountCount: string;
  TotalPrescriptionCount: string;
}

export interface NewPrescriptionSummaryInputDTO {
  BeginTimeDt: string;
  EndTimeDt: string;
  OrgIds: EpPropMergeTypeWithNull<string>;
  DisplayEmptyData: string;
  LoginUserId: string;
  PageIndex: number;
  PageSize: number;
}
export interface NewPrescriptionSummaryItem {
  Name: string;
  CityName: string;
  NewDoctorConsultCount: string;
  NewTherapistConsultCount: string;
  NewNurseConsultCount: string;
  NewDoctorQuickConsult: string;
  NewTherapistQuickConsult: string;
  NewNurseQuickConsult: string;
  NewDoctorDirectConsultCount: string;
  NewTherapistDirectConsultCount: string;
  NewNurseDirectConsultCount: string;
  NewPaitentConsultCount: string;
  NewPaitentAdvisoryCount: string;
  NewPaitentNurseCount: string;
  NewFreeGeWuCount: string;
  NewGeWuCount: string;
  NewGeWuAmount: string;
  NewFreeMaiZhenCount: string;
  NewMaiZhenCount: string;
  NewMaiZhenAmount: string;
  NewFreeCiReCount: string;
  NewCiReCount: string;
  NewCiReAmount: string;
  NewFreeHuXiCount: string;
  NewHuXiCount: string;
  NewHuXiAmount: string;
  NewChaoShenCount: string;
  NewChaoShenAmount: string;
  NewFreeYunDongCount: string;
  NewYunDongCount: string;
  NewYunDongAmount: string;
  NewFreeKangFuZiXunCount: string;
  NewKangFuZiXunCount: string;
  NewKangFuZiXunAmount: string;
  NewFreeKangFuPingDingCount: string;
  NewKangFuPingDingCount: string;
  NewKangFuPingDingAmount: string;
  NewFreePrescriptionCount: string;
  NewPrescriptionCount: string;
  NewContinuePrescriptionCount: string;
  NewPrescriptionAmount: string;
  SendPrescriptionCount: string;
}
export interface PatientsSummaryInputDTO {
  StartTime: string;
  EndTime: string;
  orgIds: EpPropMergeTypeWithNull<string>;
  DisplayEmptyData: string;
  LoginUserId: string;
  PageIndex: number;
  PageSize: number;
}
export interface PatientsSummaryItem {
  OrganizationName: string;
  CityName: string;
  newUserCount: string;
  newUserAuthCount: string;
  newPatientCount: string;
  userCount: string;
  userAuthCount: string;
  patientCount: string;
  OrganizationId: string;
}
export interface SettlementInfo {
  OrgId: string;
  UserId: string;
  RoleName: string;
  Name: string;
  PhoneNumber: string;
  OrgName: string;
  DeptName: string;
  BindDeptChiefName: string;
  BindPresidentName: string;
  BindNurseChiefName: string;
  PrescriptionSettleRatio1: string;
  PrescriptionSettleRatio2: string;
  GuideSettleRatio: string;
  DeptChiefSettleRatio: string;
  PresidentSettleRatio: string;
  NurseChiefSettleRatio: string;
  MarketingName: string;
  MarketingSettleRatio: string;
  AssistantManagerName: string;
  AssistantManagerSettleRatio: string;
  OnlineGuidanceName: string;
  OnlineGuidanceSettleRatio: string;
  DealerName: string;
  DealerSettleRatio: string;
  ReferrerName: string;
  ReferrerSettleRatio: string;
  OtherName: string;
  OtherSettleRatio: string;
  AssistantName: string;
  AssistantSettleRatio: string;
  BindDeptChiefId: string;
  BindPresidentId: string;
  BindNurseChiefId: string;
  MarketingId: string;
  AssistantManagerId: string;
  OnlineGuidanceId: string;
  DealerId: string;
  ReferrerId: string;
  OtherId: string;
  AssistantId: string;
  [key: string]: any;
}
export interface DoctorPrescriptionStatisticsInputDTO {
  BeginTimeDt: string;
  EndTimeDt: string;
  OrgIds: EpPropMergeTypeWithNull<string>;
  DeptId: EpPropMergeTypeWithNull<string>;
  Keyword: string;
  IncludeNoDataDoctor: EpPropMergeTypeWithNull<string>;
  PageIndex: number;
  PageSize: number;
  LoginUserId: string;
}
export interface DoctorPrescriptionStatisticsItem {
  DoctorName: string;
  OrgName: string;
  DeptName: string;
  AssistantName: string;
  RoleName: string;
  FreePrescriptionCount: string;
  FreeExecPrescriptionCount: string;
  FreePrescriptionExecRate: string;
  PaidPrescriptionCount: string;
  PaidExecPrescriptionCount: string;
  PaidPrescriptionExecRate: string;
  RefundPartPrescriptionCount: string;
  RefundWholePrescriptionCount: string;
  PrescriptionRefundRate: string;
  ExecAmount: string;
  RefundAmount: string;
  CiReCount: string;
  HuXiCount: string;
  ChaoShenCount: string;
  GeWuCount: string;
  MaiZhenCount: string;
  YunDongCount: string;
}
export interface PatientStatisticsInputDTO {
  OrgIds: string;
  Keyword: string;
  DeptIds: string;
}

export interface PatientStatisticsItem {
  PatientCount: string;
  ManPatientCount: string;
  WomanPatientCount: string;
  UnkownSex: string;
  AverageAge: string;
  AgeLessThan20: string;
  AgeBetween20To40: string;
  AgeBetween40To60: string;
  AgeGreaterThan60: string;
  UnkownAge: string;
}
export interface PatientDiseaseTagStatistics {
  OrgIds: string;
  Keyword: string;
  DeptIds: string;
}
export interface PatientDiseaseTagStatisticsItem {
  Tag: string;
  Count: string;
}
export interface PrescriptionExecStatisticItem {
  Date: string;
  TotalCnt: string;
  ExecTotalCnt: string;
}
export interface PrescriptionSendStatisticItem {
  Name: string;
  TotalCnt: string;
  ExecTotalCnt: string;
}
export interface PatientDiagnoseStatisticsItem {
  DiagnoseName: string;
  Count: string;
}

export interface PatientInquiryParams {
  LoginUserId: string;
  departmentId?: string;
  doctorUserId?: string;
  isTest?: string;
  keywords?: string;
  orgIds?: string;
  tagKeyWords: string; // 全部为" . "
}

export interface ConsultationOrderParams {
  consultWays?: string;
  deptIds?: string;
  doctorUserIds?: string;
  endTimeDt: string;
  isTest: string;
  keywords?: string;
  orgIds?: string;
  startTimeDt: string;
  states?: string;
  timeType: string;
}

export interface VisitSettlementReportParams {
  LoginUserId: string;
  assistantUserIds?: string;
  deptIds?: string;
  doctorUserIds?: string;
  isTest: string;
  keyword?: string;
  optTimeEnd: string;
  optTimeStart: string;
  orgIds?: string;
}

export interface TreatmentSettlementReportParams {
  orgIds?: string;
  doctorUserIds?: string;
  assistantUserIds?: string;
  therapistUserIds?: string;
  nurseUserIds?: string;
  orderNo?: string;
  isTest: string;
  deptIds?: string;
  LoginUserId: string;
  BeginTimeDt?: string;
  EndTimeDt?: string;
  optTimeEnd?: string;
  optTimeStart?: string;
}

export interface DoctorSettlementStatisticsInputDTO {
  RoleName?: string;
  OrgIds?: string;
  DeptId?: string;
  Keyword?: string;
  StartTimeDt: string;
  EndTimeDt: string;
}

export interface DoctorSettlementDetailsInputDTO {
  UserId: string;
  StartTimeDt: string;
  EndTimeDt: string;
}

export interface DoctorStatusStatisticsInputDTO {
  LoginUserId: string;
  assistantIds?: string;
  deptIds?: string;
  endTimeDt: string;
  orgIds?: string;
  startTimeDt: string;
}

export interface DoctorStatusRedash {
  Name?: string;
  PhoneNumber?: string;
  Sex?: string;
  Age?: string;
  OrgName?: string;
  DeptName?: string;
  AssistantName?: string;
  PracticeLevel?: string;
  RoleName?: string;
  newPatientCount?: string;
  totalPatientCount?: string;
  newConsultPatientCount?: string;
  newReConsultPatientCount?: string;
  newPrescriptionCount?: string;
  newExecuteCount?: string;
  newPayCount?: string;
  newCiReCount?: string;
  newCiReUnitCount?: string;
  newGeWuCount?: string;
  newGeWuUnitCount?: string;
  newMaiZhenCount?: string;
  newMaiZhenUnitCount?: string;
  newConsultCount?: string;
  newPayConsultCount?: string;
  profileDescription?: string;
  enableConsult?: string;
  showConsultCost?: string;
  isLoginIn7Day?: string;
  isTest?: string;
  DocUserId?: string;
}

export interface PatientRedash {
  Age?: string;
  BinDingDoc?: string;
  BinDingNurse?: string;
  BinDingTherapist?: string;
  ConsultCount?: string;
  DocUserId?: string;
  ExecProgramState?: string;
  IsTest?: string;
  LastConsultDate?: string;
  Name?: string;
  OrgId?: string;
  OrganizationName?: string;
  PhoneNumber?: string;
  Sex?: string;
  Tags?: string;
  TrainingCount?: string;
  UserId?: string;
}
