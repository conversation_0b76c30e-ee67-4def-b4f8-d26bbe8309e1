import request from "@/utils/request";
import { type ExportTaskRedashDTO, type ExportTaskInputDTO } from "./types";

const Report_Redash = "/report/api/redash";

const Report_Api = {
  /** 创建导出任务 */
  createExportTask(
    data: ExportTaskInputDTO | ExportTaskRedashDTO
  ): Promise<ServerResult<{ TaskId: string }>> {
    return request.post(`${Report_Redash}/CreateExportTask`, data);
  },
  /** 获取导出任务列表 */
  getExportTasks(params: {
    userId: string;
    pageIndex: number;
    pageSize: number;
  }): Promise<ServerResult<ListRowsTotal<BaseTask>>> {
    return request.get(`${Report_Redash}/GetExportTasks`, { params });
  },
  /** 删除导出任务 */
  removeExportTask(data: string[]): Promise<ServerResult<null>> {
    return request.post(`${Report_Redash}/RemoveExportTask`, data);
  },
  /** 获取导出任务结果 */
  queryResultsExportV2(data: { TaskId: string }): Promise<ServerResult<string>> {
    return request.post(`${Report_Redash}/exportV2`, data, { responseType: "arraybuffer" });
  },
  /** 获取redash列表数据 */
  getRedashList<T>(data: RedashParamsInputDTO<any>): Promise<ServerResult<RedashResult<T>>> {
    return request.post(`${Report_Redash}/QueryResults`, data);
  },
};

export default Report_Api;
