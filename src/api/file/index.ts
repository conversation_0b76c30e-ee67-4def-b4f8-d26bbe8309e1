import { kDebug } from "@/utils";
import request, { httpClient } from "@/utils/request";
const ossBaseURL = kDebug
  ? `http://${import.meta.env.VITE_DEV_HOST}:${import.meta.env.VITE_DEV_PORT}${import.meta.env.VITE_PROXY_OSS}`
  : import.meta.env.VITE_APP_UPLOAD_URL;

/**
 * 上传文件
 *
 * @param formData
 */
const upload = (formData: FormData): Promise<ServerResult<FileResult>> => {
  return request.post<FileResult>(ossBaseURL + "/api/oss/upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

/**
 * 上传文件
 */
const uploadFile = (file: File): Promise<ServerResult<FileResult>> => {
  const formData = new FormData();
  formData.append("file", file);
  return upload(formData);
};

/**
 * 下载文件
 * @param url
 * @param fileName
 */
const download = (url: string, fileName?: string) => {
  return httpClient
    .get<Blob>(url, {
      responseType: "blob",
    })
    .then((res) => {
      const blob = new Blob([res.data]);
      const a = document.createElement("a");
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = fileName || "下载文件";
      a.click();
      window.URL.revokeObjectURL(url);
    });
};

const FileAPI = {
  upload,
  uploadFile,
  download,
};

export default FileAPI;

/**
 * 文件API类型声明
 */
export interface FileInfo {
  /** 文件名 */
  name: string;
  /** 文件路径 */
  url: string;
}
