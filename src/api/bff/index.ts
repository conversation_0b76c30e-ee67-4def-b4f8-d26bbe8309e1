import request from "@/utils/request";

const Bff_Biz = "/bff/biz";

const Bff_Api = {
  // ---------------- Bff_Biz ----------------
  getByCode(params: { code: string }): Promise<ServerResult<GetCodeItem[]>> {
    return request.get(`${Bff_Biz}/GetByCode`, { params });
  },
  /**
   * 获取基础配置
   * @param {*} params
   * @returns
   */
  getSettingList(params: any): Promise<ServerResult<any[]>> {
    return request.post(`${Bff_Biz}/getList`, params);
  },
  /**
   * 获取基础配置
   * @param {*} params
   * @returns
   */
  modifySetting(params: any): Promise<ServerResult<any>> {
    return request.post(`${Bff_Biz}/modify`, params);
  },
};

export default Bff_Api;
