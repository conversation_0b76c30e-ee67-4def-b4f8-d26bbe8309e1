import request from "@/utils/request";
import { type TestUserDTO } from "./types";
const PASSPORT_IDENTITY = "/passport/identity";
/**
 * 创建平台测试用户
 */
export const createTestUser = (data: any) => {
  return request.post(`${PASSPORT_IDENTITY}/CreateTestUser`, data);
};

/**
 * 获取平台测试用户
 */
export const getTestUsers = () => {
  return request.get<TestUserDTO[]>(`${PASSPORT_IDENTITY}/GetTestUsers`);
};

/**
 * 修改平台测试用户
 */
export const updateTestUser = (data: any) => {
  return request.post(`${PASSPORT_IDENTITY}/UpdateTestUser`, data);
};

/**
 * 删除平台测试用户
 * @param data 用户id数组
 */
export const removeTestUsers = (data: string[]) => {
  return request.post(`${PASSPORT_IDENTITY}/RemoveTestUsers`, data);
};
// /** 解锁屏幕 */
// export const checkPassword = (params: any) => {
//   return request.get(`${PASSPORT_IDENTITY}/CheckPassword`, {
//     params,
//   });
// };
