import request from "@/utils/request";
import {
  type SendExpressInputDTO,
  type ExpressCompany,
  type AftersaleRefund,
  type OrderAddressInputDTO,
  type OrderAftersaleRefundInputDTO,
  type OrderItem,
  type RefundOrderInputDTO,
  type RefundDepositInputDTO,
} from "./types";

const Order_Express = "/order/api/express";
const Order_Order = "/order/api/order";
const Order_ConsultOrder = "/order/consultOrder";

const Order_Api = {
  // ------------------------------ express ------------------------------

  /** 通过快递单号获取快递公司 */
  getExpressCompany(params: { nums: string }): Promise<ServerResult<ExpressCompany>> {
    return request.get(`${Order_Express}/GetExpressCompany`, { params });
  },

  /** 获取物流信息 */
  getExpressInfo(
    data: { ExpressNum?: string; Phone?: string }[]
  ): Promise<ServerResult<ExpressInfo[]>> {
    return request.post(`${Order_Express}/query`, data);
  },

  // ------------------------------ order ------------------------------

  /** 发送快递 */
  sendExpress(data: SendExpressInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Order_Order}/send`, data);
  },
  /** 修改快递 */
  editExpresss(data: SendExpressInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Order_Order}/editExpresss`, data);
  },
  /** 获取退款数据 */
  getOrderAftersaleRefundsList(
    data: OrderAftersaleRefundInputDTO
  ): Promise<ServerResult<ListDataTotalCount<AftersaleRefund>>> {
    return request.post(`${Order_Order}/GetOrderAftersaleRefundsList`, data);
  },
  /** 获取退款金额 */
  getOrderRefundsStatistics(
    data: OrderAftersaleRefundInputDTO
  ): Promise<ServerResult<{ TotalAmount: number }>> {
    return request.post(`${Order_Order}/GetOrderRefundsStatistics`, data);
  },
  /** 修改地址 */
  setOrderAddress(data: OrderAddressInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Order_Order}/SetOrderAddress`, data);
  },
  /** 通过订单编号查询退款订单 */
  getOrdersByOrderNo(params: { orderNo: string }): Promise<ServerResult<OrderItem>> {
    return request.get(`${Order_Order}/GetOrdersByOrderNo`, { params });
  },
  /** 治疗订单退款 */
  refundByOrderDetails(data: RefundOrderInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Order_Order}/RefundByOrderDetails`, data);
  },
  /** 押金退款 */
  refundDeposit(data: RefundDepositInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Order_Order}/Refunds`, data);
  },

  // ------------------------------ consultOrder ------------------------------

  /** 支付 */
  backendPayOrder(data: {
    OrderNo: string;
    BackendPayRemark: string;
  }): Promise<ServerResult<null>> {
    return request.post(`${Order_ConsultOrder}/BackendPayOrder`, data);
  },
};
export default Order_Api;
