import { type EpPropMergeTypeWithNull } from "element-plus";

/** 公共持有 */
export interface OrderAftersaleItem {
  Id: string;
  Status: number;
  Price: number;
  OrderDetailId: string;
  Reason: string;
  RefundOptUserId: string;
  RefundOptUserName: string;
  RefundTime: string;
}

export interface Payment {
  Id: string;
  PaymentName: string;
  Provider: string;
  PayAlias: string;
}

// --------------- 以下为私有类型 ---------------

export interface ExpressCompany {
  [props: string]: {
    ComCode: string;
    Name: string;
    LengthPre: number;
  };
}

export interface SendExpressInputDTO {
  PrescriptionId?: string;
  OrderExpresses: OrderExpress[];
}

export interface AftersaleRefund {
  CreatedTime: string;
  OrgName: string;
  CreatorName: string;
  UserName: string;
  Title: string;
  OrderNo: number;
  OrderNoStr: string;
  RefundPrice: number;
  RefundOptUserId: string;
  RefundOptUserName: string;
  RefundTime: string;
  Reason: string;
  IsTest: boolean;
}
export interface OrderAddressInputDTO {
  OrderNo: string;
  Name: string;
  Tel: string;
  ProvinceName: string;
  CityName: string;
  CountyName: string;
  Address: string;
  Province: string;
  City: string;
  County: string;
}
export interface OrderAftersaleRefundInputDTO {
  OrderType: string;
  OrderNo?: string;
  PageIndex: number;
  PageSize: number;
  IsTest?: boolean;
  OrgIds?: EpPropMergeTypeWithNull<string[]>;
  DeptIds?: EpPropMergeTypeWithNull<string[]>;
  RefundTimeEnd?: string;
  RefundTimeStart?: string;
  Keyword?: string;
  LoginUserId?: EpPropMergeTypeWithNull<string>;
}
/** 通过orderNo获取订单 */
export interface OrderItem {
  Status: number;
  UserName: string;
  Price: number;
  CreatedTime: string;
  DeliveryTime?: string;
  IsPayCompleted: boolean;
  OrderAddresss: unknown[];
  OrderDetails: OrderDetailItem[];
  OrderExpresses: OrderExpress[];
  OrderNo: string;
  PayTime: string;
  PayType: number;
  Payment: Payment;
  RemarkTags?: string[];
  OrderExtend?: {
    OrgName: string;
    CreatorName: string;
  };
  Remark?: string;
}
export interface OrderDetailItem {
  Id: string;
  OrderNo: number;
  OrderNoStr: string;
  GoodsType: string;
  RelationId: string;
  Title: string;
  Price: number;
  TotalPrice: number;
  OrderAftersales: OrderAftersaleItem[];
}
export interface RefundOrderInputDTO {
  OrderDetailId: string;
  Price: number;
  Reason: string;
}
export interface RefundDepositInputDTO {
  OrderNo: string;
  OrderAftersaleId: string;
  Price: number;
  Reason?: string;
}
