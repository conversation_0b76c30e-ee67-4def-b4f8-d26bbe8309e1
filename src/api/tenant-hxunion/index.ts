import request from "@/utils/request";

const baseUrl = "/tenant-hxunion";
const gaugePath = `${baseUrl}/api/gauge`;

const TenantHxunion_Api = {
  // --------------- gauge ---------------

  /**
   * 获取基础量表列表（平台）
   *
   * @param query.OrgId 机构id
   */
  getPage(query: {
    PageIndex?: number;
    PageSize?: number;
    OrgId?: string;
  }): Promise<ServerResult<ListDataTotalCount<RiskWarningGauge>>> {
    const path = `${gaugePath}/GetPage`;
    return request.post(path, query);
  },

  /**
   * 获取量表详情（基础数据）
   *
   * @param id 量表基础数据id
   */
  getGaugeDetailById(id: string): Promise<ServerResult<RiskWarningGauge>> {
    const path = `${gaugePath}/GetGaugeDetailById`;
    return request.get(path, { params: { id } });
  },

  /**
   * 更新/创建量表
   *
   * @param gauge 量表
   */
  insertOrUpdate(gauge: RiskWarningGauge): Promise<ServerResult> {
    const path = `${gaugePath}/InsertOrUpdate`;
    return request.post(path, gauge);
  },

  /**
   * 引用量表
   */
  referenceGauge(query: { GaugeId: string; OrgIds: string[] }): Promise<ServerResult> {
    const path = `${gaugePath}/ReferenceGauge`;
    return request.post(path, query);
  },

  /**
   * 推送量表
   */
  pushGauge(gaugeId: string): Promise<ServerResult> {
    const path = `${gaugePath}/PushGauge`;
    return request.post(path, { Id: gaugeId });
  },
};

export default TenantHxunion_Api;
