{"Vue3.5+TreeTableLayout": {"scope": "vue", "prefix": "Vue3.5+TreeTable", "body": ["<template>", "  <el-container class=\"w-full h-full\">", "    <el-aside width=\"230px\" class=\"p-10px\">", "      <el-tree", "        class=\"w-full h-full p-10px overflow-auto\"", "        :data=\"leftTreeData\"", "        :loading=\"treeLoading\"", "        :props=\"defaultProps\"", "        default-expand-all", "        highlight-current", "        @node-click=\"handleNodeClick\"", "        @node-contextmenu=\"handleNodeRightClick\"", "      />", "      <div v-show=\"leftMenuShow.isShow\">", "        <ul", "          id=\"menu\"", "          class=\"menu\"", "          :style=\"'top:' + menuPosition.clientY + 'px;left:' + menuPosition.clientX + 'px;'\"", "        >", "          <li", "            v-if=\"leftMenuShow.showAdd\"", "            class=\"menu_item\"", "            @click=\"showDialog.consumableType = true\"", "          >", "            添加", "          </li>", "          <li", "            v-if=\"leftMenuShow.showEdit\"", "            class=\"menu_item\"", "            @click=\"showDialog.consumableType = true\"", "          >", "            编辑", "          </li>", "        </ul>", "      </div>", "    </el-aside>", "    <el-main class=\"p-10px\">", "      <BaseTableSearchContainer @size-changed=\"tableResize\">", "        <template #search>", "          <TBSearchContainer :is-show-toggle=\"true\">", "            <template #left>", "              <el-form :model=\"queryParams\" label-position=\"right\" :inline=\"true\">", "                <el-form-item label=\"时间\">", "                  <el-date-picker", "                    v-model=\"timeRange\"", "                    type=\"daterange\"", "                    range-separator=\"至\"", "                    start-placeholder=\"开始日期\"", "                    end-placeholder=\"结束日期\"", "                    format=\"YYYY-MM-DD\"", "                    :clearable=\"false\"", "                    value-format=\"YYYY-MM-DD\"", "                    style=\"width: 250px\"", "                  />", "                </el-form-item>", "                <el-form-item label=\"机构\">", "                  <HospitalSelect", "                    v-model=\"queryParams.sourceOrganizationNameKeyword\"", "                    keyId=\"Name\"", "                  />", "                </el-form-item>", "                <el-form-item label=\"是否测试数据\">", "                  <el-select", "                    v-model=\"queryParams.isTest\"", "                    placeholder=\"请选择\"", "                    clearable", "                    :empty-values=\"[null, undefined, '']\"", "                    :value-on-clear=\"() => null\"", "                    style=\"width: 80px\"", "                  >", "                    <el-option label=\"是\" value=\"是\" />", "                    <el-option label=\"否\" value=\"否\" />", "                  </el-select>", "                </el-form-item>", "                <el-form-item label=\"关键字\" prop=\"keyword\">", "                  <el-input v-model=\"queryParams.keyword\" placeholder=\"姓名/电话号码\" />", "                </el-form-item>", "              </el-form>", "            </template>", "            <template #right>", "              <el-button type=\"primary\" icon=\"search\" @click=\"handleQuery\">搜索</el-button>", "            </template>", "          </TBSearchContainer>", "        </template>", "        <template #table>", "          <el-table", "            ref=\"tableRef\"", "            v-loading=\"tableLoading\"", "            :data=\"pageData\"", "            :total=\"total\"", "            border", "            row-key=\"Id\"", "            :height=\"tableFluidHeight\"", "            highlight-current-row", "            style=\"text-align: center; flex: 1\"", "          >", "            <el-table-column prop=\"Name\" label=\"姓名\" align=\"center\" />", "            <el-table-column prop=\"Sex\" label=\"性别\" align=\"center\" />", "            <el-table-column prop=\"Age\" label=\"年龄\" align=\"center\" />", "            <el-table-column prop=\"HasVisitedRegister\" label=\"是否报道\" align=\"center\" />", "            <el-table-column prop=\"ConsultFinishCount\" label=\"问诊次数\" align=\"center\" />", "            <el-table-column prop=\"TreatFinishCount\" label=\"咨询次数\" align=\"center\" />", "            <el-table-column prop=\"PhoneNumber\" label=\"注册电话\" align=\"center\" width=\"180\" />", "", "            <el-table-column label=\"医生/治疗师\" prop=\"inviterUser\" align=\"center\">", "              <template #default=\"scope\">", "                {{ scope.row.inviterUser }}", "              </template>", "            </el-table-column>", "            <el-table-column fixed=\"right\" label=\"操作\" width=\"150\" align=\"center\">", "              <template #default=\"scope\">", "                <el-button link type=\"primary\" @click=\"handlePreviewOrEdit(scope.row, true)\">", "                  查看", "                </el-button>", "                <el-button", "                  v-hasNoPermission=\"['promoter']\"", "                  link", "                  type=\"primary\"", "                  @click=\"handlePreviewOrEdit(scope.row, false)\"", "                >", "                  编辑", "                </el-button>", "              </template>", "            </el-table-column>", "          </el-table>", "        </template>", "        <template #pagination>", "          <Pagination", "            v-if=\"total > 0\"", "            v-model:total=\"total\"", "            v-model:page=\"queryParams.PageIndex\"", "            v-model:limit=\"queryParams.PageSize\"", "            @pagination=\"handleGetTableList\"", "          />", "        </template>", "      </BaseTableSearchContainer>", "    </el-main>", "  </el-container>", "</template>", "", "<script setup lang=\"ts\">", "import dayjs from \"dayjs\";", "import { useTableConfig } from \"@/hooks/useTableConfig\";", "", "interface PageDialogShow {", "  consumableType: boolean;", "  consumable: boolean;", "}", "const leftTreeData = ref([]);", "const treeLoading = ref(false);", "const leftMenuShow = ref({", "  isShow: false,", "  showAdd: false,", "  showEdit: false,", "});", "const menuPosition = ref({", "  clientX: 0,", "  clientY: 0,", "});", "const defaultProps = {};", "const showDialog = ref<PageDialogShow>({", "  consumableType: false,", "  consumable: false,", "});", "", "defineOptions({", "  name: \"\",", "});", "", "const queryParams = ref<any>({});", "const timeRange = ref<[string, string]>([", "  dayjs().format(\"YYYY-MM-01\"),", "  dayjs().format(\"YYYY-MM-DD\"),", "]);", "", "const isPreview = ref<boolean>(false);", "provide(\"isPreview\", isPreview);", "", "const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =", "  useTableConfig<unknown>();", "", "const handleQuery = () => {", "  queryParams.value.PageIndex = 1;", "  handleGetTableList();", "};", "", "const handlePreviewOrEdit = async (row: BaseOrganization | null, isPreviewState: boolean) => {", "  isPreview.value = row ? isPreviewState : false;", "};", "const handleNodeClick = () => {};", "const handleNodeRightClick = () => {};", "", "const handleGetTableList = () => {};", "const handleGetLeftTreeData = () => {};", "", "watch(timeRange, (newVal) => {", "  queryParams.value.QueryStartDate = dayjs(newVal[0]).format(\"YYYY-MM-DD 00:00:00\");", "  queryParams.value.QueryEndDate = dayjs(newVal[1]).format(\"YYYY-MM-DD 23:59:59\");", "});", "", "onMounted(() => {", "  handleGetLeftTreeData();", "});", "onActivated(() => {", "  handleGetTableList();", "});", "</script>", "<style scoped lang=\"scss\"></style>", ""], "description": "Vue3.5+TreeTableLayout"}}