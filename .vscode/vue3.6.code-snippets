{"Vue3.5+TreeTableLayout": {"scope": "vue", "prefix": "Vue3.6+<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ["<template>", "  <div>test</div>", "</template>", "", "<script setup lang=\"ts\">", "", "const handleSubmit = () => {};", "", "const handleProcessingData = (info: any) => {};", "", "interface Props {", "  detailInfo: any | null;", "}", "const props = defineProps<Props>();", "", "watch(", "  () => props.detailInfo,", "  (newVal) => {", "    if (newVal) {", "      handleProcessingData(newVal);", "    }", "  },", "  { immediate: true }", ");", "", "defineExpose({", "  handleSubmit,", "});", "</script>", "", "<style lang=\"scss\" scoped></style>"], "description": "Vue3.6+<PERSON><PERSON><PERSON><PERSON><PERSON>"}}