## 环境准备

| 环境                 | 名称版本                                                     | 下载地址                                                     |
| -------------------- | :----------------------------------------------------------- | ------------------------------------------------------------ |
| **开发工具**         | VSCode    | [下载](https://code.visualstudio.com/Download)           |
| **运行环境**         | Node ≥18 (其中 20.6.0 版本不可用)    | [下载](http://nodejs.cn/download)                        |


## 项目启动

```bash

# 安装 pnpm
npm install pnpm -g

# 设置镜像源(可忽略)
pnpm config set registry https://registry.npmmirror.com

# 安装依赖
pnpm install

# 启动运行
pnpm run dev
```



## 项目部署

```bash
# 项目打包
pnpm run build

# 上传文件至远程服务器
将本地打包生成的 dist 目录下的所有文件拷贝至服务器的 /usr/share/nginx/html 目录。

# nginx.cofig 配置
server {
	listen     80;
	server_name  localhost;
	location / {
			root /usr/share/nginx/html;
			index index.html index.htm;
	}
	# 反向代理配置
	location /prod-api/ {
      # api.youlai.tech 替换后端API地址，注意保留后面的斜杠 /
      proxy_pass http://api.youlai.tech/;
	}
}
```

## 注意事项

- **自动导入插件自动生成默认关闭**

  模板项目的组件类型声明已自动生成。如果添加和使用新的组件，请按照图示方法开启自动生成。在自动生成完成后，记得将其设置为 `false`，避免重复执行引发冲突。

  ![](https://foruda.gitee.com/images/1687755823137387608/412ea803_716974.png)

- **项目启动浏览器访问空白**

  请升级浏览器尝试，低版本浏览器内核可能不支持某些新的 JavaScript 语法，比如可选链操作符 `?.`。

- **项目同步仓库更新升级**

  项目同步仓库更新升级之后，建议 `pnpm install` 安装更新依赖之后启动 。

- **项目组件、函数和引用爆红**

	重启 VSCode 尝试

## 项目注意事项

- 所有的el-dialog请设置具体的固定宽度，并且添加destroy-on-close字段

```vue
<el-dialog
  v-model="dialog.show"
  :title="dialog.title"
  width="800px"
  destroy-on-close
  @close="dialog.show = false"
>
  <Component/>
  <template #footer>
    <div class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button
        v-if="!isPreview"
        type="primary"
        :loading="dialogConfirmLoading"
        @click="handleSubmit"
      >
        确定
      </el-button>
    </div>
  </template>
</el-dialog>
```

- 所有的列表数据（编辑、新增里面的表格不用），在关键字输入可以通过回车键进行搜索。如果可以请都使用handleQuery方法，后续可以进行抽离

  ```vue
  <el-form-item label="关键字" prop="Key">
    <el-input
      v-model="queryParams.Key"
      placeholder="名称/编码/拼音码"
      @keyup.enter="handleQuery"
    />
  </el-form-item>
  ```

  
